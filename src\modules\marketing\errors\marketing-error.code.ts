import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi liên quan đến module Marketing (13000-13099)
 */
export const MARKETING_ERROR_CODES = {
  // ===== TRƯỜNG TÙY CHỈNH (13000-13009) =====
  /**
   * Lỗi khi không tìm thấy trường tùy chỉnh
   */
  CUSTOM_FIELD_NOT_FOUND: new ErrorCode(
    13000,
    'Không tìm thấy trường tùy chỉnh',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi trường tùy chỉnh đã tồn tại
   */
  CUSTOM_FIELD_ALREADY_EXISTS: new ErrorCode(
    13001,
    'Trường tùy chỉnh đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi tạo trường tùy chỉnh thất bại
   */
  CUSTOM_FIELD_CREATION_FAILED: new ErrorCode(
    13002,
    'Tạo trường tùy chỉnh thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật trường tùy chỉnh thất bại
   */
  CUSTOM_FIELD_UPDATE_FAILED: new ErrorCode(
    13003,
    'Cập nhật trường tùy chỉnh thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa trường tùy chỉnh thất bại
   */
  CUSTOM_FIELD_DELETION_FAILED: new ErrorCode(
    13004,
    'Xóa trường tùy chỉnh thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa trường tùy chỉnh thất bại
   */
  CUSTOM_FIELD_DELETE_FAILED: new ErrorCode(
    13005,
    'Xóa trường tùy chỉnh thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi validation trường tùy chỉnh thất bại
   */
  CUSTOM_FIELD_VALIDATION_FAILED: new ErrorCode(
    13006,
    'Validation trường tùy chỉnh thất bại',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi truy vấn trường tùy chỉnh thất bại
   */
  CUSTOM_FIELD_QUERY_FAILED: new ErrorCode(
    13007,
    'Truy vấn trường tùy chỉnh thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== AUDIENCE (13010-13019) =====
  /**
   * Lỗi khi không tìm thấy audience
   */
  AUDIENCE_NOT_FOUND: new ErrorCode(
    13010,
    'Không tìm thấy khách hàng',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tạo audience thất bại
   */
  AUDIENCE_CREATION_FAILED: new ErrorCode(
    13011,
    'Tạo khách hàng thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật audience thất bại
   */
  AUDIENCE_UPDATE_FAILED: new ErrorCode(
    13012,
    'Cập nhật khách hàng thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa audience thất bại
   */
  AUDIENCE_DELETION_FAILED: new ErrorCode(
    13013,
    'Xóa khách hàng thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi email audience đã tồn tại
   */
  AUDIENCE_EMAIL_ALREADY_EXISTS: new ErrorCode(
    13014,
    'Email khách hàng đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi số điện thoại audience đã tồn tại
   */
  AUDIENCE_PHONE_ALREADY_EXISTS: new ErrorCode(
    13015,
    'Số điện thoại khách hàng đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  // ===== TAG (13020-13029) =====
  /**
   * Lỗi khi không tìm thấy tag
   */
  TAG_NOT_FOUND: new ErrorCode(
    13020,
    'Không tìm thấy tag',
    HttpStatus.NOT_FOUND,
  ),

  // ===== SEGMENT (13030-13039) =====
  /**
   * Lỗi khi không tìm thấy segment
   */
  SEGMENT_NOT_FOUND: new ErrorCode(
    13030,
    'Không tìm thấy phân khúc',
    HttpStatus.NOT_FOUND,
  ),

  // ===== CAMPAIGN (13040-13049) =====
  /**
   * Lỗi khi không tìm thấy campaign
   */
  CAMPAIGN_NOT_FOUND: new ErrorCode(
    13040,
    'Không tìm thấy chiến dịch',
    HttpStatus.NOT_FOUND,
  ),

  // ===== TEMPLATE (13050-13059) =====
  /**
   * Lỗi khi không tìm thấy template
   */
  TEMPLATE_NOT_FOUND: new ErrorCode(
    13050,
    'Không tìm thấy mẫu',
    HttpStatus.NOT_FOUND,
  ),

  // ===== ZALO ZNS (13060-13079) =====
  /**
   * Lỗi khi Official Account chưa có access token
   */
  ZNS_UNAUTHORIZED: new ErrorCode(
    13060,
    'Official Account chưa có access token',
    HttpStatus.UNAUTHORIZED,
  ),

  /**
   * Lỗi khi không tìm thấy template ZNS
   */
  ZNS_TEMPLATE_NOT_FOUND: new ErrorCode(
    13061,
    'Không tìm thấy template ZNS',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi template ZNS chưa được phê duyệt
   */
  ZNS_TEMPLATE_NOT_APPROVED: new ErrorCode(
    13062,
    'Template ZNS chưa được phê duyệt',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi không tìm thấy tin nhắn ZNS
   */
  ZNS_MESSAGE_NOT_FOUND: new ErrorCode(
    13063,
    'Không tìm thấy tin nhắn ZNS',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi gọi Zalo ZNS API thất bại
   */
  ZNS_API_ERROR: new ErrorCode(
    13064,
    'Lỗi khi gọi Zalo ZNS API',
    HttpStatus.BAD_GATEWAY,
  ),

  /**
   * Lỗi khi lấy danh sách template ZNS thất bại
   */
  ZNS_GET_TEMPLATES_FAILED: new ErrorCode(
    13065,
    'Không thể lấy danh sách template ZNS',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi đăng ký template ZNS thất bại
   */
  ZNS_REGISTER_TEMPLATE_FAILED: new ErrorCode(
    13066,
    'Không thể đăng ký template ZNS',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi gửi tin nhắn ZNS thất bại
   */
  ZNS_SEND_MESSAGE_FAILED: new ErrorCode(
    13067,
    'Không thể gửi tin nhắn ZNS',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật trạng thái template ZNS thất bại
   */
  ZNS_UPDATE_TEMPLATE_STATUS_FAILED: new ErrorCode(
    13068,
    'Không thể cập nhật trạng thái template ZNS',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy lịch sử tin nhắn ZNS thất bại
   */
  ZNS_GET_MESSAGES_FAILED: new ErrorCode(
    13069,
    'Không thể lấy lịch sử tin nhắn ZNS',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== ZNS CAMPAIGN (13070-13089) =====
  /**
   * Lỗi khi không tìm thấy chiến dịch ZNS
   */
  ZNS_CAMPAIGN_NOT_FOUND: new ErrorCode(
    13070,
    'Không tìm thấy chiến dịch ZNS',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tạo chiến dịch ZNS thất bại
   */
  ZNS_CAMPAIGN_CREATION_FAILED: new ErrorCode(
    13071,
    'Không thể tạo chiến dịch ZNS',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật chiến dịch ZNS thất bại
   */
  ZNS_CAMPAIGN_UPDATE_FAILED: new ErrorCode(
    13072,
    'Không thể cập nhật chiến dịch ZNS',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa chiến dịch ZNS thất bại
   */
  ZNS_CAMPAIGN_DELETE_FAILED: new ErrorCode(
    13073,
    'Không thể xóa chiến dịch ZNS',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi chiến dịch ZNS không ở trạng thái có thể cập nhật
   */
  ZNS_CAMPAIGN_INVALID_STATUS_FOR_UPDATE: new ErrorCode(
    13074,
    'Không thể cập nhật chiến dịch đang chạy hoặc đã hoàn thành',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi chiến dịch ZNS không ở trạng thái có thể xóa
   */
  ZNS_CAMPAIGN_INVALID_STATUS_FOR_DELETE: new ErrorCode(
    13075,
    'Không thể xóa chiến dịch đang chạy hoặc đã lên lịch',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi chiến dịch ZNS không ở trạng thái có thể chạy
   */
  ZNS_CAMPAIGN_INVALID_STATUS_FOR_RUN: new ErrorCode(
    13076,
    'Chiến dịch không ở trạng thái có thể chạy',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi chiến dịch ZNS không ở trạng thái có thể hủy
   */
  ZNS_CAMPAIGN_INVALID_STATUS_FOR_CANCEL: new ErrorCode(
    13077,
    'Không thể hủy chiến dịch ở trạng thái hiện tại',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi chạy chiến dịch ZNS thất bại
   */
  ZNS_CAMPAIGN_RUN_FAILED: new ErrorCode(
    13078,
    'Không thể chạy chiến dịch ZNS',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi hủy chiến dịch ZNS thất bại
   */
  ZNS_CAMPAIGN_CANCEL_FAILED: new ErrorCode(
    13079,
    'Không thể hủy chiến dịch ZNS',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi tạo job ZNS thất bại
   */
  ZNS_JOB_CREATION_FAILED: new ErrorCode(
    13080,
    'Không thể tạo job gửi ZNS',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy danh sách chiến dịch ZNS thất bại
   */
  ZNS_CAMPAIGN_GET_LIST_FAILED: new ErrorCode(
    13081,
    'Không thể lấy danh sách chiến dịch ZNS',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};
