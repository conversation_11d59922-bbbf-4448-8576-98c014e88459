import { Injectable, Logger } from '@nestjs/common';
import { Observable, Subject } from 'rxjs';
import { MediaRepository } from '../../repositories';
import { RagFileProcessingService } from '@shared/services/ai/rag-file-processing.service';
import { AppException } from '@common/exceptions/app.exception';
import { MEDIA_ERROR_CODES } from '../../exception';
import { MediaTrackingResponseDto } from '../../dto/media-tracking-response.dto';

/**
 * Service xử lý tracking tiến độ xử lý media cho user
 */
@Injectable()
export class MediaTrackingUserService {
  private readonly logger = new Logger(MediaTrackingUserService.name);

  constructor(
    private readonly mediaRepository: MediaRepository,
    private readonly ragFileProcessingService: RagFileProcessingService,
  ) {}

  /**
   * Theo dõi tiến độ xử lý media từ RAG API
   * @param mediaId ID của media cần theo dõi (media_id từ RAG API)
   * @param userId ID của người dùng
   * @returns Observable stream của tracking events
   */
  async trackMediaProgress(
    mediaId: string,
    userId: number,
  ): Promise<Observable<MediaTrackingResponseDto>> {
    try {
      this.logger.log(`Bắt đầu tracking media ${mediaId} cho user ${userId}`);

      // Kiểm tra media có tồn tại và thuộc về user không
      const media = await this.mediaRepository.findOne({
        where: {
          mediaId: mediaId,
          ownedBy: userId,
        },
      });

      if (!media) {
        throw new AppException(
          MEDIA_ERROR_CODES.NOT_FOUND,
          'Media không tồn tại hoặc bạn không có quyền truy cập',
        );
      }

      // Tạo Subject để stream events
      const eventSubject = new Subject<MediaTrackingResponseDto>();

      // Bắt đầu tracking từ RAG API
      this.startRagTracking(mediaId, eventSubject);

      return eventSubject.asObservable();
    } catch (error) {
      this.logger.error(
        `Error starting tracking for media ${mediaId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Bắt đầu tracking từ RAG API
   */
  private async startRagTracking(
    mediaId: string,
    eventSubject: Subject<MediaTrackingResponseDto>,
  ): Promise<void> {
    try {
      // Gọi RAG API để tracking (sử dụng media_id thay vì file_id)
      const ragTrackingStream = await this.ragFileProcessingService.trackMediaProgress(mediaId);

      ragTrackingStream.subscribe({
        next: async (ragEvent) => {
          try {
            // Chuyển đổi event từ RAG API
            const trackingEvent = this.transformRagEvent(ragEvent, mediaId);

            // Emit event
            eventSubject.next(trackingEvent);

            // Đóng stream khi hoàn thành hoặc lỗi
            if (['completed', 'error', 'timeout'].includes(trackingEvent.status)) {
              eventSubject.complete();
            }
          } catch (error) {
            this.logger.error(`Error processing RAG event: ${error.message}`);
            eventSubject.error(error);
          }
        },
        error: (error) => {
          this.logger.error(`RAG tracking error: ${error.message}`);
          eventSubject.error(error);
        },
        complete: () => {
          this.logger.log(`RAG tracking completed for media ${mediaId}`);
          eventSubject.complete();
        },
      });
    } catch (error) {
      this.logger.error(`Error starting RAG tracking: ${error.message}`);
      eventSubject.error(error);
    }
  }

  /**
   * Chuyển đổi event từ RAG API thành MediaTrackingResponseDto
   */
  private transformRagEvent(ragEvent: any, mediaId: string): MediaTrackingResponseDto {
    return {
      media_id: mediaId,
      progress: ragEvent.progress || 0,
      status: this.mapRagStatus(ragEvent.status),
      message: ragEvent.message || 'Đang xử lý media',
      timestamp: Date.now(),
      metadata: ragEvent.metadata || {},
    };
  }

  /**
   * Map trạng thái từ RAG API sang trạng thái media tracking
   */
  private mapRagStatus(ragStatus: string): 'pending' | 'processing' | 'completed' | 'error' | 'timeout' {
    switch (ragStatus) {
      case 'pending':
        return 'pending';
      case 'processing':
        return 'processing';
      case 'completed':
        return 'completed';
      case 'failed':
      case 'error':
        return 'error';
      case 'timeout':
        return 'timeout';
      default:
        return 'processing';
    }
  }
}
