import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { CustomFieldConfigDto, OptionDto } from './custom-field-config.dto';
import { IsNumber, IsOptional, IsString, IsArray } from 'class-validator';
import { CustomFieldConfigJson } from '@/modules/business/types';

/**
 * Enum cho trạng thái của trường tùy chỉnh
 */
export enum CustomFieldStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  DELETED = 'DELETED'
}

/**
 * Mapper function để convert CustomFieldConfigJson sang CustomFieldConfigDto
 */
export function mapConfigJsonToDto(configJson: CustomFieldConfigJson): CustomFieldConfigDto {
  const dto = new CustomFieldConfigDto();

  if (typeof configJson === 'object' && configJson !== null) {
    // Copy basic properties
    dto.placeholder = (configJson as any).placeholder;
    dto.maxLength = (configJson as any).maxLength;
    dto.description = (configJson as any).description;
    dto.defaultValue = (configJson as any).defaultValue;
    dto.validation = (configJson as any).validation;
    dto.additionalProperties = (configJson as any).additionalProperties;

    // Handle options - convert SelectOption[] to OptionDto[]
    if ((configJson as any).options && Array.isArray((configJson as any).options)) {
      dto.options = (configJson as any).options.map((option: any) => {
        const optionDto = new OptionDto();
        // Ensure label is always a string (fallback to value if label is undefined)
        optionDto.label = option.label || option.title || String(option.value) || '';
        optionDto.value = option.value;
        return optionDto;
      });
    }
  }

  return dto;
}

/**
 * DTO cho response trả về thông tin trường tùy chỉnh
 */
export class CustomFieldResponseDto {
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 1,
  })
  @IsNumber()
  id: number;



  @ApiProperty({
    description: 'ID cấu hình',
    example: 'product_color',
  })
  @IsString()
  configId: string;

  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Màu sắc',
  })
  @IsString()
  label: string;

  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: 'Cấu hình chi tiết',
    type: () => CustomFieldConfigDto
  })
  @Type(() => CustomFieldConfigDto)
  configJson: CustomFieldConfigDto;

  /**
   * Static method để tạo CustomFieldResponseDto từ entity data
   */
  static fromEntity(entityData: {
    id: number;
    configId: string;
    label: string;
    type: string;
    configJson: CustomFieldConfigJson;
    employeeId: number | null;
    userId: number | null;
    createAt: number;
    status: CustomFieldStatus;
  }): CustomFieldResponseDto {
    const dto = new CustomFieldResponseDto();
    dto.id = entityData.id;
    dto.configId = entityData.configId;
    dto.label = entityData.label;
    dto.type = entityData.type;
    dto.configJson = mapConfigJsonToDto(entityData.configJson);
    dto.employeeId = entityData.employeeId;
    dto.userId = entityData.userId;
    dto.createAt = entityData.createAt;
    dto.status = entityData.status;
    return dto;
  }

  @ApiProperty({
    description: 'ID nhân viên tạo',
    example: 1,
    nullable: true,
  })
  @IsOptional()
  @IsNumber()
  employeeId: number | null;

  @ApiProperty({
    description: 'ID người dùng tạo',
    example: 1,
    nullable: true,
  })
  @IsOptional()
  @IsNumber()
  userId: number | null;

  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1625097600000,
  })
  @IsNumber()
  createAt: number;

  @ApiProperty({
    description: 'Trạng thái của trường tùy chỉnh',
    example: CustomFieldStatus.APPROVED,
    enum: CustomFieldStatus,
  })
  @IsString()
  status: CustomFieldStatus;
}

/**
 * DTO cho trường tùy chỉnh với giá trị
 */
export class CustomFieldWithValueResponseDto {
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 1,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Component type',
    example: 'input',
  })
  @IsString()
  component: string;

  @ApiProperty({
    description: 'ID cấu hình',
    example: 'product_color',
  })
  @IsString()
  configId: string;

  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Màu sắc',
  })
  @IsString()
  label: string;

  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: 'Bắt buộc hay không',
    example: true,
  })
  required: boolean;

  @ApiProperty({
    description: 'Cấu hình JSON',
    example: { placeholder: 'Nhập màu sắc' },
  })
  configJson: any;

  @ApiProperty({
    description: 'ID nhân viên tạo',
    example: 1,
    nullable: true,
  })
  @IsOptional()
  @IsNumber()
  employeeId: number | null;

  @ApiProperty({
    description: 'ID người dùng tạo',
    example: 1,
    nullable: true,
  })
  @IsOptional()
  @IsNumber()
  userId: number | null;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: 1625097600000,
  })
  @IsNumber()
  createAt: number;

  @ApiProperty({
    description: 'Trạng thái',
    enum: CustomFieldStatus,
    example: CustomFieldStatus.APPROVED,
  })
  status: CustomFieldStatus;

  @ApiProperty({
    description: 'Giá trị của trường',
    example: { value: 'Đỏ' },
  })
  value: any;
}

/**
 * DTO cho nhóm trường tùy chỉnh
 */
export class CustomGroupFormResponseDto {
  @ApiProperty({
    description: 'ID nhóm',
    example: 1,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Nhãn nhóm',
    example: 'Thông tin chi tiết',
  })
  @IsString()
  label: string;

  @ApiProperty({
    description: 'Danh sách trường trong nhóm',
    type: [CustomFieldWithValueResponseDto],
  })
  @IsArray()
  @Type(() => CustomFieldWithValueResponseDto)
  fields: CustomFieldWithValueResponseDto[];
}
