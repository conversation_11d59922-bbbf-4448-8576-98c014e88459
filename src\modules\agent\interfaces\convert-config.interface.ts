/**
 * Interface cho cấu hình chuyển đổi dữ liệu (legacy)
 */
export interface ConvertConfig {
  /**
   * Tên của field trong schema JSON
   */
  name: string;

  /**
   * Kiểu dữ liệu của field
   */
  type: 'string' | 'number' | 'boolean' | 'array_number' | 'array_string' | "enum";

  /**
   * <PERSON><PERSON> tả (nội dung) của field
   */
  description: string;

  /**
   * Trường này có bắt buộc không?
   */
  required: boolean;

  /**
   * Giá trị mặc định
   */
  defaultValue?: any;
}

/**
 * Interface cho cấu hình conversion mới
 */
export interface ConversionConfig {
  /**
   * Tên của field trong schema JSON
   */
  name: string;

  /**
   * Kiểu dữ liệu của field
   */
  type: 'string' | 'number' | 'boolean' | 'array_number' | 'array_string' | "enum";

  /**
   * <PERSON><PERSON> tả (nội dung) của field
   */
  description: string;

  /**
   * Trường này có bắt buộc không?
   */
  required: boolean;

  /**
   * Trạng thái hoạt động của field
   */
  active: boolean;
}