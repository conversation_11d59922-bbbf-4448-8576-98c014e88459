import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PhysicalProduct } from '../entities/physical-product.entity';

/**
 * Repository cho PhysicalProduct entity
 * Xử lý các thao tác database cho sản phẩm vật lý
 */
@Injectable()
export class PhysicalProductRepository {
  private readonly logger = new Logger(PhysicalProductRepository.name);

  constructor(
    @InjectRepository(PhysicalProduct)
    private readonly repository: Repository<PhysicalProduct>,
  ) {}

  /**
   * Tạo sản phẩm vật lý mới
   * @param data Dữ liệu sản phẩm vật lý
   * @returns Sản phẩm vật lý đã tạo
   */
  async create(data: Partial<PhysicalProduct>): Promise<PhysicalProduct> {
    const physicalProduct = this.repository.create(data);
    return this.repository.save(physicalProduct);
  }

  /**
   * Tìm sản phẩm vật lý theo ID
   * @param id ID sản phẩm vật lý (cũng là customer product ID)
   * @returns Sản phẩm vật lý hoặc null
   */
  async findById(id: number): Promise<PhysicalProduct | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Cập nhật sản phẩm vật lý
   * @param id ID sản phẩm vật lý
   * @param data Dữ liệu cập nhật
   * @returns Sản phẩm vật lý đã cập nhật
   */
  async update(id: number, data: Partial<PhysicalProduct>): Promise<PhysicalProduct | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa sản phẩm vật lý
   * @param id ID sản phẩm vật lý
   */
  async delete(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Tìm sản phẩm vật lý với variants
   * @param id ID sản phẩm vật lý
   * @returns Sản phẩm vật lý với variants
   */
  async findWithVariants(id: number): Promise<PhysicalProduct | null> {
    return this.repository.findOne({
      where: { id },
      relations: ['variants'],
    });
  }

  /**
   * Tìm sản phẩm vật lý với images
   * @param id ID sản phẩm vật lý
   * @returns Sản phẩm vật lý với images
   */
  async findWithImages(id: number): Promise<PhysicalProduct | null> {
    return this.repository.findOne({
      where: { id },
      relations: ['images'],
    });
  }

  /**
   * Tìm sản phẩm vật lý với tất cả relations
   * @param id ID sản phẩm vật lý
   * @returns Sản phẩm vật lý với tất cả relations
   */
  async findWithAllRelations(id: number): Promise<PhysicalProduct | null> {
    return this.repository.findOne({
      where: { id },
      relations: ['variants', 'images'],
    });
  }

  /**
   * Kiểm tra SKU có tồn tại không
   * @param sku Mã SKU
   * @param excludeId ID sản phẩm cần loại trừ
   * @returns true nếu SKU đã tồn tại
   */
  async existsBySku(sku: string, excludeId?: number): Promise<boolean> {
    const queryBuilder = this.repository.createQueryBuilder('physical');
    queryBuilder.where('physical.sku = :sku', { sku });
    
    if (excludeId) {
      queryBuilder.andWhere('physical.id != :excludeId', { excludeId });
    }

    const count = await queryBuilder.getCount();
    return count > 0;
  }

  /**
   * Kiểm tra barcode có tồn tại không
   * @param barcode Mã vạch
   * @param excludeId ID sản phẩm cần loại trừ
   * @returns true nếu barcode đã tồn tại
   */
  async existsByBarcode(barcode: string, excludeId?: number): Promise<boolean> {
    const queryBuilder = this.repository.createQueryBuilder('physical');
    queryBuilder.where('physical.barcode = :barcode', { barcode });
    
    if (excludeId) {
      queryBuilder.andWhere('physical.id != :excludeId', { excludeId });
    }

    const count = await queryBuilder.getCount();
    return count > 0;
  }

  /**
   * Lưu sản phẩm vật lý
   * @param physicalProduct Sản phẩm vật lý cần lưu
   * @returns Sản phẩm vật lý đã lưu
   */
  async save(physicalProduct: PhysicalProduct): Promise<PhysicalProduct> {
    return this.repository.save(physicalProduct);
  }

  /**
   * Tìm tất cả sản phẩm vật lý
   * @returns Danh sách sản phẩm vật lý
   */
  async findAll(): Promise<PhysicalProduct[]> {
    return this.repository.find();
  }

  /**
   * Đếm số lượng sản phẩm vật lý
   * @returns Số lượng sản phẩm vật lý
   */
  async count(): Promise<number> {
    return this.repository.count();
  }
}
