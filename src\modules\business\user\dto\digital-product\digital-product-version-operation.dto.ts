import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsNumber,
  IsObject,
  IsArray,
  ValidateNested,
  ValidateIf,
  MaxLength,
  Min,
  IsUrl,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ProductImageOperationDto } from '../image-operations/image-operation.dto';

/**
 * Enum cho các loại thao tác phiên bản sản phẩm số
 */
export enum VersionOperationType {
  ADD = 'add',
  UPDATE = 'update',
  DELETE = 'delete',
}

/**
 * DTO cho dữ liệu phiên bản trong thao tác
 */
export class VersionOperationDataDto {
  @ApiProperty({
    description: 'Tên phiên bản sản phẩm số',
    example: 'Bản tiêu chuẩn',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  versionName: string;

  @ApiProperty({
    description: '<PERSON><PERSON> tả chi tiết về phiên bản',
    example: '<PERSON>ên bản cơ bản với đầy đủ tính năng chính',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Các trường tùy chỉnh cho phiên bản (JSON)',
    example: {
      language: 'Tiếng Việt',
      format: 'PDF',
      pages: 100
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  customFields?: any;

  @ApiProperty({
    description: 'Liên kết nội dung sản phẩm số',
    example: 'https://example.com/download/product-v1.pdf',
    maxLength: 1000,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  @IsUrl({}, { message: 'Content link phải là URL hợp lệ' })
  contentLink?: string;

  @ApiProperty({
    description: 'Mã SKU riêng của phiên bản',
    example: 'EBOOK-STD-V1',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  sku?: string;

  @ApiProperty({
    description: 'Mã vạch riêng của phiên bản',
    example: '1234567890123',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  barcode?: string;

  @ApiProperty({
    description: 'Số lượng tối thiểu mỗi lần mua',
    example: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  minQuantity?: number;

  @ApiProperty({
    description: 'Số lượng tối đa mỗi lần mua',
    example: 10,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  maxQuantity?: number;

  @ApiProperty({
    description: 'Thao tác với ảnh riêng cho phiên bản này',
    type: [ProductImageOperationDto],
    example: [
      {
        operation: 'add',
        mediaId: '123e4567-e89b-12d3-a456-426614174000'
      },
      {
        operation: 'delete',
        key: 'version-image.jpg'
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductImageOperationDto)
  imageOperations?: ProductImageOperationDto[];
}

/**
 * DTO cho thao tác phiên bản sản phẩm số
 */
export class DigitalProductVersionOperationDto {
  @ApiProperty({
    description: 'Loại thao tác',
    enum: VersionOperationType,
    example: VersionOperationType.ADD,
  })
  @IsEnum(VersionOperationType)
  @IsNotEmpty()
  operation: VersionOperationType;

  @ApiProperty({
    description: 'ID phiên bản (bắt buộc cho UPDATE và DELETE)',
    example: 123,
    required: false,
  })
  @ValidateIf((o) => o.operation === VersionOperationType.UPDATE || o.operation === VersionOperationType.DELETE)
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  id?: number;

  @ApiProperty({
    description: 'Dữ liệu phiên bản (bắt buộc cho ADD và UPDATE)',
    type: VersionOperationDataDto,
    required: false,
  })
  @ValidateIf((o) => o.operation === VersionOperationType.ADD || o.operation === VersionOperationType.UPDATE)
  @ValidateNested()
  @Type(() => VersionOperationDataDto)
  data?: VersionOperationDataDto;
}
