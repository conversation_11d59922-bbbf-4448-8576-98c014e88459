import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { CustomerProductService } from '../services/customer-product.service';
import {
  CreateCustomerProductDto,
  UpdateCustomerProductDto,
  QueryCustomerProductDto,
  CustomerProductResponseDto,
  BulkDeleteCustomerProductDto,
  BulkDeleteCustomerProductResponseDto,
} from '../dto/customer-product';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các request liên quan đến sản phẩm khách hàng
 */
@ApiTags(SWAGGER_API_TAGS.USER_BUSINESS_CUSTOMER_PRODUCT)
@Controller('user/customer-products-old')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto,
  PaginatedResult,
  CustomerProductResponseDto,
  CreateCustomerProductDto,
  UpdateCustomerProductDto,
  QueryCustomerProductDto,
  BulkDeleteCustomerProductDto,
  BulkDeleteCustomerProductResponseDto,
)
export class CustomerProductController {
  constructor(private readonly customerProductService: CustomerProductService) {}

  /**
   * Tạo sản phẩm khách hàng mới
   * @param createDto DTO chứa thông tin sản phẩm mới
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã tạo
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo sản phẩm khách hàng mới',
    description: 'Tạo sản phẩm khách hàng mới với thông tin cơ bản. Sản phẩm sẽ có trạng thái PENDING (chờ duyệt) sau khi tạo.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Sản phẩm đã được tạo thành công',
    type: () => ApiResponseDto.getSchema(CustomerProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  async create(
    @Body() createDto: CreateCustomerProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.customerProductService.create(createDto, userId);
    return ApiResponseDto.created<CustomerProductResponseDto>(
      product,
      'Tạo sản phẩm khách hàng thành công',
    );
  }

  /**
   * Lấy danh sách sản phẩm khách hàng
   * @param queryDto DTO chứa các tham số truy vấn
   * @param userId ID của người dùng hiện tại
   * @returns Danh sách sản phẩm với phân trang
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy danh sách sản phẩm khách hàng',
    description: 'Lấy danh sách sản phẩm khách hàng của người dùng hiện tại với phân trang và filter.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách sản phẩm khách hàng',
    type: () => ApiResponseDto.getPaginatedSchema(CustomerProductResponseDto),
  })
  async findAll(
    @Query() queryDto: QueryCustomerProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const products = await this.customerProductService.findAll(queryDto, userId);
    return ApiResponseDto.success<PaginatedResult<CustomerProductResponseDto>>(
      products,
      'Lấy danh sách sản phẩm khách hàng thành công',
    );
  }

  /**
   * Lấy chi tiết sản phẩm khách hàng theo ID
   * @param id ID của sản phẩm
   * @param userId ID của người dùng hiện tại
   * @returns Chi tiết sản phẩm
   */
  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy chi tiết sản phẩm khách hàng',
    description: 'Lấy thông tin chi tiết của một sản phẩm khách hàng theo ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm khách hàng',
    type: Number,
    example: 123,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Chi tiết sản phẩm khách hàng',
    type: () => ApiResponseDto.getSchema(CustomerProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm',
  })
  async findById(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.customerProductService.findById(id, userId);
    return ApiResponseDto.success<CustomerProductResponseDto>(
      product,
      'Lấy chi tiết sản phẩm khách hàng thành công',
    );
  }

  /**
   * Cập nhật sản phẩm khách hàng
   * @param id ID của sản phẩm cần cập nhật
   * @param updateDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã cập nhật
   */
  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cập nhật sản phẩm khách hàng',
    description: 'Cập nhật thông tin sản phẩm khách hàng. Chỉ có thể cập nhật sản phẩm của chính mình.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm khách hàng',
    type: Number,
    example: 123,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Sản phẩm đã được cập nhật thành công',
    type: () => ApiResponseDto.getSchema(CustomerProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateCustomerProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.customerProductService.update(id, updateDto, userId);
    return ApiResponseDto.success<CustomerProductResponseDto>(
      product,
      'Cập nhật sản phẩm khách hàng thành công',
    );
  }

  /**
   * Xóa sản phẩm khách hàng (soft delete)
   * @param id ID của sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Thông báo xóa thành công
   */
  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Xóa sản phẩm khách hàng',
    description: 'Xóa mềm sản phẩm khách hàng (chuyển trạng thái thành DELETED). Chỉ có thể xóa sản phẩm của chính mình.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm khách hàng',
    type: Number,
    example: 123,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Sản phẩm đã được xóa thành công',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm',
  })
  async delete(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ) {
    await this.customerProductService.delete(id, userId);
    return ApiResponseDto.success(null, 'Xóa sản phẩm khách hàng thành công');
  }

  /**
   * Xóa nhiều sản phẩm khách hàng cùng lúc (soft delete)
   * @param bulkDeleteDto DTO chứa danh sách ID sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả xóa nhiều sản phẩm
   */
  @Delete('bulk')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Xóa nhiều sản phẩm khách hàng',
    description: 'Xóa mềm nhiều sản phẩm khách hàng cùng lúc. Tối đa 50 sản phẩm mỗi lần.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa nhiều sản phẩm thành công',
    type: () => ApiResponseDto.getSchema(BulkDeleteCustomerProductResponseDto),
  })
  @ApiResponse({
    status: 207,
    description: 'Một số sản phẩm không thể xóa',
    type: () => ApiResponseDto.getSchema(BulkDeleteCustomerProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  async bulkDelete(
    @Body() bulkDeleteDto: BulkDeleteCustomerProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.customerProductService.bulkDelete(bulkDeleteDto, userId);
    return ApiResponseDto.success(result, 'Xóa sản phẩm khách hàng thành công');
  }
}
