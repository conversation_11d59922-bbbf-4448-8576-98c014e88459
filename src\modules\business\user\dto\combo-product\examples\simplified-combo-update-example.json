{"description": "🎯 VÍ DỤ ĐÃ ĐƠN GIẢN HÓA - Combo Product Update Request Body", "principle": {"title": "<PERSON><PERSON><PERSON><PERSON> Operations the<PERSON> quan hệ", "rules": {"1:1_relationship": "<PERSON><PERSON><PERSON> nhật trực tiếp - KHÔNG cần operations", "1:many_relationship": "Sử dụng operations (ADD/DELETE/UPDATE)", "many:many_relationship": "Sử dụng operations (ADD/DELETE/UPDATE)"}, "examples": {"customer_products_combo_products": "1:1 → <PERSON><PERSON><PERSON> nhật trực tiếp", "combo_product_images": "1:<PERSON><PERSON><PERSON><PERSON> → Cần operations", "combo_product_variants": "1:n<PERSON><PERSON><PERSON> → Cần operations (nếu có)"}}, "simplifiedExample": {"customerProduct": {"name": "Combo <PERSON> thun + Quần jean cao cấp", "description": "Combo thời trang nam gồm áo thun cotton và quần jean slim fit", "productType": "COMBO", "tags": ["combo", "th<PERSON>i trang", "nam", "t<PERSON><PERSON><PERSON> k<PERSON>"]}, "customerProductPricing": {"displayPrice": {"listPrice": 800000, "salePrice": 650000, "currency": "VND"}, "typePrice": "HAS_PRICE"}, "customFields": [{"customFieldId": 1, "value": {"value": "Premium"}}], "comboProduct": {"comboItems": [{"productId": 5, "total": 2}, {"productId": 14, "total": 1}], "maxQuantity": 100}, "imageOperations": [{"operation": "add", "mediaId": "cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2"}, {"operation": "delete", "entityMediaId": 456}]}, "comparison": {"before": {"comboItems": "<PERSON>ức tạp với operations.comboItems (ADD/UPDATE/DELETE)", "images": "Có 2 cách: imageOperations và operations.images", "complexity": "<PERSON> <PERSON> <PERSON><PERSON><PERSON> hi<PERSON>u cho developer"}, "after": {"comboItems": "<PERSON><PERSON><PERSON> g<PERSON> - <PERSON><PERSON><PERSON> nhật trự<PERSON> tiếp qua comboProduct.comboItems", "images": "Chỉ 1 cách: imageOperations", "complexity": "Thấp - <PERSON><PERSON> hiểu và sử dụng"}}, "benefits": {"simplicity": "Giảm 70% độ phức tạp của API", "consistency": "<PERSON><PERSON><PERSON><PERSON> quán với nguyên tắc quan hệ database", "maintainability": "Dễ maintain và debug", "developer_experience": "Developer d<PERSON> hiểu và sử dụng", "performance": "<PERSON>t logic x<PERSON> l<PERSON> h<PERSON>, performance tốt hơn"}, "usageGuidelines": {"do": ["✅ Cập nhật combo items trực tiếp qua comboProduct.comboItems", "✅ Sử dụng imageOperations cho thao tác images", "✅ <PERSON><PERSON> hệ thống tự động tính calculatedPrice từ comboItems", "✅ Sử dụng displayPrice cho giá hiển thị"], "dont": ["❌ KHÔNG sử dụng operations cho combo items (đã xóa)", "❌ KHÔNG nhập calculatedPrice thủ công", "❌ KHÔNG sử dụng operations cho quan hệ 1:1"]}, "technicalDetails": {"database_operations": {"combo_items": "UPDATE combo_products SET combo_items = {...} WHERE id = ?", "images": "INSERT/DELETE entity_has_media WHERE product_combo_id = ?"}, "validation": {"combo_items": "ComboValidationHelper.validateComboItems()", "images": "ProductImageOperationDto validation"}, "price_calculation": {"automatic": "<PERSON><PERSON> thống tự động tính calculatedPrice từ comboItems", "formula": "SUM(product.price * item.total) for each item in comboItems"}}}