import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { ProductTypeEnum, EntityStatusEnum } from '@modules/business/enums';

/**
 * Response DTO đơn giản cho customer product
 * Chỉ trả về thông tin cơ bản sau khi tạo sản phẩm
 */
export class SimpleCustomerProductResponseDto {
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 123,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Áo thun nam cao cấp',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Áo thun nam chất liệu cotton 100%, thiết kế hiện đại',
    required: false,
  })
  @Expose()
  description?: string;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
  })
  @Expose()
  productType: ProductTypeEnum;

  @ApiProperty({
    description: 'Trạng thái sản phẩm',
    enum: EntityStatusEnum,
    example: EntityStatusEnum.PENDING,
  })
  @Expose()
  status: EntityStatusEnum;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1704067200000,
    nullable: true,
  })
  @Expose()
  createdAt: number | null;

  @ApiProperty({
    description: 'Thời gian cập nhật cuối (timestamp)',
    example: 1704067200000,
    nullable: true,
  })
  @Expose()
  updatedAt: number | null;

  @ApiProperty({
    description: 'ID người tạo sản phẩm',
    example: 1,
  })
  @Expose()
  userId: number;
}
