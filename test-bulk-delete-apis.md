# Test Bulk Delete APIs

## 1. Admin Audience Custom Field Definition - Bulk Delete

### Endpoint
```
DELETE /admin/marketing/audience-custom-fields
```

### Headers
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

### Request Body
```json
{
  "customFieldIds": [1, 2, 3]
}
```

### Expected Response (Success)
```json
{
  "success": true,
  "message": "Đã xóa 2 trường tùy chỉnh thành công, 1 trường tùy chỉnh không thể xóa",
  "data": {
    "deletedCount": 2,
    "failedCount": 1,
    "deletedIds": [1, 2],
    "failedIds": [3],
    "message": "Đã xóa 2 trường tùy chỉnh thành công, 1 trường tùy chỉnh không thể xóa"
  }
}
```

### Expected Response (All Success)
```json
{
  "success": true,
  "message": "Đã xóa 3 trường tùy chỉnh thành công",
  "data": {
    "deletedCount": 3,
    "failedCount": 0,
    "deletedIds": [1, 2, 3],
    "failedIds": [],
    "message": "Đã xóa 3 trường tùy chỉnh thành công"
  }
}
```

## 2. Admin Segment - Bulk Delete

### Endpoint
```
DELETE /admin/marketing/segments
```

### Headers
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

### Request Body
```json
{
  "ids": [1, 2, 3]
}
```

### Expected Response (Success)
```json
{
  "success": true,
  "message": "Đã xóa 2 segment thành công, 1 segment không thể xóa",
  "data": {
    "deletedCount": 2,
    "failedCount": 1,
    "deletedIds": [1, 2],
    "failedIds": [3],
    "message": "Đã xóa 2 segment thành công, 1 segment không thể xóa"
  }
}
```

### Expected Response (All Success)
```json
{
  "success": true,
  "message": "Đã xóa 3 segment thành công",
  "data": {
    "deletedCount": 3,
    "failedCount": 0,
    "deletedIds": [1, 2, 3],
    "failedIds": [],
    "message": "Đã xóa 3 segment thành công"
  }
}
```

## Error Cases

### 1. Invalid Request Body
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "customFieldIds",
      "message": "Phải có ít nhất một item để xóa"
    }
  ]
}
```

### 2. Empty Array
```json
{
  "customFieldIds": []
}
```

Response:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "customFieldIds",
      "message": "Phải có ít nhất một item để xóa"
    }
  ]
}
```

### 3. Invalid ID Type
```json
{
  "customFieldIds": ["invalid", "ids"]
}
```

Response:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "customFieldIds",
      "message": "ID phải là số"
    }
  ]
}
```

## Testing Steps

1. **Setup Test Data**
   - Create some custom fields and segments via POST APIs
   - Note down the IDs

2. **Test Valid Bulk Delete**
   - Send DELETE request with valid IDs
   - Verify response structure
   - Check that items are actually deleted

3. **Test Partial Success**
   - Send DELETE request with mix of valid and invalid IDs
   - Verify partial success response

4. **Test Validation**
   - Send empty array
   - Send invalid data types
   - Verify error responses

5. **Test Authorization**
   - Send request without JWT token
   - Send request with invalid token
   - Verify 401 responses
