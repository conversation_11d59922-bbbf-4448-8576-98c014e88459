import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Enum cho trạng thái chiến dịch ZNS
 */
export enum ZaloZnsCampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

/**
 * Entity cho chiến dịch ZNS Zalo
 */
@Entity('zalo_zns_campaigns')
export class ZaloZnsCampaign {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'oa_id' })
  oaId: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ name: 'template_id' })
  templateId: string;

  @Column({ name: 'template_data', type: 'jsonb' })
  templateData: Record<string, any>;

  @Column({ name: 'phone_list', type: 'jsonb' })
  phoneList: string[];

  @Column({ type: 'enum', enum: ZaloZnsCampaignStatus, default: ZaloZnsCampaignStatus.DRAFT })
  status: ZaloZnsCampaignStatus;

  @Column({ name: 'scheduled_at', type: 'bigint', nullable: true })
  scheduledAt?: number;

  @Column({ name: 'started_at', type: 'bigint', nullable: true })
  startedAt?: number;

  @Column({ name: 'completed_at', type: 'bigint', nullable: true })
  completedAt?: number;

  @Column({ name: 'total_messages', default: 0 })
  totalMessages: number;

  @Column({ name: 'sent_messages', default: 0 })
  sentMessages: number;

  @Column({ name: 'failed_messages', default: 0 })
  failedMessages: number;

  @Column({ name: 'job_ids', type: 'jsonb', nullable: true })
  jobIds: string[] | null;

  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage?: string;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
