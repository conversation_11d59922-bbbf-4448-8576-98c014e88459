import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng event_products trong cơ sở dữ liệu
 * L<PERSON><PERSON> thông tin hình thức tham gia sự kiện, bao gồm địa điểm, đ<PERSON>ờng dẫn, thời gian và múi giờ
 */
@Entity('event_products')
export class EventProduct {
  /**
   * ID tự tăng của sản phẩm sự kiện
   */
  @PrimaryColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * H<PERSON>nh thức tham gia sự kiện
   */
  @Column({
    name: 'participation_type',
    type: 'varchar',
    length: 255,
    nullable: false,
    comment: 'Hình thức tham gia sự kiện (online, offline, hybrid...)',
  })
  participationType: string;

  /**
   * Địa điểm tham gia sự kiện
   */
  @Column({
    name: 'location',
    type: 'text',
    nullable: true,
    comment: 'Địa điểm tham gia sự kiện (nếu offline hoặc hybrid)',
  })
  location: string | null;

  /**
   * Đường dẫn tham gia sự kiện
   */
  @Column({
    name: 'participation_url',
    type: 'text',
    nullable: true,
    comment: 'Đường dẫn tham gia sự kiện (nếu là online hoặc hybrid)',
  })
  participationUrl: string | null;

  /**
   * Thời gian bắt đầu sự kiện
   */
  @Column({
    name: 'start_date',
    type: 'bigint',
    nullable: false,
    comment: 'Thời gian bắt đầu sự kiện (epoch milliseconds)',
  })
  startDate: number;

  /**
   * Ngày và giờ kết thúc sự kiện kèm múi giờ
   */
  @Column({
    name: 'end_date',
    type: 'timestamp with time zone',
    nullable: false,
    comment: 'Ngày và giờ kết thúc sự kiện kèm múi giờ',
  })
  endDate: Date;

  /**
   * Tên múi giờ chuẩn
   */
  @Column({
    name: 'time_zone',
    type: 'varchar',
    length: 100,
    nullable: false,
    comment: 'Tên múi giờ chuẩn (ví dụ: Asia/Ho_Chi_Minh)',
  })
  timeZone: string;
}
