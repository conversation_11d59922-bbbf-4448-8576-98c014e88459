import { Injectable, Logger } from '@nestjs/common';
import { In } from 'typeorm';
import { CustomerProductRepository } from '@modules/business/repositories/customer-product.repository';
import { ComboProductRepository } from '@modules/business/repositories/combo-product.repository';
import { EntityHasMediaRepository } from '@modules/business/repositories/entity-has-media.repository';
import { MediaRepository } from '@modules/data/media/repositories/media.repository';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils/time/time-interval.util';
import { CompleteUpdateComboProductDto } from '../dto/combo-product/complete-update-combo-product.dto';
import { CompleteComboProductResponseDto } from '../dto/combo-product/complete-combo-product-response.dto';
import { AppException } from '@common/exceptions/app.exception';
import { CUSTOMER_PRODUCT_ERROR_CODES } from '@modules/business/exceptions/customer-product.error-codes';
import { Transactional } from 'typeorm-transactional';
import { CustomerProduct, ComboProduct, EntityHasMedia } from '@modules/business/entities';
import { EntityStatusEnum, ProductTypeEnum } from '@modules/business/enums';
import { ImageOperationType } from '../dto/image-operations/image-operation.dto';
import { ComboItemOperationType } from '../dto/combo-product/combo-operations.dto';
import { ComboValidationHelper } from '../helpers/combo-validation.helper';

/**
 * Service xử lý cập nhật hoàn chỉnh Combo Product
 * Chỉ bao gồm customer_products + combo_products + images
 *
 * LOGIC PHÂN BIỆT ẢNH:
 * - Product level: product_combo_id có giá trị, các field khác = null
 * - Key và URL được lưu trong bảng media_data, entity_has_media chỉ lưu liên kết
 */
@Injectable()
export class CompleteComboProductService {
  private readonly logger = new Logger(CompleteComboProductService.name);

  constructor(
    private readonly customerProductRepository: CustomerProductRepository,
    private readonly comboProductRepository: ComboProductRepository,
    private readonly entityHasMediaRepository: EntityHasMediaRepository,
    private readonly mediaRepository: MediaRepository,
    private readonly comboValidationHelper: ComboValidationHelper,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Cập nhật hoàn chỉnh combo product
   * @param id ID của sản phẩm
   * @param dto DTO chứa toàn bộ thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã cập nhật
   */
  @Transactional()
  async updateCompleteComboProduct(
    id: number,
    dto: CompleteUpdateComboProductDto,
    userId: number,
  ): Promise<CompleteComboProductResponseDto> {
    try {
      this.logger.log(`Cập nhật hoàn chỉnh combo product ID=${id} cho userId=${userId}`);

      // 1. Kiểm tra sản phẩm tồn tại và thuộc về user
      const existingProduct = await this.validateProductOwnership(id, userId);

      // 2. Validate combo items nếu có
      if (dto.comboProduct?.comboItems) {
        await this.comboValidationHelper.validateComboItems(dto.comboProduct.comboItems, userId);
      }

      // 3. Cập nhật customer_products table
      const updatedCustomerProduct = await this.updateCustomerProduct(existingProduct, dto);

      // 4. Cập nhật/tạo combo_products table
      const comboProduct = await this.updateOrCreateComboProduct(id, dto, userId);

      // 5. Xử lý product image operations (entity_has_media.product_id)
      const productImageOperations = dto.productImageOperations || [];
      if (productImageOperations.length > 0) {
        await this.processProductImageOperations(id, productImageOperations, userId);
      }

      // 6. Xử lý combo image operations (entity_has_media.product_combo_id)
      const comboImageOperations = dto.comboImageOperations || [];
      if (comboImageOperations.length > 0) {
        await this.processComboImageOperations(id, comboImageOperations, userId);
      }

      // 7. Build operation results cho images
      const operationResults = {
        productImages: {
          added: dto.productImageOperations?.filter(op => op.operation === 'add').map((op, index) => ({
            mediaId: op.mediaId,
            entityMediaId: `product-img-${String(index + 1).padStart(3, '0')}`,
            url: `https://cdn.redai.vn/product-images/${op.mediaId}`,
          })) || [],
          deleted: dto.productImageOperations?.filter(op => op.operation === 'delete').map(op => op.entityMediaId) || [],
        },
        comboImages: {
          added: dto.comboImageOperations?.filter(op => op.operation === 'add').map((op, index) => ({
            mediaId: op.mediaId,
            entityMediaId: `combo-img-${String(index + 1).padStart(3, '0')}`,
            url: `https://cdn.redai.vn/combo-images/${op.mediaId}`,
          })) || [],
          deleted: dto.comboImageOperations?.filter(op => op.operation === 'delete').map(op => op.entityMediaId) || [],
        }
      };

      // 8. Tạo response hoàn chỉnh
      const response = await this.buildCompleteResponse(
        updatedCustomerProduct,
        comboProduct,
        operationResults
      );

      this.logger.log(`Cập nhật thành công combo product ID=${id}`);
      return response;

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi cập nhật combo product: ${error.message}`, error.stack);
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.UPDATE_FAILED,
        `Lỗi khi cập nhật sản phẩm combo: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết hoàn chỉnh combo product
   * @param id ID của sản phẩm
   * @param userId ID của người dùng hiện tại
   * @returns Chi tiết sản phẩm hoàn chỉnh
   */
  async getCompleteComboProduct(
    id: number,
    userId: number,
  ): Promise<CompleteComboProductResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết hoàn chỉnh combo product ID=${id} cho userId=${userId}`);

      // 1. Lấy customer product
      const customerProduct = await this.validateProductOwnership(id, userId);

      // 2. Lấy combo product data
      const comboProduct = await this.comboProductRepository.findById(id);

      // 3. Build response
      const response = await this.buildCompleteResponse(customerProduct, comboProduct || undefined);

      return response;

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi lấy chi tiết combo product: ${error.message}`, error.stack);
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.FIND_FAILED,
        `Lỗi khi lấy chi tiết sản phẩm combo: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra quyền sở hữu sản phẩm và validate productType
   */
  private async validateProductOwnership(id: number, userId: number): Promise<CustomerProduct> {
    const product = await this.customerProductRepository.findByIdAndUserId(id, userId);

    if (!product) {
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.NOT_FOUND,
        'Không tìm thấy sản phẩm hoặc bạn không có quyền truy cập',
      );
    }

    // Kiểm tra productType phải là COMBO
    if (product.productType !== ProductTypeEnum.COMBO) {
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.INVALID_PRODUCT_TYPE,
        `Sản phẩm này có loại '${product.productType}', không thể sử dụng API cập nhật Combo Product. Vui lòng sử dụng API phù hợp với loại sản phẩm.`,
      );
    }

    return product;
  }

  /**
   * Cập nhật customer_products table với cấu trúc nhóm mới
   */
  private async updateCustomerProduct(
    existingProduct: CustomerProduct,
    dto: CompleteUpdateComboProductDto,
  ): Promise<CustomerProduct> {
    const updateData: Partial<CustomerProduct> = {
      ...existingProduct,
      updatedAt: Date.now(),
      // Luôn đặt status là PENDING khi người dùng cập nhật sản phẩm
      status: EntityStatusEnum.PENDING,
    };

    // Cập nhật từ customerProduct (basicInfo)
    if (dto.customerProduct) {
      if (dto.customerProduct.name !== undefined) updateData.name = dto.customerProduct.name.trim();
      if (dto.customerProduct.description !== undefined) updateData.description = dto.customerProduct.description?.trim() || null;
      if (dto.customerProduct.productType !== undefined) updateData.productType = dto.customerProduct.productType;
      if (dto.customerProduct.tags !== undefined) updateData.tags = dto.customerProduct.tags;
    }

    // Cập nhật từ customerProductPricing (pricing)
    if (dto.customerProductPricing) {
      if (dto.customerProductPricing.displayPrice !== undefined) updateData.price = dto.customerProductPricing.displayPrice;
      if (dto.customerProductPricing.typePrice !== undefined) updateData.typePrice = dto.customerProductPricing.typePrice;
    }

    // Cập nhật custom fields
    if (dto.customFields !== undefined) {
      // Convert custom fields array thành object để lưu vào JSONB column
      const customFieldsObject = dto.customFields.reduce((acc, field) => {
        acc[field.customFieldId] = field.value;
        return acc;
      }, {} as any);
      updateData.customFields = customFieldsObject;
    }

    // Combo products không cần shipment config (không có trong entity)

    return await this.customerProductRepository.update(existingProduct.id, updateData);
  }

  /**
   * Cập nhật hoặc tạo combo_products record
   *
   * LOGIC GIÁ COMBO THÔNG MINH:
   * 1. customer_products.price = Giá bán chính thức của combo (giá khách hàng thấy)
   * 2. combo_products.price = Giá tính toán từ tổng giá các sản phẩm con (để so sánh, phân tích)
   *
   * Ưu tiên: customer_products.price > combo_products.price
   */
  private async updateOrCreateComboProduct(
    id: number,
    dto: CompleteUpdateComboProductDto,
    userId: number,
  ): Promise<ComboProduct> {
    // Kiểm tra xem combo product đã tồn tại chưa
    let comboProduct = await this.comboProductRepository.findById(id);

    if (comboProduct) {
      // Cập nhật existing record
      const updateData: Partial<ComboProduct> = {};

      if (dto.comboProduct?.maxQuantity !== undefined) {
        updateData.maxQuantity = dto.comboProduct.maxQuantity;
      }

      // Cập nhật combo items nếu có
      if (dto.comboProduct?.comboItems !== undefined) {
        updateData.comboItems = { info: dto.comboProduct.comboItems };

        // Tính toán lại giá combo từ các sản phẩm con
        const calculatedPrice = await this.calculateComboPrice(dto.comboProduct.comboItems, userId);
        updateData.price = calculatedPrice;
      }

      comboProduct = await this.comboProductRepository.update(id, updateData);
    } else {
      // Tạo mới combo product record
      const comboItems = dto.comboProduct?.comboItems || [];
      const calculatedPrice = await this.calculateComboPrice(comboItems, userId);

      const createData: Partial<ComboProduct> = {
        id: id, // Same as customer product ID
        price: calculatedPrice,
        comboItems: { info: comboItems },
        maxQuantity: dto.comboProduct?.maxQuantity || null,
      };

      comboProduct = await this.comboProductRepository.create(createData);
    }

    return comboProduct!;
  }



  /**
   * Xử lý product image operations (entity_has_media.product_id)
   */
  private async processProductImageOperations(
    productId: number,
    operations: any[],
    userId: number,
  ): Promise<void> {
    for (const operation of operations) {
      try {
        switch (operation.operation) {
          case ImageOperationType.ADD:
            if (operation.mediaId) {
              // Lấy media từ kho media_data theo mediaId
              const existingMedia = await this.mediaRepository.findOneBy({ id: operation.mediaId });

              if (!existingMedia) {
                throw new AppException(
                  CUSTOMER_PRODUCT_ERROR_CODES.MEDIA_NOT_FOUND,
                  `Media với ID ${operation.mediaId} không tồn tại trong kho media.`
                );
              }

              // Kiểm tra quyền sở hữu media
              if (existingMedia.ownedBy !== userId) {
                throw new AppException(
                  CUSTOMER_PRODUCT_ERROR_CODES.MEDIA_ACCESS_DENIED,
                  `Bạn không có quyền sử dụng media này.`
                );
              }

              // Tạo bản ghi trong entity_has_media để liên kết media với product
              const mediaLinkRecord: Partial<EntityHasMedia> = {
                productId: productId, // Product level image
                physicalVarial: null,
                ticketVarial: null,
                versionId: null,
                productComboId: null,
                productPlanVarialId: null,
                mediaId: existingMedia.id,
              };

              await this.entityHasMediaRepository.create(mediaLinkRecord);
              this.logger.log(`Liên kết media với product: ${existingMedia.storageKey} (Media ID: ${existingMedia.id})`);
            }
            break;

          case ImageOperationType.DELETE:
            if (operation.entityMediaId) {
              // Xóa liên kết media với product
              await this.entityHasMediaRepository.delete(operation.entityMediaId);
              this.logger.log(`Xóa liên kết media product: entityMediaId=${operation.entityMediaId}`);
            }
            break;

          default:
            this.logger.warn(`Unknown product image operation: ${operation.operation}`);
        }
      } catch (error) {
        this.logger.error(`Error processing product image operation: ${error.message}`, error.stack);
        throw error; // Để transaction rollback
      }
    }
  }

  /**
   * Xử lý combo image operations (entity_has_media.product_combo_id)
   */
  private async processComboImageOperations(
    comboProductId: number,
    operations: any[],
    userId: number,
  ): Promise<void> {
    for (const operation of operations) {
      try {
        switch (operation.operation) {
          case ImageOperationType.ADD:
            if (operation.mediaId) {
              // Lấy media từ kho media_data theo mediaId
              const existingMedia = await this.mediaRepository.findOneBy({ id: operation.mediaId });

              if (!existingMedia) {
                throw new AppException(
                  CUSTOMER_PRODUCT_ERROR_CODES.MEDIA_NOT_FOUND,
                  `Media với ID ${operation.mediaId} không tồn tại trong kho media.`
                );
              }

              // Kiểm tra quyền sở hữu media
              if (existingMedia.ownedBy !== userId) {
                throw new AppException(
                  CUSTOMER_PRODUCT_ERROR_CODES.MEDIA_ACCESS_DENIED,
                  `Bạn không có quyền sử dụng media này.`
                );
              }

              // Tạo bản ghi trong entity_has_media để liên kết media với combo
              const mediaLinkRecord: Partial<EntityHasMedia> = {
                productId: null,
                physicalVarial: null,
                ticketVarial: null,
                versionId: null,
                productComboId: comboProductId, // Combo level image
                productPlanVarialId: null,
                mediaId: existingMedia.id,
              };

              await this.entityHasMediaRepository.create(mediaLinkRecord);
              this.logger.log(`Liên kết media với combo: ${existingMedia.storageKey} (Media ID: ${existingMedia.id})`);
            }
            break;

          case ImageOperationType.DELETE:
            if (operation.entityMediaId) {
              // Xóa liên kết media với combo
              await this.entityHasMediaRepository.delete(operation.entityMediaId);
              this.logger.log(`Xóa liên kết media combo: entityMediaId=${operation.entityMediaId}`);
            }
            break;

          default:
            this.logger.warn(`Unknown combo image operation: ${operation.operation}`);
        }
      } catch (error) {
        this.logger.error(`Error processing combo image operation: ${error.message}`, error.stack);
        throw error; // Để transaction rollback
      }
    }
  }

  /**
   * Build response hoàn chỉnh cho combo product
   */
  private async buildCompleteResponse(
    customerProduct: CustomerProduct,
    comboProduct?: ComboProduct,
    operationResults?: any,
  ): Promise<CompleteComboProductResponseDto> {
    // Load product images (entity_has_media.product_id)
    const productImages = await this.loadProductImages(customerProduct.id);

    // Load combo images (entity_has_media.product_combo_id)
    const comboImages = await this.loadComboImages(customerProduct.id);

    // Lấy combo items từ combo_products.comboItems
    const comboItems = comboProduct?.comboItems?.info || [];

    const response: CompleteComboProductResponseDto = {
      customerProduct: {
        id: customerProduct.id,
        name: customerProduct.name,
        description: customerProduct.description || undefined,
        price: customerProduct.price,
        typePrice: customerProduct.typePrice,
        tags: customerProduct.tags,
        status: customerProduct.status,
        productType: customerProduct.productType,
        createdAt: customerProduct.createdAt || 0,
        updatedAt: customerProduct.updatedAt || 0,
        customFields: this.buildCustomFieldsResponse(customerProduct.customFields),
      },
      comboProduct: {
        price: comboProduct?.price || { listPrice: 0, salePrice: 0, currency: 'VND' }, // Giá tính toán từ sản phẩm con
        comboItems: comboItems,
        maxQuantity: comboProduct?.maxQuantity || undefined,
        // Thêm thông tin tiết kiệm
        savings: this.calculateSavings(customerProduct.price, comboProduct?.price),
      },
      productImages: productImages,
      comboImages: comboImages,
      operationResults,
    };

    return response;
  }

  /**
   * Load product images từ entity_has_media.product_id (optimized - tránh N+1 queries)
   */
  private async loadProductImages(productId: number): Promise<Array<{ key: string; mediaId: string; url: string }>> {
    const mediaLinks = await this.entityHasMediaRepository.findByProductId(productId);

    if (!mediaLinks || mediaLinks.length === 0) {
      return [];
    }

    // Lấy tất cả mediaIds để query một lần
    const mediaIds = mediaLinks
      .map(link => link.mediaId)
      .filter(mediaId => mediaId) as string[];

    if (mediaIds.length === 0) {
      return [];
    }

    // Query tất cả media cùng lúc thay vì từng cái một
    const medias = await this.mediaRepository.find({
      where: { id: In(mediaIds) }
    });

    // Tạo map để lookup nhanh
    const mediaMap = new Map(medias.map(media => [media.id, media]));

    // Build result với thứ tự giống mediaLinks
    const result: Array<{ key: string; mediaId: string; url: string }> = [];
    for (const link of mediaLinks) {
      if (link.mediaId) {
        const media = mediaMap.get(link.mediaId);
        if (media) {
          // Tạo CDN URL với CdnService
          let viewUrl = '';
          if (media.storageKey) {
            try {
              viewUrl = this.cdnService.generateUrlView(media.storageKey, TimeIntervalEnum.ONE_DAY) || '';
            } catch (error) {
              this.logger.error(`Lỗi khi tạo CDN view URL cho product image ${media.storageKey}: ${error.message}`);
              viewUrl = '';
            }
          }

          result.push({
            key: media.storageKey || '',
            mediaId: link.mediaId,
            url: viewUrl,
          });
        }
      }
    }

    return result;
  }

  /**
   * Load combo images từ entity_has_media.product_combo_id (optimized - tránh N+1 queries)
   */
  private async loadComboImages(comboProductId: number): Promise<Array<{ key: string; mediaId: string; url: string }>> {
    const mediaLinks = await this.entityHasMediaRepository.findByProductComboId(comboProductId);

    if (!mediaLinks || mediaLinks.length === 0) {
      return [];
    }

    // Lấy tất cả mediaIds để query một lần
    const mediaIds = mediaLinks
      .map(link => link.mediaId)
      .filter(mediaId => mediaId) as string[];

    if (mediaIds.length === 0) {
      return [];
    }

    // Query tất cả media cùng lúc thay vì từng cái một
    const medias = await this.mediaRepository.find({
      where: { id: In(mediaIds) }
    });

    // Tạo map để lookup nhanh
    const mediaMap = new Map(medias.map(media => [media.id, media]));

    // Build result với thứ tự giống mediaLinks
    const result: Array<{ key: string; mediaId: string; url: string }> = [];
    for (const link of mediaLinks) {
      if (link.mediaId) {
        const media = mediaMap.get(link.mediaId);
        if (media) {
          // Tạo CDN URL với CdnService
          let viewUrl = '';
          if (media.storageKey) {
            try {
              viewUrl = this.cdnService.generateUrlView(media.storageKey, TimeIntervalEnum.ONE_DAY) || '';
            } catch (error) {
              this.logger.error(`Lỗi khi tạo CDN view URL cho combo image ${media.storageKey}: ${error.message}`);
              viewUrl = '';
            }
          }

          result.push({
            key: media.storageKey || '',
            mediaId: link.mediaId,
            url: viewUrl,
          });
        }
      }
    }

    return result;
  }

  /**
   * Build custom fields response từ customer product custom fields
   * @param customFields Custom fields từ customer_products.custom_fields (JSONB)
   * @returns Array custom fields cho response
   */
  private buildCustomFieldsResponse(customFields: any): Array<{ customFieldId: number; value: { value: any } }> {
    if (!customFields || typeof customFields !== 'object') {
      return [];
    }

    // Convert từ object {customFieldId: value} thành array format
    return Object.entries(customFields).map(([customFieldId, value]) => ({
      customFieldId: parseInt(customFieldId),
      value: value as { value: any }
    }));
  }

  /**
   * Tính toán giá combo từ các sản phẩm con (cho combo_products.price)
   * @param comboItems Danh sách sản phẩm trong combo
   * @param userId ID người dùng
   * @returns Giá tính toán từ sản phẩm con
   */
  private async calculateComboPrice(comboItems: any[], userId: number): Promise<any> {
    if (!comboItems || comboItems.length === 0) {
      return { listPrice: 0, salePrice: 0, currency: 'VND' };
    }

    let totalListPrice = 0;
    let totalSalePrice = 0;
    let currency = 'VND';

    for (const item of comboItems) {
      try {
        // Lấy thông tin sản phẩm con
        const product = await this.customerProductRepository.findByIdAndUserId(item.productId, userId);
        if (product && product.price) {
          const productPrice = product.price as any;
          if (productPrice.listPrice) {
            totalListPrice += (productPrice.listPrice * item.total);
          }
          if (productPrice.salePrice) {
            totalSalePrice += (productPrice.salePrice * item.total);
          }
          if (productPrice.currency) {
            currency = productPrice.currency;
          }
        }
      } catch (error) {
        this.logger.warn(`Không thể tính giá cho sản phẩm ${item.productId}: ${error.message}`);
      }
    }

    return {
      listPrice: totalListPrice,
      salePrice: totalSalePrice,
      currency: currency,
      calculatedAt: Date.now(),
      note: 'Giá tính toán từ tổng giá các sản phẩm con'
    };
  }

  /**
   * Tính toán số tiền tiết kiệm khi mua combo
   * @param comboPrice Giá bán combo (customer_products.price)
   * @param calculatedPrice Giá tính toán từ sản phẩm con (combo_products.price)
   * @returns Thông tin tiết kiệm
   */
  private calculateSavings(comboPrice: any, calculatedPrice: any): any {
    if (!comboPrice || !calculatedPrice) {
      return null;
    }

    try {
      const comboSalePrice = comboPrice.salePrice || comboPrice.listPrice || 0;
      const calculatedSalePrice = calculatedPrice.salePrice || calculatedPrice.listPrice || 0;

      if (calculatedSalePrice > comboSalePrice) {
        const savingsAmount = calculatedSalePrice - comboSalePrice;
        const savingsPercentage = Math.round((savingsAmount / calculatedSalePrice) * 100);

        return {
          amount: savingsAmount,
          percentage: savingsPercentage,
          currency: comboPrice.currency || 'VND',
          message: `Tiết kiệm ${savingsAmount.toLocaleString()} ${comboPrice.currency || 'VND'} (${savingsPercentage}%) khi mua combo`
        };
      }

      return null;
    } catch (error) {
      this.logger.warn(`Lỗi khi tính toán tiết kiệm: ${error.message}`);
      return null;
    }
  }

  // Đã xóa loadComboItemsDetail vì không cần thiết
}
