import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { PhysicalProductVariant } from '../entities/physical-product-variant.entity';

/**
 * Repository cho PhysicalProductVariant entity
 * Xử lý các thao tác database cho biến thể sản phẩm vật lý
 */
@Injectable()
export class PhysicalProductVariantRepository {
  private readonly logger = new Logger(PhysicalProductVariantRepository.name);

  constructor(
    @InjectRepository(PhysicalProductVariant)
    private readonly repository: Repository<PhysicalProductVariant>,
  ) {}

  /**
   * Tạo biến thể sản phẩm vật lý mới
   * @param data Dữ liệu biến thể
   * @returns Biến thể đã tạo
   */
  async create(data: Partial<PhysicalProductVariant>): Promise<PhysicalProductVariant> {
    const variant = this.repository.create(data);
    return this.repository.save(variant);
  }

  /**
   * Tìm biến thể theo ID
   * @param id ID biến thể
   * @returns Biến thể hoặc null
   */
  async findById(id: number): Promise<PhysicalProductVariant | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm tất cả biến thể theo physical product ID
   * @param physicalProductId ID sản phẩm vật lý
   * @returns Danh sách biến thể
   */
  async findByPhysicalProductId(physicalProductId: number): Promise<PhysicalProductVariant[]> {
    return this.repository.find({
      where: { physicalProductId },
      order: { id: 'ASC' },
    });
  }

  /**
   * Cập nhật biến thể
   * @param id ID biến thể
   * @param data Dữ liệu cập nhật
   * @returns Biến thể đã cập nhật
   */
  async update(id: number, data: Partial<PhysicalProductVariant>): Promise<PhysicalProductVariant | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa mềm biến thể (có thể implement soft delete nếu cần)
   * @param id ID biến thể
   */
  async softDelete(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Xóa cứng biến thể
   * @param id ID biến thể
   */
  async delete(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Tạo nhiều biến thể cùng lúc
   * @param variants Danh sách dữ liệu biến thể
   * @returns Danh sách biến thể đã tạo
   */
  async bulkCreate(variants: Partial<PhysicalProductVariant>[]): Promise<PhysicalProductVariant[]> {
    if (!variants.length) return [];
    
    const createdVariants = this.repository.create(variants);
    return this.repository.save(createdVariants);
  }

  /**
   * Tìm biến thể theo SKU
   * @param sku Mã SKU
   * @param excludeId ID biến thể cần loại trừ (dùng khi update)
   * @returns Biến thể hoặc null
   */
  async findBySku(sku: string, excludeId?: number): Promise<PhysicalProductVariant | null> {
    const queryBuilder = this.repository.createQueryBuilder('variant');
    queryBuilder.where('variant.sku = :sku', { sku });
    
    if (excludeId) {
      queryBuilder.andWhere('variant.id != :excludeId', { excludeId });
    }

    return queryBuilder.getOne();
  }

  /**
   * Kiểm tra SKU có tồn tại không
   * @param sku Mã SKU
   * @param excludeId ID biến thể cần loại trừ
   * @returns true nếu SKU đã tồn tại
   */
  async existsBySku(sku: string, excludeId?: number): Promise<boolean> {
    const variant = await this.findBySku(sku, excludeId);
    return !!variant;
  }

  /**
   * Xóa tất cả biến thể của một sản phẩm vật lý
   * @param physicalProductId ID sản phẩm vật lý
   */
  async deleteByPhysicalProductId(physicalProductId: number): Promise<void> {
    await this.repository.delete({ physicalProductId });
  }

  /**
   * Đếm số lượng biến thể của một sản phẩm vật lý
   * @param physicalProductId ID sản phẩm vật lý
   * @returns Số lượng biến thể
   */
  async countByPhysicalProductId(physicalProductId: number): Promise<number> {
    return this.repository.count({
      where: { physicalProductId },
    });
  }

  /**
   * Lưu biến thể
   * @param variant Biến thể cần lưu
   * @returns Biến thể đã lưu
   */
  async save(variant: PhysicalProductVariant): Promise<PhysicalProductVariant> {
    return this.repository.save(variant);
  }

  /**
   * Tìm biến thể theo danh sách IDs
   * @param ids Danh sách ID
   * @returns Danh sách biến thể
   */
  async findByIds(ids: number[]): Promise<PhysicalProductVariant[]> {
    if (!ids.length) return [];

    return this.repository.find({
      where: { id: In(ids) },
    });
  }
}
