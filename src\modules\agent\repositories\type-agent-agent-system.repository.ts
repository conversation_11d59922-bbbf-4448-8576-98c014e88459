import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { TypeAgentAgentSystem } from '@modules/agent/entities';
import { PaginatedResult } from '@common/response';

/**
 * Repository cho TypeAgentAgentSystem
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến ánh xạ type agent và agent system
 */
@Injectable()
export class TypeAgentAgentSystemRepository extends Repository<TypeAgentAgentSystem> {
  private readonly logger = new Logger(TypeAgentAgentSystemRepository.name);

  constructor(private dataSource: DataSource) {
    super(TypeAgentAgentSystem, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho TypeAgentAgentSystem
   * @returns SelectQueryBuilder cho TypeAgentAgentSystem
   */
  private createBaseQuery(): SelectQueryBuilder<TypeAgentAgentSystem> {
    return this.createQueryBuilder('typeAgentAgentSystem');
  }

  /**
   * Tìm ánh xạ theo type ID và agent ID
   * @param typeId ID của type agent
   * @param agentId ID của agent system
   * @returns TypeAgentAgentSystem nếu tìm thấy, null nếu không tìm thấy
   */
  async findByTypeAndAgent(typeId: number, agentId: string): Promise<TypeAgentAgentSystem | null> {
    return this.createBaseQuery()
      .where('typeAgentAgentSystem.typeId = :typeId', { typeId })
      .andWhere('typeAgentAgentSystem.agentId = :agentId', { agentId })
      .getOne();
  }

  /**
   * Lấy danh sách agent systems của một type agent
   * @param typeId ID của type agent
   * @returns Danh sách agent systems
   */
  async findAgentsByTypeId(typeId: number): Promise<any[]> {
    return this.dataSource
      .createQueryBuilder()
      .select([
        'ags.id',
        'ags.nameCode',
        'ags.description',
        'ags.active',
        'ags.isSupervisor'
      ])
      .from('type_agent_agent_system', 'taas')
      .innerJoin('agents_system', 'ags', 'taas.agent_id = ags.id')
      .where('taas.type_id = :typeId', { typeId })
      .andWhere('ags.deleted_by IS NULL')
      .orderBy('ags.nameCode', 'ASC')
      .getRawMany();
  }

  /**
   * Lấy danh sách agent systems của type agent với phân trang
   * @param typeId ID của type agent
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách agent systems với phân trang
   */
  async findAgentsByTypeIdPaginated(
    typeId: number,
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'nameCode',
    sortDirection: 'ASC' | 'DESC' = 'ASC',
  ): Promise<PaginatedResult<any>> {
    try {
      // Map sortBy to correct database column
      const sortByMapping: Record<string, string> = {
        'nameCode': 'ags.name_code',
        'name': 'agent.name',
        'createdAt': 'agent.created_at',
      };

      const actualSortBy = sortByMapping[sortBy] || 'ags.name_code';

      // Tạo base query
      const baseQuery = this.dataSource
        .createQueryBuilder()
        .select([
          'ags.id',
          'ags.name_code AS nameCode',
          'ags.description',
          'ags.active',
          'ags.is_supervisor AS isSupervisor',
          'agent.name',
          'agent.avatar',
          'agent.created_at AS createdAt',
          'sm.model_id AS model_id',
          'sm.provider'
        ])
        .from('type_agent_agent_system', 'taas')
        .innerJoin('agents_system', 'ags', 'taas.agent_id = ags.id')
        .innerJoin('agents', 'agent', 'ags.id = agent.id')
        .leftJoin('system_models', 'sm', 'ags.system_model_id = sm.id')
        .where('taas.type_id = :typeId', { typeId })
        .andWhere('ags.deleted_by IS NULL')
        .andWhere('agent.deleted_at IS NULL');

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        baseQuery.andWhere(
          '(ags.name_code ILIKE :search OR ags.description ILIKE :search OR agent.name ILIKE :search)',
          { search: `%${search}%` }
        );
      }

      // Clone query để đếm tổng số
      const countQuery = baseQuery.clone();
      const totalItems = await countQuery.getCount();

      // Thêm sắp xếp và phân trang cho query chính
      const items = await baseQuery
        .orderBy(actualSortBy, sortDirection)
        .skip((page - 1) * limit)
        .take(limit)
        .getRawMany();

      // Map kết quả
      const mappedItems = items.map(item => ({
        id: item.ags_id,
        nameCode: item.nameCode,
        description: item.ags_description || '',
        active: item.ags_active || false,
        isSupervisor: item.isSupervisor || false,
        name: item.agent_name || 'Unknown Agent',
        avatar: item.agent_avatar || null,
        modelId: item.model_id || 'Unknown Model',
        provider: item.sm_provider || 'Unknown Provider',
        createdAt: item.createdAt || null,
      }));

      return {
        items: mappedItems,
        meta: {
          totalItems,
          itemCount: mappedItems.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };

    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách agent systems có phân trang: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy danh sách type agents sử dụng một agent system
   * @param agentId ID của agent system
   * @returns Danh sách type agents
   */
  async findTypesByAgentId(agentId: string): Promise<any[]> {
    return this.dataSource
      .createQueryBuilder()
      .select([
        'ta.id',
        'ta.name',
        'ta.description',
        'ta.status'
      ])
      .from('type_agent_agent_system', 'taas')
      .innerJoin('type_agents', 'ta', 'taas.type_id = ta.id')
      .where('taas.agent_id = :agentId', { agentId })
      .andWhere('ta.deleted_at IS NULL')
      .orderBy('ta.name', 'ASC')
      .getRawMany();
  }

  /**
   * Liên kết type agent với agent systems (tối ưu hóa và fix lỗi)
   * @param typeId ID của type agent
   * @param agentIds Danh sách ID của agent systems
   */
  async linkTypeWithAgents(typeId: number, agentIds: string[]): Promise<void> {
    try {
      // Lấy danh sách liên kết hiện tại với query đơn giản hơn
      const currentLinks = await this.createQueryBuilder('taas')
        .select(['taas.agentId'])
        .where('taas.typeId = :typeId', { typeId })
        .getRawMany();

      this.logger.debug(`Raw current links:`, currentLinks);

      // Filter out null values và đảm bảo chỉ có valid IDs
      const currentAgentIds = currentLinks
        .map(link => link.taas_agentId || link.agentId)
        .filter(id => id !== null && id !== undefined);

      // Đảm bảo newAgentIds cũng không có null/undefined
      const newAgentIds = (agentIds || []).filter(id => id !== null && id !== undefined && id.trim() !== '');

      this.logger.debug(`Current agent IDs: [${currentAgentIds.join(', ')}]`);
      this.logger.debug(`New agent IDs: [${newAgentIds.join(', ')}]`);

      // Nếu arrays giống nhau, không cần làm gì
      if (this.arraysEqual(currentAgentIds.sort(), newAgentIds.sort())) {
        this.logger.debug(`No changes needed for type agent ${typeId}`);
        return;
      }

      // Tìm các liên kết cần xóa (có trong DB nhưng không có trong danh sách mới)
      const agentIdsToRemove = currentAgentIds.filter(id => !newAgentIds.includes(id));

      // Tìm các liên kết cần thêm (có trong danh sách mới nhưng không có trong DB)
      const agentIdsToAdd = newAgentIds.filter(id => !currentAgentIds.includes(id));

      this.logger.debug(`Agent IDs to remove: [${agentIdsToRemove.join(', ')}]`);
      this.logger.debug(`Agent IDs to add: [${agentIdsToAdd.join(', ')}]`);

      // Xóa các liên kết không cần thiết
      if (agentIdsToRemove.length > 0) {
        await this.createQueryBuilder()
          .delete()
          .from(TypeAgentAgentSystem)
          .where('typeId = :typeId', { typeId })
          .andWhere('agentId IN (:...agentIds)', { agentIds: agentIdsToRemove })
          .execute();
        this.logger.debug(`Removed ${agentIdsToRemove.length} links`);
      }

      // Thêm các liên kết mới
      if (agentIdsToAdd.length > 0) {
        const linkData = agentIdsToAdd.map(agentId => ({
          typeId: typeId,
          agentId: agentId,
        }));

        await this.createQueryBuilder()
          .insert()
          .into(TypeAgentAgentSystem)
          .values(linkData)
          .execute();
        this.logger.debug(`Added ${agentIdsToAdd.length} links`);
      }

      this.logger.log(`Type agent ${typeId}: Removed ${agentIdsToRemove.length} links, Added ${agentIdsToAdd.length} links`);
    } catch (error) {
      this.logger.error(`Lỗi khi liên kết type agent với agent systems: ${error.message}`);
      throw error;
    }
  }

  /**
   * So sánh 2 arrays
   * @param a Array 1
   * @param b Array 2
   * @returns true nếu giống nhau
   */
  private arraysEqual(a: string[], b: string[]): boolean {
    return a.length === b.length && a.every((val, index) => val === b[index]);
  }

  /**
   * Liên kết type agent với agent systems (phương thức backup - reset hoàn toàn)
   * @param typeId ID của type agent
   * @param agentIds Danh sách ID của agent systems
   */
  async linkTypeWithAgentsReset(typeId: number, agentIds: string[]): Promise<void> {
    try {
      this.logger.debug(`Reset linking for type agent ${typeId} with agents: [${(agentIds || []).join(', ')}]`);

      // Xóa tất cả liên kết cũ
      await this.createQueryBuilder()
        .delete()
        .from(TypeAgentAgentSystem)
        .where('typeId = :typeId', { typeId })
        .execute();

      // Thêm liên kết mới (nếu có)
      const validAgentIds = (agentIds || []).filter(id => id !== null && id !== undefined && id.trim() !== '');

      if (validAgentIds.length > 0) {
        const linkData = validAgentIds.map(agentId => ({
          typeId: typeId,
          agentId: agentId,
        }));

        await this.createQueryBuilder()
          .insert()
          .into(TypeAgentAgentSystem)
          .values(linkData)
          .execute();
      }

      this.logger.log(`Reset complete for type agent ${typeId}: ${validAgentIds.length} links created`);
    } catch (error) {
      this.logger.error(`Lỗi khi reset liên kết type agent với agent systems: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xóa liên kết giữa type agent và agent system
   * @param typeId ID của type agent
   * @param agentId ID của agent system
   * @returns true nếu xóa thành công
   */
  async unlinkTypeFromAgent(typeId: number, agentId: string): Promise<boolean> {
    try {
      const result = await this.createQueryBuilder()
        .delete()
        .from(TypeAgentAgentSystem)
        .where('typeId = :typeId', { typeId })
        .andWhere('agentId = :agentId', { agentId })
        .execute();

      return result.affected !== null && result.affected !== undefined && result.affected > 0;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa liên kết type-agent: ${error.message}`);
      return false;
    }
  }

  /**
   * Xóa tất cả liên kết của một type agent
   * @param typeId ID của type agent
   * @returns true nếu xóa thành công
   */
  async unlinkAllAgentsFromType(typeId: number): Promise<boolean> {
    try {
      const result = await this.createQueryBuilder()
        .delete()
        .from(TypeAgentAgentSystem)
        .where('typeId = :typeId', { typeId })
        .execute();

      return true; // Luôn trả về true vì có thể không có liên kết nào
    } catch (error) {
      this.logger.error(`Lỗi khi xóa tất cả liên kết agent của type ${typeId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Đếm số lượng agent systems của một type agent
   * @param typeId ID của type agent
   * @returns Số lượng agent systems
   */
  async countAgentsByTypeId(typeId: number): Promise<number> {
    try {
      const result = await this.createBaseQuery()
        .where('typeAgentAgentSystem.typeId = :typeId', { typeId })
        .getCount();

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi đếm agent systems của type ${typeId}: ${error.message}`);
      return 0;
    }
  }

  /**
   * Đếm số lượng type agents sử dụng một agent system
   * @param agentId ID của agent system
   * @returns Số lượng type agents
   */
  async countTypesByAgentId(agentId: string): Promise<number> {
    try {
      const result = await this.createBaseQuery()
        .where('typeAgentAgentSystem.agentId = :agentId', { agentId })
        .getCount();

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi đếm type agents của agent ${agentId}: ${error.message}`);
      return 0;
    }
  }
}
