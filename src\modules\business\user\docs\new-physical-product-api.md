# 📋 New Physical Product API Documentation

## 🎯 Overview

Hệ thống API mới cho Physical Product được thiết kế với 2 luồng chính:

1. **Simple Customer Product Creation**: Tạo sản phẩm đơn giản với 3 fields cơ bản
2. **Complete Physical Product Update**: Cập nhật hoàn chỉnh sản phẩm vật lý với tất cả thông tin

## 🚀 API Endpoints

### 1. Simple Customer Product APIs

#### 1.1 Create Simple Customer Product
```http
POST /user/customer-products
```

**Request Body:**
```json
{
  "name": "<PERSON><PERSON> thun nam cao cấp",
  "description": "<PERSON>o thun nam chất liệu cotton 100%, thiết kế hiện đại",
  "productType": "PHYSICAL"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Tạo sản phẩm khách hàng thành công",
  "data": {
    "id": 123,
    "name": "<PERSON>o thun nam cao cấp",
    "description": "<PERSON>o thun nam chất liệu cotton 100%, thiết kế hiện đại",
    "productType": "PHYSICAL",
    "status": "PENDING",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z",
    "userId": 1
  }
}
```

#### 1.2 Get Customer Products List
```http
GET /user/customer-products?page=1&limit=10&search=áo thun&productType=PHYSICAL&status=APPROVED
```

**Response:**
```json
{
  "success": true,
  "message": "Lấy danh sách sản phẩm khách hàng thành công",
  "data": {
    "items": [
      {
        "id": 123,
        "name": "Áo thun nam cao cấp",
        "description": "Áo thun nam chất liệu cotton 100%",
        "productType": "PHYSICAL",
        "status": "APPROVED",
        "createdAt": "2024-01-15T10:30:00.000Z",
        "updatedAt": "2024-01-15T10:30:00.000Z",
        "userId": 1
      }
    ],
    "meta": {
      "totalItems": 50,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 5,
      "currentPage": 1
    }
  }
}
```

#### 1.3 Get Customer Product Detail
```http
GET /user/customer-products/123
```

#### 1.4 Delete Batch Customer Products
```http
DELETE /user/customer-products
```

**Request Body:**
```json
{
  "ids": [123, 124, 125]
}
```

### 2. Complete Physical Product APIs

#### 2.1 Update Complete Physical Product
```http
PUT /user/products/physical/123
```

**Request Body:**
```json
{
  // Base fields từ UpdateCustomerProductDto
  "name": "Áo thun nam cao cấp - Phiên bản mới",
  "description": "Mô tả cập nhật",
  "productType": "PHYSICAL",
  
  // Customer products fields
  "price": {
    "listPrice": 500000,
    "salePrice": 450000,
    "currency": "VND"
  },
  "typePrice": "HAS_PRICE",
  "tags": ["thời trang", "nam", "cotton", "premium"],
  "status": "APPROVED",
  "customFields": [
    {
      "customFieldId": 1,
      "value": { "value": "XL" }
    }
  ],
  
  // Physical products fields
  "stockQuantity": 150,
  "sku": "SHIRT-001-V2",
  "barcode": "1234567890123",
  "shipmentConfig": {
    "widthCm": 25,
    "heightCm": 5,
    "lengthCm": 30,
    "weightGram": 200
  },
  
  // 1:many operations
  "variantOperations": [
    {
      "operation": "add",
      "data": {
        "name": "Áo thun đỏ size M",
        "sku": "SHIRT-RED-M-001",
        "attributes": { "color": "Đỏ", "size": "M" },
        "price": { "listPrice": 300000, "salePrice": 250000, "currency": "VND" }
      }
    },
    {
      "operation": "update",
      "id": 456,
      "data": {
        "name": "Áo thun đỏ size L - Cập nhật",
        "attributes": { "color": "Đỏ", "size": "L" }
      }
    },
    {
      "operation": "delete",
      "id": 789
    }
  ],
  "imageOperations": [
    {
      "operation": "add",
      "key": "new-product-image.jpg",
      "mediaId": 123,
      "position": 1
    },
    {
      "operation": "delete",
      "key": "old-product-image.jpg"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Cập nhật sản phẩm vật lý thành công",
  "data": {
    // Customer product data
    "id": 123,
    "name": "Áo thun nam cao cấp - Phiên bản mới",
    "description": "Mô tả cập nhật",
    "productType": "PHYSICAL",
    "price": { "listPrice": 500000, "salePrice": 450000, "currency": "VND" },
    "typePrice": "HAS_PRICE",
    "tags": ["thời trang", "nam", "cotton", "premium"],
    "status": "APPROVED",
    "customFields": [{ "customFieldId": 1, "value": { "value": "XL" } }],
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T11:45:00.000Z",
    "userId": 1,
    
    // Physical product data
    "stockQuantity": 150,
    "sku": "SHIRT-001-V2",
    "barcode": "1234567890123",
    "shipmentConfig": {
      "widthCm": 25,
      "heightCm": 5,
      "lengthCm": 30,
      "weightGram": 200
    },
    
    // Variants
    "variants": [
      {
        "id": 456,
        "physicalProductId": 123,
        "name": "Áo thun đỏ size M",
        "sku": "SHIRT-RED-M-001",
        "attributes": { "color": "Đỏ", "size": "M" },
        "price": { "listPrice": 300000, "salePrice": 250000, "currency": "VND" },
        "images": []
      }
    ],
    
    // Images
    "images": [
      {
        "key": "new-product-image.jpg",
        "url": "https://cdn.example.com/new-product-image.jpg",
        "position": 1
      }
    ]
  }
}
```

#### 2.2 Get Complete Physical Product Detail
```http
GET /user/products/physical/123/complete
```

## 🔄 API Flow

### Luồng tạo sản phẩm mới:
1. **POST** `/user/customer-products` - Tạo sản phẩm với 3 fields cơ bản
2. **PUT** `/user/products/physical/{id}` - Cập nhật hoàn chỉnh thông tin

### Luồng quản lý sản phẩm:
1. **GET** `/user/customer-products` - Xem danh sách sản phẩm
2. **GET** `/user/products/physical/{id}/complete` - Xem chi tiết hoàn chỉnh
3. **PUT** `/user/products/physical/{id}` - Cập nhật hoàn chỉnh
4. **DELETE** `/user/customer-products` - Xóa batch sản phẩm

## 📊 Database Operations

### Simple Creation:
- Insert vào `customer_products` với 3 fields + metadata
- Status mặc định: `PENDING`

### Complete Update:
1. Update `customer_products` table
2. Update/Insert `physical_products` table (1:1)
3. Process `physical_product_variants` operations:
   - **ADD**: Insert new variants
   - **UPDATE**: Update existing variants
   - **DELETE**: Soft delete variants
4. Process `entity_has_media` operations:
   - **ADD**: Insert new image links
   - **DELETE**: Remove image links

## 🎯 Benefits

1. **Tách biệt rõ ràng**: Creation đơn giản vs Update phức tạp
2. **Type-safe**: Sử dụng existing operation DTOs
3. **Flexible**: Operations cho 1:many relationships
4. **Maintainable**: Clear separation of concerns
5. **User-friendly**: Tạo nhanh với 3 fields, edit chi tiết sau
