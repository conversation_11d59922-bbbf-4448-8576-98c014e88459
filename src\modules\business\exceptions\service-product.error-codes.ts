import { ErrorCode } from '@common/exceptions/app.exception';
import { HttpStatus } from '@nestjs/common';
import { CUSTOMER_PRODUCT_ERROR_CODES } from './customer-product.error-codes';

/**
 * Error codes cho Service Product module
 * Range: 4300-4399 (extends customer product range 4000-4099)
 * 
 * Thừa kế tất cả error codes từ CUSTOMER_PRODUCT_ERROR_CODES
 * và thêm các error codes đặc biệt cho service product
 */
export const SERVICE_PRODUCT_ERROR_CODES = {
  // ========== THỪA KẾ TỪ CUSTOMER PRODUCT ==========
  ...CUSTOMER_PRODUCT_ERROR_CODES,

  // ========== SERVICE PRODUCT SPECIFIC ERRORS (4300-4319) ==========
  SERVICE_NOT_FOUND: new ErrorCode(4300, 'Không tìm thấy sản phẩm dịch vụ', HttpStatus.NOT_FOUND),
  SERVICE_CREATION_FAILED: new ErrorCode(4301, 'Lỗi khi tạo sản phẩm dịch vụ', HttpStatus.BAD_REQUEST),
  SERVICE_UPDATE_FAILED: new ErrorCode(4302, 'Lỗi khi cập nhật sản phẩm dịch vụ', HttpStatus.BAD_REQUEST),
  SERVICE_DELETE_FAILED: new ErrorCode(4303, 'Lỗi khi xóa sản phẩm dịch vụ', HttpStatus.BAD_REQUEST),
  SERVICE_FIND_FAILED: new ErrorCode(4304, 'Lỗi khi tìm kiếm sản phẩm dịch vụ', HttpStatus.BAD_REQUEST),
  INVALID_PRODUCT_TYPE: new ErrorCode(4305, 'Loại sản phẩm không hợp lệ cho Service Product API', HttpStatus.BAD_REQUEST),

  // ========== SERVICE INFO VALIDATION (4320-4329) ==========
  INVALID_SERVICE_TYPE: new ErrorCode(4320, 'Loại hình dịch vụ không hợp lệ', HttpStatus.BAD_REQUEST),
  SERVICE_TYPE_REQUIRED: new ErrorCode(4321, 'Loại hình dịch vụ là bắt buộc', HttpStatus.BAD_REQUEST),
  INVALID_SERVICE_LOCATION: new ErrorCode(4322, 'Địa điểm dịch vụ không hợp lệ', HttpStatus.BAD_REQUEST),
  INVALID_PROVIDER_NAME: new ErrorCode(4323, 'Tên nhà cung cấp dịch vụ không hợp lệ', HttpStatus.BAD_REQUEST),
  PROVIDER_NAME_TOO_LONG: new ErrorCode(4324, 'Tên nhà cung cấp dịch vụ quá dài', HttpStatus.BAD_REQUEST),
  SERVICE_LOCATION_TOO_LONG: new ErrorCode(4325, 'Địa điểm dịch vụ quá dài', HttpStatus.BAD_REQUEST),

  // ========== SERVICE PACKAGE ERRORS (4330-4349) ==========
  PACKAGE_NOT_FOUND: new ErrorCode(4330, 'Không tìm thấy gói dịch vụ', HttpStatus.NOT_FOUND),
  PACKAGE_CREATION_FAILED: new ErrorCode(4331, 'Lỗi khi tạo gói dịch vụ', HttpStatus.BAD_REQUEST),
  PACKAGE_UPDATE_FAILED: new ErrorCode(4332, 'Lỗi khi cập nhật gói dịch vụ', HttpStatus.BAD_REQUEST),
  PACKAGE_DELETE_FAILED: new ErrorCode(4333, 'Lỗi khi xóa gói dịch vụ', HttpStatus.BAD_REQUEST),
  PACKAGE_FIND_FAILED: new ErrorCode(4334, 'Lỗi khi tìm kiếm gói dịch vụ', HttpStatus.BAD_REQUEST),

  // ========== PACKAGE VALIDATION (4340-4349) ==========
  PACKAGE_NAME_REQUIRED: new ErrorCode(4340, 'Tên gói dịch vụ là bắt buộc', HttpStatus.BAD_REQUEST),
  PACKAGE_NAME_TOO_LONG: new ErrorCode(4341, 'Tên gói dịch vụ quá dài', HttpStatus.BAD_REQUEST),
  INVALID_PACKAGE_PRICE: new ErrorCode(4342, 'Giá gói dịch vụ không hợp lệ', HttpStatus.BAD_REQUEST),
  INVALID_PACKAGE_DURATION: new ErrorCode(4343, 'Thời lượng gói dịch vụ không hợp lệ', HttpStatus.BAD_REQUEST),
  INVALID_PACKAGE_UNIT: new ErrorCode(4344, 'Đơn vị thời gian gói dịch vụ không hợp lệ', HttpStatus.BAD_REQUEST),
  INVALID_MAX_SALES: new ErrorCode(4345, 'Số lượng tối đa bán không hợp lệ', HttpStatus.BAD_REQUEST),
  PACKAGE_FEATURES_INVALID: new ErrorCode(4346, 'Danh sách tính năng gói dịch vụ không hợp lệ', HttpStatus.BAD_REQUEST),
  DUPLICATE_PACKAGE_NAME: new ErrorCode(4347, 'Tên gói dịch vụ đã tồn tại trong sản phẩm này', HttpStatus.CONFLICT),
  PACKAGE_DURATION_TOO_SHORT: new ErrorCode(4348, 'Thời lượng gói dịch vụ quá ngắn', HttpStatus.BAD_REQUEST),
  PACKAGE_DURATION_TOO_LONG: new ErrorCode(4349, 'Thời lượng gói dịch vụ quá dài', HttpStatus.BAD_REQUEST),

  // ========== PACKAGE OPERATIONS (4350-4359) ==========
  INVALID_PACKAGE_OPERATION: new ErrorCode(4350, 'Thao tác với gói dịch vụ không hợp lệ', HttpStatus.BAD_REQUEST),
  PACKAGE_OPERATION_NOT_ALLOWED: new ErrorCode(4351, 'Không được phép thực hiện thao tác này với gói dịch vụ', HttpStatus.FORBIDDEN),
  PACKAGE_ALREADY_SOLD: new ErrorCode(4352, 'Không thể xóa gói dịch vụ đã được bán', HttpStatus.BAD_REQUEST),
  PACKAGE_IN_USE: new ErrorCode(4353, 'Không thể xóa gói dịch vụ đang được sử dụng', HttpStatus.BAD_REQUEST),
  PACKAGE_LIMIT_EXCEEDED: new ErrorCode(4354, 'Số lượng gói dịch vụ vượt quá giới hạn cho phép', HttpStatus.BAD_REQUEST),
  PACKAGE_SALES_LIMIT_REACHED: new ErrorCode(4355, 'Gói dịch vụ đã đạt giới hạn số lần bán', HttpStatus.BAD_REQUEST),

  // ========== SERVICE AVAILABILITY (4360-4369) ==========
  SERVICE_NOT_AVAILABLE: new ErrorCode(4360, 'Dịch vụ hiện không khả dụng', HttpStatus.BAD_REQUEST),
  SERVICE_TEMPORARILY_UNAVAILABLE: new ErrorCode(4361, 'Dịch vụ tạm thời không khả dụng', HttpStatus.SERVICE_UNAVAILABLE),
  PROVIDER_NOT_AVAILABLE: new ErrorCode(4362, 'Nhà cung cấp dịch vụ hiện không khả dụng', HttpStatus.BAD_REQUEST),
  SERVICE_LOCATION_CLOSED: new ErrorCode(4363, 'Địa điểm dịch vụ đã đóng cửa', HttpStatus.BAD_REQUEST),
  SERVICE_CAPACITY_FULL: new ErrorCode(4364, 'Dịch vụ đã đầy, không thể đặt thêm', HttpStatus.BAD_REQUEST),

  // ========== SERVICE BOOKING (4370-4379) ==========
  BOOKING_NOT_ALLOWED: new ErrorCode(4370, 'Không được phép đặt dịch vụ này', HttpStatus.FORBIDDEN),
  BOOKING_TIME_INVALID: new ErrorCode(4371, 'Thời gian đặt dịch vụ không hợp lệ', HttpStatus.BAD_REQUEST),
  BOOKING_CONFLICT: new ErrorCode(4372, 'Thời gian đặt dịch vụ bị trung lặp', HttpStatus.CONFLICT),
  ADVANCE_BOOKING_REQUIRED: new ErrorCode(4373, 'Dịch vụ này yêu cầu đặt trước', HttpStatus.BAD_REQUEST),
  BOOKING_DEADLINE_PASSED: new ErrorCode(4374, 'Đã quá hạn đặt dịch vụ', HttpStatus.BAD_REQUEST),

  // ========== SERVICE QUALITY & CERTIFICATION (4380-4389) ==========
  CERTIFICATION_REQUIRED: new ErrorCode(4380, 'Dịch vụ này yêu cầu chứng chỉ', HttpStatus.BAD_REQUEST),
  CERTIFICATION_EXPIRED: new ErrorCode(4381, 'Chứng chỉ dịch vụ đã hết hạn', HttpStatus.BAD_REQUEST),
  QUALITY_STANDARD_NOT_MET: new ErrorCode(4382, 'Dịch vụ không đáp ứng tiêu chuẩn chất lượng', HttpStatus.BAD_REQUEST),
  PROVIDER_LICENSE_INVALID: new ErrorCode(4383, 'Giấy phép nhà cung cấp dịch vụ không hợp lệ', HttpStatus.BAD_REQUEST),

  // ========== INTEGRATION ERRORS (4390-4399) ==========
  BOOKING_SYSTEM_INTEGRATION_FAILED: new ErrorCode(4390, 'Lỗi tích hợp hệ thống đặt lịch', HttpStatus.INTERNAL_SERVER_ERROR),
  PAYMENT_INTEGRATION_FAILED: new ErrorCode(4391, 'Lỗi tích hợp thanh toán dịch vụ', HttpStatus.INTERNAL_SERVER_ERROR),
  NOTIFICATION_SEND_FAILED: new ErrorCode(4392, 'Lỗi gửi thông báo dịch vụ', HttpStatus.INTERNAL_SERVER_ERROR),
  CALENDAR_SYNC_FAILED: new ErrorCode(4393, 'Lỗi đồng bộ lịch dịch vụ', HttpStatus.INTERNAL_SERVER_ERROR),
  PROVIDER_API_FAILED: new ErrorCode(4394, 'Lỗi kết nối API nhà cung cấp', HttpStatus.INTERNAL_SERVER_ERROR),
  SERVICE_CONFIRMATION_FAILED: new ErrorCode(4395, 'Lỗi xác nhận dịch vụ', HttpStatus.INTERNAL_SERVER_ERROR),
} as const;

/**
 * Type helper để đảm bảo type safety khi sử dụng error codes
 */
export type ServiceProductErrorCode = typeof SERVICE_PRODUCT_ERROR_CODES[keyof typeof SERVICE_PRODUCT_ERROR_CODES];
