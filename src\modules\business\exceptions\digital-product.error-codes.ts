import { ErrorCode } from '@common/exceptions/app.exception';
import { HttpStatus } from '@nestjs/common';
import { CUSTOMER_PRODUCT_ERROR_CODES } from './customer-product.error-codes';

/**
 * Error codes cho Digital Product module
 * Range: 4100-4199 (extends customer product range 4000-4099)
 * 
 * Thừa kế tất cả error codes từ CUSTOMER_PRODUCT_ERROR_CODES
 * và thêm các error codes đặc biệt cho digital product
 */
export const DIGITAL_PRODUCT_ERROR_CODES = {
  // ========== THỪA KẾ TỪ CUSTOMER PRODUCT ==========
  ...CUSTOMER_PRODUCT_ERROR_CODES,

  // ========== DIGITAL PRODUCT SPECIFIC ERRORS (4100-4119) ==========
  DIGITAL_PRODUCT_INVALID_DELIVERY_METHOD: new ErrorCode(4100, '<PERSON><PERSON><PERSON><PERSON> thức giao hàng không hợp lệ', HttpStatus.BAD_REQUEST),
  DIGITAL_PRODUCT_INVALID_DELIVERY_TIME: new ErrorCode(4101, 'Thời gian giao hàng không hợp lệ', HttpStatus.BAD_REQUEST),
  DIGITAL_PRODUCT_INVALID_WAITING_TIME: new ErrorCode(4102, 'Thời gian chờ không hợp lệ', HttpStatus.BAD_REQUEST),
  DIGITAL_PRODUCT_CONTENT_LINK_REQUIRED: new ErrorCode(4103, 'Yêu cầu liên kết nội dung', HttpStatus.BAD_REQUEST),
  DIGITAL_PRODUCT_CONTENT_LINK_INVALID: new ErrorCode(4104, 'Liên kết nội dung không hợp lệ', HttpStatus.BAD_REQUEST),

  // ========== VERSION SPECIFIC ERRORS (4120-4139) ==========
  VERSION_NOT_FOUND: new ErrorCode(4120, 'Không tìm thấy phiên bản sản phẩm', HttpStatus.NOT_FOUND),
  VERSION_CREATION_FAILED: new ErrorCode(4121, 'Lỗi khi tạo phiên bản sản phẩm', HttpStatus.BAD_REQUEST),
  VERSION_UPDATE_FAILED: new ErrorCode(4122, 'Lỗi khi cập nhật phiên bản sản phẩm', HttpStatus.BAD_REQUEST),
  VERSION_DELETE_FAILED: new ErrorCode(4123, 'Lỗi khi xóa phiên bản sản phẩm', HttpStatus.BAD_REQUEST),
  VERSION_NAME_REQUIRED: new ErrorCode(4124, 'Yêu cầu tên phiên bản', HttpStatus.BAD_REQUEST),
  VERSION_NAME_DUPLICATE: new ErrorCode(4125, 'Tên phiên bản đã tồn tại trong sản phẩm này', HttpStatus.CONFLICT),
  VERSION_SKU_DUPLICATE: new ErrorCode(4126, 'SKU phiên bản đã tồn tại', HttpStatus.CONFLICT),
  VERSION_BARCODE_DUPLICATE: new ErrorCode(4127, 'Mã vạch phiên bản đã tồn tại', HttpStatus.CONFLICT),
  VERSION_CONTENT_LINK_DUPLICATE: new ErrorCode(4128, 'Liên kết nội dung đã tồn tại', HttpStatus.CONFLICT),
  VERSION_INVALID_QUANTITY_RANGE: new ErrorCode(4129, 'Khoảng số lượng không hợp lệ (min > max)', HttpStatus.BAD_REQUEST),
  VERSION_INVALID_MIN_QUANTITY: new ErrorCode(4130, 'Số lượng tối thiểu không hợp lệ', HttpStatus.BAD_REQUEST),
  VERSION_INVALID_MAX_QUANTITY: new ErrorCode(4131, 'Số lượng tối đa không hợp lệ', HttpStatus.BAD_REQUEST),

  // ========== VERSION OPERATION ERRORS (4140-4149) ==========
  VERSION_OPERATION_FAILED: new ErrorCode(4140, 'Lỗi thao tác phiên bản', HttpStatus.BAD_REQUEST),
  VERSION_ID_REQUIRED: new ErrorCode(4141, 'Yêu cầu ID phiên bản', HttpStatus.BAD_REQUEST),
  VERSION_DATA_REQUIRED: new ErrorCode(4142, 'Yêu cầu dữ liệu phiên bản', HttpStatus.BAD_REQUEST),
  VERSION_OPERATION_TYPE_INVALID: new ErrorCode(4143, 'Loại thao tác phiên bản không hợp lệ', HttpStatus.BAD_REQUEST),
  VERSION_UPDATE_ID_MISMATCH: new ErrorCode(4144, 'ID phiên bản không khớp khi cập nhật', HttpStatus.BAD_REQUEST),
  VERSION_DELETE_LAST_VERSION: new ErrorCode(4145, 'Không thể xóa phiên bản cuối cùng', HttpStatus.BAD_REQUEST),

  // ========== DIGITAL CONTENT ERRORS (4150-4159) ==========
  CONTENT_ACCESS_DENIED: new ErrorCode(4150, 'Không có quyền truy cập nội dung', HttpStatus.FORBIDDEN),
  CONTENT_NOT_AVAILABLE: new ErrorCode(4151, 'Nội dung không khả dụng', HttpStatus.NOT_FOUND),
  CONTENT_EXPIRED: new ErrorCode(4152, 'Nội dung đã hết hạn', HttpStatus.GONE),
  CONTENT_DOWNLOAD_LIMIT_EXCEEDED: new ErrorCode(4153, 'Vượt quá giới hạn tải xuống', HttpStatus.TOO_MANY_REQUESTS),
  CONTENT_SIZE_TOO_LARGE: new ErrorCode(4154, 'Kích thước nội dung quá lớn', HttpStatus.PAYLOAD_TOO_LARGE),
  CONTENT_FORMAT_UNSUPPORTED: new ErrorCode(4155, 'Định dạng nội dung không được hỗ trợ', HttpStatus.UNSUPPORTED_MEDIA_TYPE),

  // ========== DELIVERY ERRORS (4160-4169) ==========
  DELIVERY_METHOD_NOT_CONFIGURED: new ErrorCode(4160, 'Phương thức giao hàng chưa được cấu hình', HttpStatus.BAD_REQUEST),
  DELIVERY_FAILED: new ErrorCode(4161, 'Giao hàng thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  DELIVERY_EMAIL_INVALID: new ErrorCode(4162, 'Email giao hàng không hợp lệ', HttpStatus.BAD_REQUEST),
  DELIVERY_SCHEDULE_INVALID: new ErrorCode(4163, 'Lịch giao hàng không hợp lệ', HttpStatus.BAD_REQUEST),
  DELIVERY_RETRY_LIMIT_EXCEEDED: new ErrorCode(4164, 'Vượt quá giới hạn thử lại giao hàng', HttpStatus.TOO_MANY_REQUESTS),
  MEDIA_NOT_FOUND: new ErrorCode(4165, 'Không tìm thấy media trong kho', HttpStatus.NOT_FOUND),
  MEDIA_ACCESS_DENIED: new ErrorCode(4166, 'Không có quyền truy cập media', HttpStatus.FORBIDDEN),

  // ========== LICENSING ERRORS (4170-4179) ==========
  LICENSE_INVALID: new ErrorCode(4170, 'Giấy phép không hợp lệ', HttpStatus.BAD_REQUEST),
  LICENSE_EXPIRED: new ErrorCode(4171, 'Giấy phép đã hết hạn', HttpStatus.GONE),
  LICENSE_LIMIT_EXCEEDED: new ErrorCode(4172, 'Vượt quá giới hạn giấy phép', HttpStatus.TOO_MANY_REQUESTS),
  LICENSE_ACTIVATION_FAILED: new ErrorCode(4173, 'Kích hoạt giấy phép thất bại', HttpStatus.BAD_REQUEST),
  LICENSE_DEACTIVATION_FAILED: new ErrorCode(4174, 'Hủy kích hoạt giấy phép thất bại', HttpStatus.BAD_REQUEST),

  // ========== CUSTOM FIELDS ERRORS (4180-4189) ==========
  CUSTOM_FIELD_VALIDATION_FAILED: new ErrorCode(4180, 'Xác thực trường tùy chỉnh thất bại', HttpStatus.BAD_REQUEST),
  CUSTOM_FIELD_TYPE_MISMATCH: new ErrorCode(4181, 'Loại trường tùy chỉnh không khớp', HttpStatus.BAD_REQUEST),
  CUSTOM_FIELD_REQUIRED: new ErrorCode(4182, 'Trường tùy chỉnh bắt buộc', HttpStatus.BAD_REQUEST),
  CUSTOM_FIELD_FORMAT_INVALID: new ErrorCode(4183, 'Định dạng trường tùy chỉnh không hợp lệ', HttpStatus.BAD_REQUEST),

  // ========== INTEGRATION ERRORS (4190-4199) ==========
  EXTERNAL_SERVICE_UNAVAILABLE: new ErrorCode(4190, 'Dịch vụ bên ngoài không khả dụng', HttpStatus.SERVICE_UNAVAILABLE),
  EXTERNAL_API_ERROR: new ErrorCode(4191, 'Lỗi API bên ngoài', HttpStatus.BAD_GATEWAY),
  WEBHOOK_DELIVERY_FAILED: new ErrorCode(4192, 'Gửi webhook thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  THIRD_PARTY_AUTHENTICATION_FAILED: new ErrorCode(4193, 'Xác thực bên thứ ba thất bại', HttpStatus.UNAUTHORIZED),
  SYNC_FAILED: new ErrorCode(4194, 'Đồng bộ hóa thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
} as const;

/**
 * Type helper để đảm bảo type safety khi sử dụng error codes
 */
export type DigitalProductErrorCode = typeof DIGITAL_PRODUCT_ERROR_CODES[keyof typeof DIGITAL_PRODUCT_ERROR_CODES];
