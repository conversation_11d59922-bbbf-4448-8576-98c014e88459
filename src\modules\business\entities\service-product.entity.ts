import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng service_products trong cơ sở dữ liệu
 * Bảng lưu thông tin các sản phẩm dịch vụ
 */
@Entity('service_products')
export class ServiceProduct {
  /**
   * ID tự tăng, khóa chính của sản phẩm dịch vụ
   */
  @PrimaryColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Loại hình dịch vụ
   */
  @Column({
    name: 'service_type',
    type: 'varchar',
    length: 255,
    nullable: false,
    comment: 'Loại hình dịch vụ (ví dụ: spa, sửa chữa, tư vấn...)',
  })
  serviceType: string;

  /**
   * Đ<PERSON>a điểm thực hiện dịch vụ
   */
  @Column({
    name: 'location',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '<PERSON><PERSON><PERSON> điểm thực hiện dịch vụ (ví dụ: tạ<PERSON> nh<PERSON>, tại cửa hàng...)',
  })
  location: string | null;

  /**
   * Tên người thực hiện hoặc tổ chức cung cấp dịch vụ
   */
  @Column({
    name: 'provider_name',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Tên người thực hiện hoặc tổ chức cung cấp dịch vụ',
  })
  providerName: string | null;

  /**
   * Thời điểm tạo bản ghi
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời điểm tạo bản ghi (timestamp dạng epoch milliseconds)',
  })
  createdAt: number | null;

  /**
   * Thời điểm cập nhật bản ghi
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời điểm cập nhật bản ghi (timestamp dạng epoch milliseconds)',
  })
  updatedAt: number | null;
}
