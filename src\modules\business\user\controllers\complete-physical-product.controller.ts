import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Put,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto } from '@common/response';
import { CompletePhysicalProductService } from '../services/complete-physical-product.service';
import { CompleteUpdatePhysicalProductDto } from '../dto/physical-product/complete-update-physical-product.dto';
import { CompletePhysicalProductResponseDto } from '../dto/physical-product/complete-physical-product-response.dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý cập nhật hoàn chỉnh Physical Product
 * Bao gồm customer_products + physical_products + variants + images
 */
@ApiTags(SWAGGER_API_TAGS.USER_BUSINESS_VARIANT_PRODUCT)
@Controller('user/products/physical')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto,
  CompleteUpdatePhysicalProductDto,
  CompletePhysicalProductResponseDto,
)
export class CompletePhysicalProductController {
  constructor(
    private readonly completePhysicalProductService: CompletePhysicalProductService,
  ) {}

  /**
   * Cập nhật hoàn chỉnh sản phẩm vật lý
   * @param id ID của sản phẩm vật lý
   * @param updateDto DTO chứa toàn bộ thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm vật lý đã cập nhật
   */
  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cập nhật hoàn chỉnh sản phẩm vật lý',
    description: `Cập nhật toàn bộ thông tin sản phẩm vật lý bao gồm:
    - Thông tin cơ bản từ customer_products (name, description, price, tags, etc.)
    - Thông tin vật lý từ physical_products (stockQuantity, sku, barcode, shipmentConfig)
    - Quản lý variants thông qua operations (ADD/UPDATE/DELETE)
    - Quản lý images thông qua operations (ADD/DELETE)`,
  })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm vật lý cần cập nhật',
    example: 123,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật sản phẩm vật lý thành công',
    type: () => ApiResponseDto.getSchema(CompletePhysicalProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm vật lý',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Không có quyền truy cập',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền cập nhật sản phẩm này',
  })
  async updateComplete(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: CompleteUpdatePhysicalProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.completePhysicalProductService.updateCompletePhysicalProduct(
      id,
      updateDto,
      userId,
    );
    return ApiResponseDto.success<CompletePhysicalProductResponseDto>(
      product,
      'Cập nhật sản phẩm vật lý thành công',
    );
  }

  /**
   * Lấy chi tiết hoàn chỉnh sản phẩm vật lý
   * @param id ID của sản phẩm vật lý
   * @param userId ID của người dùng hiện tại
   * @returns Chi tiết hoàn chỉnh sản phẩm vật lý
   */
  @Get(':id/complete')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy chi tiết hoàn chỉnh sản phẩm vật lý',
    description: `Lấy thông tin chi tiết hoàn chỉnh sản phẩm vật lý bao gồm:
    - Thông tin cơ bản từ customer_products
    - Thông tin vật lý từ physical_products
    - Danh sách variants từ physical_product_variants
    - Danh sách images từ entity_has_media`,
  })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm vật lý',
    example: 123,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy chi tiết sản phẩm vật lý thành công',
    type: () => ApiResponseDto.getSchema(CompletePhysicalProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm vật lý',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Không có quyền truy cập',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền xem sản phẩm này',
  })
  async getCompleteDetail(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.completePhysicalProductService.getCompletePhysicalProduct(
      id,
      userId,
    );
    return ApiResponseDto.success<CompletePhysicalProductResponseDto>(
      product,
      'Lấy chi tiết sản phẩm vật lý thành công',
    );
  }
}
