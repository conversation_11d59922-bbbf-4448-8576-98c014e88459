import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventProduct } from '../entities/event-product.entity';

/**
 * Repository cho EventProduct entity
 * Xử lý các thao tác database cho sản phẩm sự kiện
 */
@Injectable()
export class EventProductRepository {
  private readonly logger = new Logger(EventProductRepository.name);

  constructor(
    @InjectRepository(EventProduct)
    private readonly repository: Repository<EventProduct>,
  ) {}

  /**
   * Tạo sản phẩm sự kiện mới
   * @param data Dữ liệu sản phẩm sự kiện
   * @returns Sản phẩm sự kiện đã tạo
   */
  async create(data: Partial<EventProduct>): Promise<EventProduct> {
    const eventProduct = this.repository.create(data);
    return this.repository.save(eventProduct);
  }

  /**
   * <PERSON><PERSON><PERSON> sản phẩm sự kiện theo ID
   * @param id ID sản phẩm sự kiện (cũng là customer product ID)
   * @returns Sản phẩm sự kiện hoặc null
   */
  async findById(id: number): Promise<EventProduct | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Cập nhật sản phẩm sự kiện
   * @param id ID sản phẩm sự kiện
   * @param data Dữ liệu cập nhật
   * @returns Sản phẩm sự kiện đã cập nhật
   */
  async update(id: number, data: Partial<EventProduct>): Promise<EventProduct | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa sản phẩm sự kiện
   * @param id ID sản phẩm sự kiện
   */
  async delete(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Lưu sản phẩm sự kiện
   * @param eventProduct Sản phẩm sự kiện cần lưu
   * @returns Sản phẩm sự kiện đã lưu
   */
  async save(eventProduct: EventProduct): Promise<EventProduct> {
    return this.repository.save(eventProduct);
  }

  /**
   * Tìm nhiều sản phẩm sự kiện theo danh sách ID
   * @param ids Danh sách ID sản phẩm sự kiện
   * @returns Danh sách sản phẩm sự kiện
   */
  async findByIds(ids: number[]): Promise<EventProduct[]> {
    if (ids.length === 0) {
      return [];
    }
    
    return this.repository.findByIds(ids);
  }

  /**
   * Kiểm tra sản phẩm sự kiện có tồn tại không
   * @param id ID sản phẩm sự kiện
   * @returns true nếu tồn tại
   */
  async exists(id: number): Promise<boolean> {
    const count = await this.repository.count({
      where: { id },
    });
    return count > 0;
  }

  /**
   * Tìm sản phẩm sự kiện theo khoảng thời gian
   * @param startDate Thời gian bắt đầu (epoch milliseconds)
   * @param endDate Thời gian kết thúc (epoch milliseconds)
   * @returns Danh sách sản phẩm sự kiện
   */
  async findByDateRange(startDate: number, endDate: number): Promise<EventProduct[]> {
    return this.repository
      .createQueryBuilder('event')
      .where('event.startDate >= :startDate', { startDate })
      .andWhere('event.startDate <= :endDate', { endDate })
      .getMany();
  }

  /**
   * Tìm sản phẩm sự kiện theo hình thức tham gia
   * @param participationType Hình thức tham gia
   * @returns Danh sách sản phẩm sự kiện
   */
  async findByParticipationType(participationType: string): Promise<EventProduct[]> {
    return this.repository.find({
      where: { participationType },
    });
  }

  /**
   * Tìm sản phẩm sự kiện đang diễn ra
   * @param currentTime Thời gian hiện tại (epoch milliseconds)
   * @returns Danh sách sản phẩm sự kiện đang diễn ra
   */
  async findOngoingEvents(currentTime: number): Promise<EventProduct[]> {
    return this.repository
      .createQueryBuilder('event')
      .where('event.startDate <= :currentTime', { currentTime })
      .andWhere('event.endDate >= :currentTime', { currentTime: new Date(currentTime) })
      .getMany();
  }

  /**
   * Tìm sản phẩm sự kiện sắp diễn ra
   * @param currentTime Thời gian hiện tại (epoch milliseconds)
   * @param hoursAhead Số giờ trước khi sự kiện bắt đầu
   * @returns Danh sách sản phẩm sự kiện sắp diễn ra
   */
  async findUpcomingEvents(currentTime: number, hoursAhead: number = 24): Promise<EventProduct[]> {
    const futureTime = currentTime + (hoursAhead * 60 * 60 * 1000);
    
    return this.repository
      .createQueryBuilder('event')
      .where('event.startDate > :currentTime', { currentTime })
      .andWhere('event.startDate <= :futureTime', { futureTime })
      .orderBy('event.startDate', 'ASC')
      .getMany();
  }

  /**
   * Đếm số lượng sản phẩm sự kiện theo hình thức tham gia
   * @param participationType Hình thức tham gia
   * @returns Số lượng sản phẩm sự kiện
   */
  async countByParticipationType(participationType: string): Promise<number> {
    return this.repository.count({
      where: { participationType },
    });
  }

  /**
   * Tìm sản phẩm sự kiện theo múi giờ
   * @param timeZone Múi giờ
   * @returns Danh sách sản phẩm sự kiện
   */
  async findByTimeZone(timeZone: string): Promise<EventProduct[]> {
    return this.repository.find({
      where: { timeZone },
    });
  }
}
