import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RuleContract } from './entities/rule-contract.entity';
import { BusinessInfo } from '@modules/user/entities/business-info.entity';
import { RuleContractRepository } from './repositories';
import { RuleContractAdminService } from './admin/services';
import { RuleContractUserService } from './user/services';
import { RuleContractAdminController } from './admin/controllers';
import { RuleContractUserController } from './user/controllers';
import { UserModule } from '@modules/user/user.module';
import { ServicesModule } from '@shared/services/services.module';
import { ContractHelperService } from './user/services/contract-helper.service';
import { SystemConfigurationModule } from '@modules/system-configuration/system-configuration.module';
import { EmailModule } from '@modules/email/email.module';
import { RuleContractStateService } from './state-machine/rule-contract-state.service';
import { RuleContractActionsService } from './state-machine/rule-contract-actions.service';

/**
 * Module quản lý hợp đồng nguyên tắc
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([RuleContract, BusinessInfo]),
    UserModule,
    ServicesModule,
    SystemConfigurationModule,
    EmailModule,
  ],
  controllers: [
    RuleContractAdminController,
    RuleContractUserController,
  ],
  providers: [
    RuleContractRepository,
    RuleContractAdminService,
    RuleContractUserService,
    ContractHelperService,
    RuleContractStateService,
    RuleContractActionsService,
  ],
  exports: [
    RuleContractRepository,
    RuleContractAdminService,
    RuleContractUserService,
    ContractHelperService,
    RuleContractStateService,
    RuleContractActionsService,
  ],
})
export class RuleContractModule {}
