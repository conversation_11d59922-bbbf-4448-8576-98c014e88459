import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsOptional } from 'class-validator';
import { QueryDto } from '@common/dto';
import { Transform } from 'class-transformer';

/**
 * Enum cho các trường sắp xếp
 */
export enum FacebookPageSortBy {
  CREATED_AT = 'created_at',
  PAGE_NAME = 'page_name',
  PERSONAL_NAME = 'personal_name',
}

/**
 * DTO cho truy vấn danh sách trang Facebook
 */
export class FacebookPageQueryDto extends QueryDto {
  // Override sortBy từ QueryDto để sử dụng enum
  @ApiProperty({
    description: 'Trường sắp xếp',
    required: false,
    enum: FacebookPageSortBy,
    default: FacebookPageSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(FacebookPageSortBy)
  sortBy?: FacebookPageSortBy = FacebookPageSortBy.CREATED_AT;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> theo trạng thái kết nối với agent',
    required: false,
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  isConnectAgent?: boolean;
}
