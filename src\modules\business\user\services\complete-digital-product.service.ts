import { Injectable, Logger } from '@nestjs/common';
import { CustomerProductRepository } from '@modules/business/repositories/customer-product.repository';
import { DigitalProductRepository } from '@modules/business/repositories/digital-product.repository';
import { DigitalProductVersionRepository } from '@modules/business/repositories/digital-product-version.repository';
import { EntityHasMediaRepository } from '@modules/business/repositories/entity-has-media.repository';
import { MediaRepository } from '@modules/data/media/repositories/media.repository';
import { CompleteUpdateDigitalProductDto } from '../dto/digital-product/complete-update-digital-product.dto';
import { CompleteDigitalProductResponseDto } from '../dto/digital-product/complete-digital-product-response.dto';
import { AppException } from '@common/exceptions/app.exception';
import { DIGITAL_PRODUCT_ERROR_CODES } from '@modules/business/exceptions/digital-product.error-codes';
import { Transactional } from 'typeorm-transactional';
import { CustomerProduct, DigitalProduct, DigitalProductVersion, EntityHasMedia } from '@modules/business/entities';
import { Media } from '@modules/data/media/entities/media.entity';
import { OwnerTypeEnum, MediaTypeEnum } from '@modules/data/media/enums';
import { EntityStatusEnum, ProductTypeEnum } from '@modules/business/enums';
import { ImageOperationType } from '../dto/image-operations/image-operation.dto';
import { VersionOperationType } from '../dto/digital-product/digital-product-version-operation.dto';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { generateS3Key, CategoryFolderEnum } from '@shared/utils/generators/s3-key-generator.util';
import { ImageTypeEnum } from '@shared/utils/file/image-media_type.util';
import { TimeIntervalEnum } from '@shared/utils/time/time-interval.util';
import { FileSizeEnum } from '@shared/utils/file/file-size.util';
import { IsNull } from 'typeorm';

/**
 * Service xử lý cập nhật hoàn chỉnh Digital Product
 * Bao gồm customer_products + digital_products + versions + images
 *
 * LOGIC PHÂN BIỆT ẢNH:
 * - Product level: product_id có giá trị, versionId = null
 * - Version level: versionId có giá trị (đây là version ID)
 * - Key và URL được lưu trong bảng media_data, entity_has_media chỉ lưu liên kết
 */
@Injectable()
export class CompleteDigitalProductService {
  private readonly logger = new Logger(CompleteDigitalProductService.name);

  constructor(
    private readonly customerProductRepository: CustomerProductRepository,
    private readonly digitalProductRepository: DigitalProductRepository,
    private readonly digitalProductVersionRepository: DigitalProductVersionRepository,
    private readonly entityHasMediaRepository: EntityHasMediaRepository,
    private readonly mediaRepository: MediaRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Cập nhật hoàn chỉnh digital product
   * @param id ID của sản phẩm
   * @param dto DTO chứa toàn bộ thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã cập nhật
   */
  @Transactional()
  async updateCompleteDigitalProduct(
    id: number,
    dto: CompleteUpdateDigitalProductDto,
    userId: number,
  ): Promise<CompleteDigitalProductResponseDto> {
    try {
      this.logger.log(`Cập nhật hoàn chỉnh digital product ID=${id} cho userId=${userId}`);

      // 1. Kiểm tra sản phẩm tồn tại và thuộc về user
      const existingProduct = await this.validateProductOwnership(id, userId);

      // 2. Cập nhật customer_products table
      const updatedCustomerProduct = await this.updateCustomerProduct(existingProduct, dto);

      // 3. Cập nhật/tạo digital_products table
      const digitalProduct = await this.updateOrCreateDigitalProduct(id, dto);

      // 4. Xử lý version operations (ADD/UPDATE/DELETE) và ảnh versions
      const versionResult = await this.processVersionOperations(id, dto.operations?.versions || [], userId);

      // 5. Xử lý image operations (ADD/DELETE) cho product level
      const imageResult = await this.processImageOperations(id, dto.operations?.images || [], userId);

      // 6. Tạo response hoàn chỉnh
      const response = await this.buildCompleteResponse(updatedCustomerProduct, digitalProduct, versionResult.versions);

      // ✅ NEW: Thêm operations với ảnh vào response
      if (versionResult.operationsWithImages && versionResult.operationsWithImages.length > 0) {
        (response as any).operations = {
          versions: versionResult.operationsWithImages,
          images: dto.operations?.images || []
        };
      }

      this.logger.log(`Cập nhật thành công digital product ID=${id}`);
      return response;

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi cập nhật digital product: ${error.message}`, error.stack);
      throw new AppException(
        DIGITAL_PRODUCT_ERROR_CODES.UPDATE_FAILED,
        `Lỗi khi cập nhật sản phẩm số: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết hoàn chỉnh digital product
   * @param id ID của sản phẩm
   * @param userId ID của người dùng hiện tại
   * @returns Chi tiết sản phẩm hoàn chỉnh
   */
  async getCompleteDigitalProduct(
    id: number,
    userId: number,
  ): Promise<CompleteDigitalProductResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết hoàn chỉnh digital product ID=${id} cho userId=${userId}`);

      // 1. Lấy customer product
      const customerProduct = await this.validateProductOwnership(id, userId);

      // 2. Lấy digital product data
      const digitalProduct = await this.digitalProductRepository.findById(id);

      // 3. Lấy versions data
      const versions = await this.digitalProductVersionRepository.findByDigitalProductId(id);

      // 4. Build response
      const response = await this.buildCompleteResponse(customerProduct, digitalProduct || undefined, versions);

      return response;

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi lấy chi tiết digital product: ${error.message}`, error.stack);
      throw new AppException(
        DIGITAL_PRODUCT_ERROR_CODES.FIND_FAILED,
        `Lỗi khi lấy chi tiết sản phẩm số: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra quyền sở hữu sản phẩm và validate productType
   */
  private async validateProductOwnership(id: number, userId: number): Promise<CustomerProduct> {
    const product = await this.customerProductRepository.findByIdAndUserId(id, userId);

    if (!product) {
      throw new AppException(
        DIGITAL_PRODUCT_ERROR_CODES.NOT_FOUND,
        'Không tìm thấy sản phẩm hoặc bạn không có quyền truy cập',
      );
    }

    // Kiểm tra productType phải là DIGITAL
    if (product.productType !== ProductTypeEnum.DIGITAL) {
      throw new AppException(
        DIGITAL_PRODUCT_ERROR_CODES.INVALID_PRODUCT_TYPE,
        `Sản phẩm này có loại '${product.productType}', không thể sử dụng API cập nhật Digital Product. Vui lòng sử dụng API phù hợp với loại sản phẩm.`,
      );
    }

    return product;
  }

  /**
   * Cập nhật customer_products table với cấu trúc nhóm mới
   */
  private async updateCustomerProduct(
    existingProduct: CustomerProduct,
    dto: CompleteUpdateDigitalProductDto,
  ): Promise<CustomerProduct> {
    const updateData: Partial<CustomerProduct> = {
      ...existingProduct,
      updatedAt: Date.now(),
      // Luôn đặt status là PENDING khi người dùng cập nhật sản phẩm
      status: EntityStatusEnum.PENDING,
    };

    // Cập nhật từ basicInfo
    if (dto.basicInfo) {
      if (dto.basicInfo.name !== undefined) updateData.name = dto.basicInfo.name.trim();
      if (dto.basicInfo.description !== undefined) updateData.description = dto.basicInfo.description?.trim() || null;
      if (dto.basicInfo.productType !== undefined) updateData.productType = dto.basicInfo.productType;
      if (dto.basicInfo.tags !== undefined) updateData.tags = dto.basicInfo.tags;
    }

    // Cập nhật từ pricing
    if (dto.pricing) {
      if (dto.pricing.price !== undefined) updateData.price = dto.pricing.price;
      if (dto.pricing.typePrice !== undefined) updateData.typePrice = dto.pricing.typePrice;
    }

    // Cập nhật custom fields
    if (dto.customFields !== undefined) {
      // Convert custom fields array thành object để lưu vào JSONB column
      const customFieldsObject = dto.customFields.reduce((acc, field) => {
        acc[field.customFieldId] = field.value;
        return acc;
      }, {} as any);
      updateData.customFields = customFieldsObject;
    }

    // Không cập nhật status từ DTO vì người dùng không có quyền thay đổi

    return await this.customerProductRepository.update(existingProduct.id, updateData);
  }

  /**
   * Cập nhật hoặc tạo digital_products record
   */
  private async updateOrCreateDigitalProduct(
    id: number,
    dto: CompleteUpdateDigitalProductDto,
  ): Promise<DigitalProduct> {
    // Kiểm tra xem digital product đã tồn tại chưa
    let digitalProduct = await this.digitalProductRepository.findById(id);

    if (digitalProduct) {
      // Cập nhật existing record từ digitalInfo
      const updateData: Partial<DigitalProduct> = {};
      if (dto.digitalInfo) {
        if (dto.digitalInfo.deliveryMethod !== undefined) updateData.deliveryMethod = dto.digitalInfo.deliveryMethod?.trim() || null;
        if (dto.digitalInfo.deliveryTime !== undefined) updateData.deliveryTime = dto.digitalInfo.deliveryTime?.trim() || null;
        if (dto.digitalInfo.waitingTime !== undefined) updateData.waitingTime = dto.digitalInfo.waitingTime?.trim() || null;
      }

      digitalProduct = await this.digitalProductRepository.update(id, updateData);
    } else {
      // Tạo mới digital product record từ digitalInfo
      const createData: Partial<DigitalProduct> = {
        id: id, // Same as customer product ID
        deliveryMethod: dto.digitalInfo?.deliveryMethod?.trim() || null,
        deliveryTime: dto.digitalInfo?.deliveryTime?.trim() || null,
        waitingTime: dto.digitalInfo?.waitingTime?.trim() || null,
      };

      digitalProduct = await this.digitalProductRepository.create(createData);
    }

    return digitalProduct!;
  }

  /**
   * Xử lý version operations (ADD/UPDATE/DELETE) và ảnh versions
   */
  private async processVersionOperations(
    digitalProductId: number,
    operations: any[],
    userId: number,
  ): Promise<{ versions: DigitalProductVersion[], versionImageUploadUrls: any[], operationsWithImages: any[] }> {
    const results: DigitalProductVersion[] = [];
    const allVersionImageUploadUrls: any[] = [];
    const operationsWithImages: any[] = [];

    for (const operation of operations) {
      try {
        switch (operation.operation) {
          case VersionOperationType.ADD:
            if (operation.data) {
              const newVersion = await this.digitalProductVersionRepository.create({
                digitalProductId,
                versionName: operation.data.versionName,
                description: operation.data.description || null,
                customFields: operation.data.customFields || null,
                contentLink: operation.data.contentLink?.trim() || null,
                sku: operation.data.sku?.trim() || null,
                barcode: operation.data.barcode?.trim() || null,
                minQuantity: operation.data.minQuantity || null,
                maxQuantity: operation.data.maxQuantity || null,
              });
              results.push(newVersion);
              this.logger.log(`Added new version: ${newVersion.versionName} (ID: ${newVersion.id})`);

              // ✅ NEW: Xử lý ảnh cho version mới tạo và load ảnh ngay vào data
              if (operation.data.imageOperations && operation.data.imageOperations.length > 0) {
                await this.processVersionImageOperations(
                  digitalProductId,
                  newVersion.id,
                  operation.data.imageOperations,
                  userId
                );
              }

              // ✅ NEW: Load ảnh ngay cho version này và gán vào operation
              const versionImages = await this.loadVersionImages(digitalProductId, newVersion.id);
              const operationWithImages = {
                ...operation,
                data: {
                  ...operation.data,
                  images: versionImages
                }
              };
              operationsWithImages.push(operationWithImages);
            }
            break;

          case VersionOperationType.UPDATE:
            if (operation.id && operation.data) {
              const updateData: Partial<DigitalProductVersion> = {};
              if (operation.data.versionName !== undefined) updateData.versionName = operation.data.versionName;
              if (operation.data.description !== undefined) updateData.description = operation.data.description || null;
              if (operation.data.customFields !== undefined) updateData.customFields = operation.data.customFields;
              if (operation.data.contentLink !== undefined) updateData.contentLink = operation.data.contentLink?.trim() || null;
              if (operation.data.sku !== undefined) updateData.sku = operation.data.sku?.trim() || null;
              if (operation.data.barcode !== undefined) updateData.barcode = operation.data.barcode?.trim() || null;
              if (operation.data.minQuantity !== undefined) updateData.minQuantity = operation.data.minQuantity;
              if (operation.data.maxQuantity !== undefined) updateData.maxQuantity = operation.data.maxQuantity;

              const updatedVersion = await this.digitalProductVersionRepository.update(operation.id, updateData);
              if (updatedVersion) {
                results.push(updatedVersion);
                this.logger.log(`Updated version ID: ${operation.id}`);

                // ✅ NEW: Xử lý ảnh cho version được update và load ảnh ngay vào data
                if (operation.data.imageOperations && operation.data.imageOperations.length > 0) {
                  await this.processVersionImageOperations(
                    digitalProductId,
                    operation.id,
                    operation.data.imageOperations,
                    userId
                  );
                }

                // ✅ NEW: Load ảnh ngay cho version này và gán vào operation
                const versionImages = await this.loadVersionImages(digitalProductId, operation.id);
                const operationWithImages = {
                  ...operation,
                  data: {
                    ...operation.data,
                    images: versionImages
                  }
                };
                operationsWithImages.push(operationWithImages);
              }
            }
            break;

          case VersionOperationType.DELETE:
            if (operation.id) {
              await this.digitalProductVersionRepository.delete(operation.id);
              this.logger.log(`Deleted version ID: ${operation.id}`);
              // DELETE operation không cần images
              operationsWithImages.push(operation);
            }
            break;

          default:
            this.logger.warn(`Unknown version operation: ${operation.operation}`);
        }
      } catch (error) {
        this.logger.error(`Error processing version operation: ${error.message}`, error.stack);
        // Continue with other operations instead of failing completely
      }
    }

    // Return all current versions for this digital product
    const allVersions = await this.digitalProductVersionRepository.findByDigitalProductId(digitalProductId);
    return {
      versions: allVersions,
      versionImageUploadUrls: [], // ✅ NEW: Không trả về upload URLs nữa
      operationsWithImages: operationsWithImages
    };
  }

  /**
   * Xử lý image operations (ADD/DELETE) cho product level
   */
  private async processImageOperations(
    digitalProductId: number,
    operations: any[],
    userId: number,
  ): Promise<{ uploadUrls: any[] }> {
    const uploadUrls: any[] = [];
    const timestamp = Date.now();

    for (const operation of operations) {
      try {
        switch (operation.operation) {
          case ImageOperationType.ADD:
            if (operation.mediaId) {
              // ✅ NEW: Lấy media từ kho media_data theo mediaId
              const existingMedia = await this.mediaRepository.findOneBy({ id: operation.mediaId });

              if (!existingMedia) {
                throw new AppException(
                  DIGITAL_PRODUCT_ERROR_CODES.MEDIA_NOT_FOUND,
                  `Media với ID ${operation.mediaId} không tồn tại trong kho media.`
                );
              }

              // Kiểm tra quyền sở hữu media (nếu cần)
              if (existingMedia.ownedBy !== userId) {
                throw new AppException(
                  DIGITAL_PRODUCT_ERROR_CODES.MEDIA_ACCESS_DENIED,
                  `Bạn không có quyền sử dụng media này.`
                );
              }

              // ✅ FIX: Tự động tính position cao nhất cho product images
              const nextPosition = await this.getNextPositionForProductImages(digitalProductId);

              // ✅ NEW: Tạo bản ghi trong entity_has_media để liên kết media với product
              const mediaLinkRecord: Partial<EntityHasMedia> = {
                productId: digitalProductId,  // Product level image
                versionId: null,              // Không phải version level
                physicalVarial: null,
                ticketVarial: null,
                productComboId: null,
                productPlanVarialId: null,
                mediaId: existingMedia.id, // UUID string từ media có sẵn
              };

              const createdMediaLink = await this.entityHasMediaRepository.create(mediaLinkRecord);

              this.logger.log(`Liên kết media có sẵn với sản phẩm số: ${existingMedia.storageKey} (Media ID: ${existingMedia.id})`);
            }
            break;

          case ImageOperationType.DELETE:
            if (operation.entityMediaId) {
              // ✅ NEW: Xóa theo entity_has_media.id - chính xác 100%
              const mediaLink = await this.entityHasMediaRepository.findById(operation.entityMediaId);

              if (!mediaLink) {
                this.logger.warn(`Không tìm thấy liên kết media với ID ${operation.entityMediaId}`);
                break;
              }

              // Kiểm tra quyền sở hữu - chỉ xóa media của product này
              if (mediaLink.productId !== digitalProductId) {
                this.logger.warn(`Liên kết media ${operation.entityMediaId} không thuộc về product ${digitalProductId}`);
                break;
              }

              await this.entityHasMediaRepository.delete(operation.entityMediaId);
              this.logger.log(`Đã xóa liên kết media product ID: ${operation.entityMediaId}`);

            } else if (operation.key) {
              // ⚠️ DEPRECATED: Backward compatibility - tìm theo key
              this.logger.warn(`⚠️ DEPRECATED: Sử dụng 'key' để xóa ảnh đã lỗi thời. Vui lòng sử dụng 'entityMediaId'.`);

              // Tìm media theo storage_key trong media_data, sau đó tìm liên kết
              const mediaRecords = await this.mediaRepository.find({
                where: { storageKey: operation.key }
              });

              if (mediaRecords.length === 0) {
                this.logger.warn(`Không tìm thấy media với key: ${operation.key}`);
                break;
              }

              // Xóa tất cả liên kết product-level của media này với product hiện tại
              for (const media of mediaRecords) {
                await this.entityHasMediaRepository.deleteSpecificMediaLink(
                  digitalProductId,
                  media.id,
                  undefined // product level
                );
              }

              this.logger.log(`Đã xóa liên kết media với key: ${operation.key} (deprecated method)`);
            }
            break;

          default:
            this.logger.warn(`Unknown image operation: ${operation.operation}`);
        }
      } catch (error) {
        this.logger.error(`Error processing image operation: ${error.message}`, error.stack);
        // Continue with other operations instead of failing completely
      }
    }

    return { uploadUrls: [] }; // ✅ NEW: Không trả về upload URLs nữa
  }

  /**
   * Xử lý image operations cho version level
   */
  private async processVersionImageOperations(
    digitalProductId: number,
    versionId: number,
    operations: any[],
    userId: number,
  ): Promise<{ uploadUrls: any[] }> {
    const uploadUrls: any[] = [];
    const timestamp = Date.now();

    for (const operation of operations) {
      try {
        switch (operation.operation) {
          case ImageOperationType.ADD:
            if (operation.mediaId) {
              // ✅ NEW: Lấy media từ kho media_data theo mediaId
              const existingMedia = await this.mediaRepository.findOneBy({ id: operation.mediaId });

              if (!existingMedia) {
                throw new AppException(
                  DIGITAL_PRODUCT_ERROR_CODES.MEDIA_NOT_FOUND,
                  `Media với ID ${operation.mediaId} không tồn tại trong kho media.`
                );
              }

              // Kiểm tra quyền sở hữu media (nếu cần)
              if (existingMedia.ownedBy !== userId) {
                throw new AppException(
                  DIGITAL_PRODUCT_ERROR_CODES.MEDIA_ACCESS_DENIED,
                  `Bạn không có quyền sử dụng media này.`
                );
              }

              // ✅ FIX: Tự động tính position cao nhất cho version images
              const nextPosition = await this.getNextPositionForVersionImages(digitalProductId, versionId);

              // ✅ NEW: Tạo bản ghi trong entity_has_media để liên kết media với version
              const mediaLinkRecord: Partial<EntityHasMedia> = {
                productId: digitalProductId,
                versionId: versionId,  // ← Version level image
                physicalVarial: null,
                ticketVarial: null,
                productComboId: null,
                productPlanVarialId: null,
                mediaId: existingMedia.id, // UUID string từ media có sẵn
              };

              const createdMediaLink = await this.entityHasMediaRepository.create(mediaLinkRecord);

              this.logger.log(`Liên kết media có sẵn với version ${versionId}: ${existingMedia.storageKey} (Media ID: ${existingMedia.id})`);
            }
            break;

          case ImageOperationType.DELETE:
            if (operation.entityMediaId) {
              // ✅ NEW: Xóa theo entity_has_media.id - chính xác 100%
              const mediaLink = await this.entityHasMediaRepository.findById(operation.entityMediaId);

              if (!mediaLink) {
                this.logger.warn(`Không tìm thấy liên kết media với ID ${operation.entityMediaId}`);
                break;
              }

              // Kiểm tra quyền sở hữu - chỉ xóa media của version này
              if (mediaLink.productId !== digitalProductId || mediaLink.versionId !== versionId) {
                this.logger.warn(`Liên kết media ${operation.entityMediaId} không thuộc về version ${versionId} của product ${digitalProductId}`);
                break;
              }

              await this.entityHasMediaRepository.delete(operation.entityMediaId);
              this.logger.log(`Đã xóa liên kết media version ID: ${operation.entityMediaId}`);

            } else if (operation.key) {
              // ⚠️ DEPRECATED: Backward compatibility
              this.logger.warn(`⚠️ DEPRECATED: Sử dụng 'key' để xóa ảnh version đã lỗi thời. Vui lòng sử dụng 'entityMediaId'.`);

              // Tìm media theo storage_key, sau đó xóa liên kết version-level
              const mediaRecords = await this.mediaRepository.find({
                where: { storageKey: operation.key }
              });

              if (mediaRecords.length === 0) {
                this.logger.warn(`Không tìm thấy media với key: ${operation.key}`);
                break;
              }

              // Xóa liên kết version-level của media này
              for (const media of mediaRecords) {
                // Tìm version-level media link cụ thể
                const versionMediaLink = await this.entityHasMediaRepository.findVersionLevelMedia(
                  digitalProductId,
                  versionId,
                  media.id
                );

                if (versionMediaLink) {
                  await this.entityHasMediaRepository.delete(versionMediaLink.id);
                }
              }

              this.logger.log(`Đã xóa liên kết media version với key: ${operation.key} (deprecated method)`);
            }
            break;

          default:
            this.logger.warn(`Unknown version image operation: ${operation.operation}`);
        }
      } catch (error) {
        this.logger.error(`Error processing version image operation: ${error.message}`, error.stack);
        // Continue with other operations instead of failing completely
      }
    }

    return { uploadUrls: [] }; // ✅ NEW: Không trả về upload URLs nữa
  }

  /**
   * Tạo response hoàn chỉnh
   */
  private async buildCompleteResponse(
    customerProduct: CustomerProduct,
    digitalProduct?: DigitalProduct,
    versions?: DigitalProductVersion[],
  ): Promise<CompleteDigitalProductResponseDto> {
    const response = new CompleteDigitalProductResponseDto();

    // Map customer product data
    response.id = customerProduct.id;
    response.name = customerProduct.name;
    response.description = customerProduct.description || undefined;
    response.productType = customerProduct.productType;
    response.price = customerProduct.price;
    response.typePrice = customerProduct.typePrice || undefined;
    response.tags = customerProduct.tags;
    response.status = customerProduct.status;

    // ✅ FIX: Convert customFields object back to array format for response
    if (customerProduct.customFields && typeof customerProduct.customFields === 'object') {
      response.customFields = Object.entries(customerProduct.customFields).map(([customFieldId, value]) => ({
        customFieldId: parseInt(customFieldId),
        value: value
      }));
    } else {
      response.customFields = undefined;
    }

    // ✅ FIX: Handle timestamp properly - convert from number to Date if needed
    response.createdAt = customerProduct.createdAt ?
      (typeof customerProduct.createdAt === 'number' ? new Date(customerProduct.createdAt) :
       typeof customerProduct.createdAt === 'string' ? new Date(parseInt(customerProduct.createdAt)) :
       customerProduct.createdAt) :
      null;
    response.updatedAt = customerProduct.updatedAt ?
      (typeof customerProduct.updatedAt === 'number' ? new Date(customerProduct.updatedAt) :
       typeof customerProduct.updatedAt === 'string' ? new Date(parseInt(customerProduct.updatedAt)) :
       customerProduct.updatedAt) :
      null;
    response.userId = customerProduct.userId || 0;

    // Map digital product data
    if (digitalProduct) {
      response.deliveryMethod = digitalProduct.deliveryMethod || undefined;
      response.deliveryTime = digitalProduct.deliveryTime || undefined;
      response.waitingTime = digitalProduct.waitingTime || undefined;
    }

    // Map versions data với images
    if (versions && versions.length > 0) {
      response.versions = await Promise.all(versions.map(async (version) => {
        const versionResponse: any = {
          id: version.id,
          digitalProductId: version.digitalProductId,
          versionName: version.versionName,
          description: version.description || undefined,
          customFields: version.customFields || undefined,
          contentLink: version.contentLink || undefined,
          sku: version.sku || undefined,
          barcode: version.barcode || undefined,
          minQuantity: version.minQuantity || undefined,
          maxQuantity: version.maxQuantity || undefined,
        };

        // Load version images
        const versionImages = await this.loadVersionImages(customerProduct.id, version.id);
        if (versionImages.length > 0) {
          versionResponse.images = versionImages;
        }

        return versionResponse;
      }));
    }

    // Load product level images
    const productImages = await this.loadProductImages(customerProduct.id);
    if (productImages.length > 0) {
      response.images = productImages;
    }

    return response;
  }

  /**
   * Load product level images
   */
  private async loadProductImages(productId: number): Promise<any[]> {
    try {
      // 1. Lấy tất cả media links cho product level (versionId = null)
      const mediaLinks = await this.entityHasMediaRepository.findByProductId(productId);
      const productLevelLinks = mediaLinks.filter(link =>
        link.productId &&
        !link.versionId &&  // product level
        !link.physicalVarial &&
        !link.ticketVarial &&
        !link.productComboId &&
        !link.productPlanVarialId &&
        link.mediaId
      );

      if (productLevelLinks.length === 0) {
        return [];
      }

      // 2. Lấy media records
      const mediaIds = productLevelLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      // 3. Build response với CDN URLs
      return await Promise.all(mediaRecords.map(async (media) => {
        let viewUrl = '';
        let position = 0;
        let mimeType = 'image/jpeg';

        // Tạo CDN view URL từ storageKey trong media_data
        if (media.storageKey) {
          try {
            const cdnUrl = this.cdnService.generateUrlView(media.storageKey, TimeIntervalEnum.ONE_DAY);
            viewUrl = cdnUrl || '';
          } catch (error) {
            this.logger.error(`Lỗi khi tạo CDN view URL cho key ${media.storageKey}: ${error.message}`);
            viewUrl = '';
          }
        }

        // ✅ FIX: Đọc position và mimeType từ media tags metadata
        if (media.tags && typeof media.tags === 'object') {
          position = media.tags.position || 0;
          mimeType = media.tags.mimeType || media.mediaType?.toString() || 'image/jpeg';
        } else {
          // Fallback to default values if tags is not available
          position = 0;
          mimeType = media.mediaType?.toString() || 'image/jpeg';
        }

        return {
          id: media.id,
          key: media.storageKey,
          position: position,
          url: viewUrl,
          mimeType: mimeType,
          name: media.name,
          size: media.size?.toString() || '0',
        };
      }));
    } catch (error) {
      this.logger.error(`Lỗi khi load product images: ${error.message}`, error.stack);
      return [];
    }
  }



  /**
   * ✅ FIX: Tự động tính position cao nhất cho product images
   */
  private async getNextPositionForProductImages(productId: number): Promise<number> {
    try {
      // 1. Lấy tất cả media links cho product level (versionId = null)
      const mediaLinks = await this.entityHasMediaRepository.findByProductId(productId);
      const productLevelLinks = mediaLinks.filter(link =>
        link.productId &&
        !link.versionId &&  // product level
        !link.physicalVarial &&
        !link.ticketVarial &&
        !link.productComboId &&
        !link.productPlanVarialId &&
        link.mediaId
      );

      if (productLevelLinks.length === 0) {
        return 1; // Bắt đầu từ position 1
      }

      // 2. Lấy tất cả media records để đọc position từ tags
      const mediaIds = productLevelLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return 1;
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      // 3. Tìm position cao nhất
      let maxPosition = 0;
      for (const media of mediaRecords) {
        if (media.tags && typeof media.tags === 'object' && media.tags.position) {
          const position = parseInt(media.tags.position.toString());
          if (!isNaN(position) && position > maxPosition) {
            maxPosition = position;
          }
        }
      }

      return maxPosition + 1;
    } catch (error) {
      this.logger.error(`Lỗi khi tính position cho product images: ${error.message}`);
      return 1; // Fallback
    }
  }

  /**
   * ✅ NEW: Load ảnh cho một version cụ thể
   */
  private async loadVersionImages(productId: number, versionId: number): Promise<Array<{
    key: string;
    mediaId: string;
    url: string;
  }>> {
    try {
      // 1. Lấy media links cho version này
      const mediaLinks = await this.entityHasMediaRepository.findByProductId(productId);
      const versionLinks = mediaLinks.filter(link =>
        link.productId &&
        link.versionId === versionId &&
        !link.physicalVarial &&
        !link.ticketVarial &&
        !link.productComboId &&
        !link.productPlanVarialId &&
        link.mediaId
      );

      if (versionLinks.length === 0) {
        return []; // Không có ảnh
      }

      // 2. Lấy media records
      const mediaIds = versionLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      // 3. Tạo response format theo yêu cầu
      const images: Array<{
        key: string;
        mediaId: string;
        url: string;
      }> = [];
      for (const mediaRecord of mediaRecords) {
        if (!mediaRecord) continue;

        let viewUrl = '';

        // Tạo CDN view URL
        if (mediaRecord.storageKey) {
          try {
            const cdnUrl = this.cdnService.generateUrlView(mediaRecord.storageKey, TimeIntervalEnum.ONE_DAY);
            viewUrl = cdnUrl || '';
          } catch (error) {
            this.logger.error(`Lỗi khi tạo CDN view URL cho version image ${mediaRecord.storageKey}: ${error.message}`);
            viewUrl = '';
          }
        }

        images.push({
          key: mediaRecord.storageKey || '',
          mediaId: mediaRecord.id,
          url: viewUrl,
        });
      }

      return images;
    } catch (error) {
      this.logger.error(`Lỗi khi load version images cho version ${versionId}: ${error.message}`);
      return []; // Trả về empty array nếu có lỗi
    }
  }

  /**
   * ✅ FIX: Tự động tính position cao nhất cho version images
   */
  private async getNextPositionForVersionImages(productId: number, versionId: number): Promise<number> {
    try {
      // 1. Lấy tất cả media links cho version level (versionId = versionId)
      const mediaLinks = await this.entityHasMediaRepository.findByProductId(productId);
      const versionLevelLinks = mediaLinks.filter(link =>
        link.productId &&
        link.versionId === versionId &&  // version level
        !link.physicalVarial &&
        !link.ticketVarial &&
        !link.productComboId &&
        !link.productPlanVarialId &&
        link.mediaId
      );

      if (versionLevelLinks.length === 0) {
        return 1; // Bắt đầu từ position 1
      }

      // 2. Lấy tất cả media records để đọc position từ tags
      const mediaIds = versionLevelLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return 1;
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      // 3. Tìm position cao nhất
      let maxPosition = 0;
      for (const media of mediaRecords) {
        if (media.tags && typeof media.tags === 'object' && media.tags.position) {
          const position = parseInt(media.tags.position.toString());
          if (!isNaN(position) && position > maxPosition) {
            maxPosition = position;
          }
        }
      }

      return maxPosition + 1;
    } catch (error) {
      this.logger.error(`Lỗi khi tính position cho version images: ${error.message}`);
      return 1; // Fallback
    }
  }
}
