# Agent Template Memories Implementation

## Tổng quan

Tài liệu này mô tả việc triển khai logic xử lý memories cho Agent Template trong hệ thống NestJS.

## Những thay đổi đã thực hiện

### 1. <PERSON><PERSON><PERSON> nhật AdminAgentTemplateService

#### 1.1 Thêm dependencies
- Thêm `AgentMemoriesRepository` vào constructor
- Import `AgentMemories` entity

#### 1.2 Logic Create Agent Template
```typescript
// Lưu memories nếu có
if (createDto.memories && createDto.memories.length > 0) {
  const memoriesData: Partial<AgentMemories>[] = createDto.memories.map(memory => ({
    agentId: savedAgent.id,
    structuredContent: memory,
    createdAt: Date.now(),
  }));

  await this.agentMemoriesRepository.save(memoriesData);
  this.logger.log(`Saved ${memoriesData.length} memories for agent template: ${savedAgent.id}`);
}
```

#### 1.3 Logic Update Agent Template
```typescript
// Cập nhật memories nếu có
if (updateDto.memories !== undefined) {
  // Xóa tất cả memories cũ
  await this.agentMemoriesRepository.deleteByAgentId(id);
  
  // Thêm memories mới nếu có
  if (updateDto.memories.length > 0) {
    const memoriesData: Partial<AgentMemories>[] = updateDto.memories.map(memory => ({
      agentId: id,
      structuredContent: memory,
      createdAt: Date.now(),
    }));

    await this.agentMemoriesRepository.save(memoriesData);
    this.logger.log(`Updated ${memoriesData.length} memories for agent template: ${id}`);
  } else {
    this.logger.log(`Cleared all memories for agent template: ${id}`);
  }
}
```

#### 1.4 Logic Soft Delete Agent Template
```typescript
// Xóa tất cả memories của agent
const deletedMemoriesCount = await this.agentMemoriesRepository.deleteByAgentId(id);
if (deletedMemoriesCount > 0) {
  this.logger.log(`Deleted ${deletedMemoriesCount} memories for agent template: ${id}`);
}
```

#### 1.5 Logic Get Detail Agent Template
```typescript
// Lấy memories của agent
const memories = await this.agentMemoriesRepository.findByAgentId(template.id);

// Map memories to DTO
dto.memories = memories.map(memory => ({
  title: memory.structuredContent.title || '',
  reason: memory.structuredContent.reason || '',
  content: memory.structuredContent.content,
}));
```

### 2. Cập nhật Module Configuration

#### 2.1 Agent Module (agent.module.ts)
- Thêm `entities.AgentMemories` vào TypeOrmModule.forFeature

#### 2.2 Agent Admin Module (agent-admin.module.ts)
- Import `AgentMemoriesRepository`
- Thêm `AgentMemoriesRepository` vào providers

### 3. Sửa lại AgentMemoriesRepository

#### 3.1 Loại bỏ leftJoinAndSelect không cần thiết
```typescript
private createBaseQuery(): SelectQueryBuilder<AgentMemories> {
  return this.createQueryBuilder('agentMemories')
    .where('agentMemories.id IS NOT NULL');
}
```

### 4. Cấu trúc dữ liệu

#### 4.1 StructuredContentInterface
```typescript
export interface StructuredContentInterface {
  title?: string;
  reason?: string;
  content: string;
}
```

#### 4.2 AgentMemoryDto
```typescript
export class AgentMemoryDto {
  title: string;    // Bắt buộc
  reason: string;   // Bắt buộc
  content: string;  // Bắt buộc
}
```

#### 4.3 AgentMemories Entity
```typescript
@Entity('agent_memories')
export class AgentMemories {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'agent_id', type: 'uuid', nullable: false })
  agentId: string;

  @Column({
    name: 'structured_content',
    type: 'jsonb',
    nullable: false,
  })
  structuredContent: StructuredContentInterface;

  @Column({
    name: 'metadata',
    type: 'jsonb',
    nullable: true,
  })
  metadata?: Record<string, any>;

  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    nullable: true,
  })
  createdAt?: number;
}
```

## Luồng xử lý

### 1. Tạo Agent Template
1. Validate type agent
2. Tạo agent record
3. Tạo agent template record
4. **Lưu memories nếu có**
5. Tạo avatar upload URL nếu cần
6. Return response

### 2. Cập nhật Agent Template
1. Validate template tồn tại
2. Validate type agent nếu có thay đổi
3. Cập nhật agent record
4. Cập nhật agent template record
5. **Cập nhật memories (xóa cũ, thêm mới)**
6. Tạo avatar upload URL nếu cần
7. Return response

### 3. Lấy chi tiết Agent Template
1. Lấy template với details
2. **Lấy memories từ database**
3. Map employee info
4. **Map memories to DTO format**
5. Return response

### 4. Xóa mềm Agent Template
1. Validate template tồn tại
2. Soft delete agent template
3. Soft delete agent
4. **Xóa tất cả memories**
5. Return response

## Testing

Đã tạo test file `admin-agent-template-memories.test.ts` để kiểm tra:
- Logic lưu memories khi tạo agent template
- Logic không lưu memories khi không có dữ liệu
- Logic lấy memories trong detail response

## Sửa lỗi Validation

### Vấn đề gặp phải
```json
{
  "code": 9003,
  "message": "memories (3 lỗi)",
  "detail": {
    "summary": "memories (3 lỗi)",
    "details": [
      {
        "field": "memories.0.title",
        "constraints": [
          {
            "constraint": "whitelistValidation",
            "message": "property title should not exist"
          }
        ]
      }
    ]
  }
}
```

### Nguyên nhân
- Thiếu `@Type(() => AgentMemoryDto)` decorator trong CreateAgentTemplateDto
- Sử dụng `StructuredContentInterface[]` thay vì `AgentMemoryDto[]`
- Whitelist validation block các properties của nested object

### Giải pháp đã áp dụng

#### 1. Cập nhật CreateAgentTemplateDto
```typescript
// Trước
@ValidateNested({ each: true })
memories?: StructuredContentInterface[];

// Sau
@ValidateNested({ each: true })
@Type(() => AgentMemoryDto)
memories?: AgentMemoryDto[];
```

#### 2. Cập nhật Service Logic
```typescript
// Mapping từ AgentMemoryDto sang StructuredContentInterface
const memoriesData: Partial<AgentMemories>[] = createDto.memories.map(memory => ({
  agentId: savedAgent.id,
  structuredContent: {
    title: memory.title,
    reason: memory.reason,
    content: memory.content,
  },
  createdAt: Date.now(),
}));
```

## Lưu ý quan trọng

1. **DTO Validation**: Sử dụng `@Type()` decorator cho nested objects để class-transformer có thể transform đúng.

2. **Mapping dữ liệu**: Map từ AgentMemoryDto (input) sang StructuredContentInterface (database storage).

3. **Transaction**: Tất cả operations đều được wrap trong @Transactional() để đảm bảo data consistency.

4. **Logging**: Thêm logging để track việc lưu/xóa memories.

5. **Performance**: Sử dụng bulk operations cho việc lưu nhiều memories cùng lúc.

6. **Data cleanup**: Khi xóa agent template, tất cả memories liên quan cũng được xóa để tránh orphaned data.

## Test Request Body
```json
{
  "name": "Test Agent Template",
  "typeId": 1,
  "modelConfig": {
    "temperature": 0.7,
    "maxTokens": 1000
  },
  "instruction": "You are a helpful assistant",
  "memories": [
    {
      "title": "Programming Knowledge",
      "reason": "To help with coding questions",
      "content": "I have extensive knowledge in JavaScript, Python, and TypeScript"
    },
    {
      "title": "Communication Style",
      "reason": "To maintain consistent personality",
      "content": "Always be polite, helpful, and provide clear explanations"
    }
  ]
}
```
