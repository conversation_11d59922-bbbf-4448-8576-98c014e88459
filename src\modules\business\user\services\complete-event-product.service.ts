import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { AppException } from '@common/exceptions';
import { EVENT_PRODUCT_ERROR_CODES } from '@modules/business/exceptions';
import { CustomerProduct, EventProduct, EventProductTicket } from '@modules/business/entities';
import { CustomFieldInterface } from '@modules/business/interfaces/custom-field.interface';
import { ProductTypeEnum, ParticipationTypeEnum } from '@modules/business/enums';
import {
  CustomerProductRepository,
  EventProductRepository,
  EventProductTicketRepository,
  EntityHasMediaRepository,
} from '@modules/business/repositories';
import { MediaRepository } from '@modules/data/media/repositories';
import { CompleteUpdateEventProductDto } from '@modules/business/user/dto/event-product';
import { CompleteEventProductResponseDto } from '@modules/business/user/dto/event-product';
import { EventProductTicketOperationDto, TicketOperationType } from '@modules/business/user/dto/event-product';

/**
 * Service xử lý các thao tác hoàn chỉnh với Event Product
 * Bao gồm customer_products + event_products + tickets + images
 */
@Injectable()
export class CompleteEventProductService {
  private readonly logger = new Logger(CompleteEventProductService.name);

  constructor(
    private readonly customerProductRepository: CustomerProductRepository,
    private readonly eventProductRepository: EventProductRepository,
    private readonly eventProductTicketRepository: EventProductTicketRepository,
    private readonly entityHasMediaRepository: EntityHasMediaRepository,
    private readonly mediaRepository: MediaRepository,
  ) {}

  /**
   * Cập nhật hoàn chỉnh event product
   * @param id ID của sản phẩm
   * @param dto DTO chứa toàn bộ thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã cập nhật
   */
  @Transactional()
  async updateCompleteEventProduct(
    id: number,
    dto: CompleteUpdateEventProductDto,
    userId: number,
  ): Promise<CompleteEventProductResponseDto> {
    try {
      this.logger.log(`Cập nhật hoàn chỉnh event product ID=${id} cho userId=${userId}`);

      // 1. Kiểm tra sản phẩm tồn tại và thuộc về user
      const existingProduct = await this.validateProductOwnership(id, userId);

      // 2. Cập nhật customer_products table
      const updatedCustomerProduct = await this.updateCustomerProduct(existingProduct, dto);

      // 3. Cập nhật/tạo event_products table
      const eventProduct = await this.updateOrCreateEventProduct(id, dto);

      // 4. Xử lý ticket operations (ADD/UPDATE/DELETE) và ảnh tickets
      const ticketResult = await this.processTicketOperations(id, dto.operations?.tickets || []);

      // 5. Xử lý image operations (ADD/DELETE) cho product level
      await this.processImageOperations(id, dto.operations?.images || []);

      // 6. Tạo response hoàn chỉnh
      return await this.buildCompleteResponse(updatedCustomerProduct, eventProduct, ticketResult.tickets);

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi cập nhật event product: ${error.message}`, error.stack);
      throw new AppException(
        EVENT_PRODUCT_ERROR_CODES.UPDATE_FAILED,
        `Lỗi khi cập nhật sản phẩm sự kiện: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết hoàn chỉnh event product
   * @param id ID của sản phẩm
   * @param userId ID của người dùng hiện tại
   * @returns Chi tiết sản phẩm hoàn chỉnh
   */
  async getCompleteEventProduct(
    id: number,
    userId: number,
  ): Promise<CompleteEventProductResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết hoàn chỉnh event product ID=${id} cho userId=${userId}`);

      // 1. Lấy customer product
      const customerProduct = await this.validateProductOwnership(id, userId);

      // 2. Lấy event product data
      const eventProduct = await this.eventProductRepository.findById(id);

      // 3. Lấy tickets data
      const tickets = await this.eventProductTicketRepository.findByEventProductId(id);

      // 4. Build response
      const response = await this.buildCompleteResponse(customerProduct, eventProduct || undefined, tickets);

      return response;

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi lấy chi tiết event product: ${error.message}`, error.stack);
      throw new AppException(
        EVENT_PRODUCT_ERROR_CODES.FIND_FAILED,
        `Lỗi khi lấy chi tiết sản phẩm sự kiện: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra quyền sở hữu sản phẩm và validate productType
   */
  private async validateProductOwnership(id: number, userId: number): Promise<CustomerProduct> {
    const product = await this.customerProductRepository.findByIdAndUserId(id, userId);

    if (!product) {
      throw new AppException(
        EVENT_PRODUCT_ERROR_CODES.NOT_FOUND,
        'Không tìm thấy sản phẩm hoặc bạn không có quyền truy cập',
      );
    }

    // Kiểm tra productType phải là EVENT
    if (product.productType !== ProductTypeEnum.EVENT) {
      throw new AppException(
        EVENT_PRODUCT_ERROR_CODES.INVALID_PRODUCT_TYPE,
        `Sản phẩm này có loại '${product.productType}', không thể sử dụng API cập nhật Event Product. Vui lòng sử dụng API phù hợp với loại sản phẩm.`,
      );
    }

    return product;
  }

  /**
   * Cập nhật customer_products table
   */
  private async updateCustomerProduct(
    existingProduct: CustomerProduct,
    dto: CompleteUpdateEventProductDto,
  ): Promise<CustomerProduct> {
    const updateData: Partial<CustomerProduct> = {};

    // Basic info updates
    if (dto.basicInfo?.name) {
      updateData.name = dto.basicInfo.name;
    }
    if (dto.basicInfo?.description !== undefined) {
      updateData.description = dto.basicInfo.description;
    }
    if (dto.basicInfo?.tags) {
      updateData.tags = dto.basicInfo.tags;
    }
    // ❌ REMOVED: Không cho phép user update status
    // if (dto.basicInfo?.status) {
    //   updateData.status = dto.basicInfo.status;
    // }

    // Pricing updates
    if (dto.pricing?.price) {
      updateData.price = dto.pricing.price;
    }
    if (dto.pricing?.typePrice) {
      updateData.typePrice = dto.pricing.typePrice;
    }

    // Custom fields updates
    if (dto.customFields) {
      updateData.customFields = dto.customFields as unknown as CustomFieldInterface;
    }

    // Update if there are changes
    if (Object.keys(updateData).length > 0) {
      const updated = await this.customerProductRepository.update(existingProduct.id, updateData);
      return updated || existingProduct;
    }

    return existingProduct;
  }

  /**
   * Cập nhật/tạo event_products table
   */
  private async updateOrCreateEventProduct(
    id: number,
    dto: CompleteUpdateEventProductDto,
  ): Promise<EventProduct> {
    let eventProduct = await this.eventProductRepository.findById(id);

    if (eventProduct) {
      // Update existing event product
      const updateData: Partial<EventProduct> = {};

      if (dto.eventInfo?.participationType) {
        updateData.participationType = dto.eventInfo.participationType;
      }
      if (dto.eventInfo?.location !== undefined) {
        updateData.location = dto.eventInfo.location;
      }
      if (dto.eventInfo?.participationUrl !== undefined) {
        updateData.participationUrl = dto.eventInfo.participationUrl;
      }
      if (dto.eventInfo?.startDate) {
        updateData.startDate = dto.eventInfo.startDate;
      }
      if (dto.eventInfo?.endDate) {
        updateData.endDate = dto.eventInfo.endDate;
      }
      if (dto.eventInfo?.timeZone) {
        updateData.timeZone = dto.eventInfo.timeZone;
      }

      const updated = await this.eventProductRepository.update(id, updateData);
      if (!updated) {
        throw new AppException(EVENT_PRODUCT_ERROR_CODES.UPDATE_FAILED);
      }
      eventProduct = updated;
    } else {
      // Tạo mới event product record từ eventInfo
      const createData: Partial<EventProduct> = {
        id: id, // Same as customer product ID
        participationType: dto.eventInfo?.participationType || ParticipationTypeEnum.ONLINE,
        location: dto.eventInfo?.location || null,
        participationUrl: dto.eventInfo?.participationUrl || null,
        startDate: dto.eventInfo?.startDate || Date.now(),
        endDate: dto.eventInfo?.endDate || new Date(Date.now() + 3600000), // +1 hour default
        timeZone: dto.eventInfo?.timeZone || 'Asia/Ho_Chi_Minh',
      };

      eventProduct = await this.eventProductRepository.create(createData);
    }

    return eventProduct;
  }

  /**
   * Xử lý ticket operations (ADD/UPDATE/DELETE)
   */
  private async processTicketOperations(
    eventProductId: number,
    operations: EventProductTicketOperationDto[],
  ): Promise<{ tickets: EventProductTicket[] }> {
    for (const operation of operations) {
      switch (operation.operation) {
        case TicketOperationType.ADD:
          if (operation.data) {
            const newTicket = await this.eventProductTicketRepository.create({
              eventProductId,
              name: operation.data.name || '',
              price: operation.data.price || 0,
              totalQuantity: operation.data.totalQuantity || 1,
              description: operation.data.description || null,
              saleStart: operation.data.saleStart || Date.now(),
              saleEnd: operation.data.saleEnd || Date.now() + 86400000, // +1 day
              timeZone: operation.data.timeZone || 'Asia/Ho_Chi_Minh',
              sku: operation.data.sku || null,
              minQuantityPerOrder: operation.data.minQuantityPerOrder || 1,
              maxQuantityPerOrder: operation.data.maxQuantityPerOrder || null,
            });

            // Process image operations for this ticket
            if (operation.data.imageOperations && operation.data.imageOperations.length > 0) {
              this.logger.log(`Processing ${operation.data.imageOperations.length} image operations for new ticket ID=${newTicket.id}`);
              await this.processTicketImageOperations(newTicket.id, operation.data.imageOperations);
            }
          }
          break;

        case TicketOperationType.UPDATE:
          if (operation.id && operation.data) {
            const updateData: Partial<EventProductTicket> = {};

            if (operation.data.name) updateData.name = operation.data.name;
            if (operation.data.price !== undefined) updateData.price = operation.data.price;
            if (operation.data.totalQuantity !== undefined) updateData.totalQuantity = operation.data.totalQuantity;
            if (operation.data.description !== undefined) updateData.description = operation.data.description;
            if (operation.data.saleStart !== undefined) updateData.saleStart = operation.data.saleStart;
            if (operation.data.saleEnd !== undefined) updateData.saleEnd = operation.data.saleEnd;
            if (operation.data.timeZone) updateData.timeZone = operation.data.timeZone;
            if (operation.data.sku !== undefined) updateData.sku = operation.data.sku;
            if (operation.data.minQuantityPerOrder !== undefined) updateData.minQuantityPerOrder = operation.data.minQuantityPerOrder;
            if (operation.data.maxQuantityPerOrder !== undefined) updateData.maxQuantityPerOrder = operation.data.maxQuantityPerOrder;

            await this.eventProductTicketRepository.update(operation.id, updateData);

            // Process image operations for this ticket
            if (operation.data.imageOperations && operation.data.imageOperations.length > 0) {
              await this.processTicketImageOperations(operation.id, operation.data.imageOperations);
            }
          }
          break;

        case TicketOperationType.DELETE:
          if (operation.id) {
            await this.eventProductTicketRepository.delete(operation.id);
          }
          break;
      }
    }

    // Return all current tickets for this event product
    const allTickets = await this.eventProductTicketRepository.findByEventProductId(eventProductId);
    return { tickets: allTickets };
  }

  /**
   * Xử lý image operations (ADD/DELETE) cho product level
   */
  private async processImageOperations(
    eventProductId: number,
    operations: Array<{ operation: 'add' | 'delete'; mediaId?: string; entityHasMediaId?: number }>,
  ): Promise<void> {
    for (const operation of operations) {
      switch (operation.operation) {
        case 'add':
          if (operation.mediaId) {
            // Create entity_has_media link for product level
            await this.entityHasMediaRepository.create({
              productId: eventProductId,
              mediaId: operation.mediaId,
              physicalVarial: null,
              ticketVarial: null, // null = product level image
              versionId: null,
              productComboId: null,
              productPlanVarialId: null,
            });
          }
          break;

        case 'delete':
          if (operation.entityHasMediaId) {
            // Delete entity_has_media record by ID
            await this.entityHasMediaRepository.delete(operation.entityHasMediaId);
          }
          break;
      }
    }
  }

  /**
   * Xử lý image operations cho ticket level
   */
  private async processTicketImageOperations(
    ticketId: number,
    operations: Array<{ operation: 'add' | 'delete'; mediaId?: string; entityHasMediaId?: number }>,
  ): Promise<void> {
    for (const operation of operations) {
      switch (operation.operation) {
        case 'add':
          if (operation.mediaId) {
            this.logger.log(`Creating ticket image link: ticketId=${ticketId}, mediaId=${operation.mediaId}`);

            // ✅ Validate media tồn tại trước khi tạo link
            const mediaExists = await this.mediaRepository.findByIds([operation.mediaId]);
            if (mediaExists.length === 0) {
              this.logger.warn(`Media ID ${operation.mediaId} không tồn tại, bỏ qua tạo link`);
              continue;
            }

            // Create entity_has_media link for ticket level
            const createdLink = await this.entityHasMediaRepository.create({
              productId: null, // Ticket level không cần productId
              mediaId: operation.mediaId,
              physicalVarial: null,
              ticketVarial: ticketId, // ticketId = ticket level image
              versionId: null,
              productComboId: null,
              productPlanVarialId: null,
            });
            this.logger.log(`Created ticket image link: ID=${createdLink.id}`);
          }
          break;

        case 'delete':
          if (operation.entityHasMediaId) {
            // Delete entity_has_media record by ID
            await this.entityHasMediaRepository.delete(operation.entityHasMediaId);
          }
          break;
      }
    }
  }

  /**
   * Load images cho ticket
   */
  private async loadTicketImages(ticketId: number): Promise<Record<string, unknown>[]> {
    try {
      this.logger.log(`Loading images for ticket ID=${ticketId}`);

      // Tìm entity_has_media records cho ticket này
      const ticketLevelLinks = await this.entityHasMediaRepository.findByTicketVarial(ticketId);
      this.logger.log(`Found ${ticketLevelLinks.length} media links for ticket ${ticketId}`);

      // Lấy danh sách mediaId để query media_data
      const mediaIds = ticketLevelLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      this.logger.log(`Media IDs for ticket ${ticketId}: ${JSON.stringify(mediaIds)}`);

      if (mediaIds.length > 0) {
        const mediaRecords = await this.mediaRepository.findByIds(mediaIds);
        this.logger.log(`Found ${mediaRecords.length} media records for ticket ${ticketId}`);

        return mediaRecords.map(media => ({
          id: media.id,
          url: media.storageKey || '',
          name: media.name || '',
          size: media.size || 0,
        }));
      }

      return [];
    } catch (error) {
      this.logger.warn(`Không thể load images cho ticket ${ticketId}: ${error.message}`);
      return [];
    }
  }

  /**
   * Build complete response
   */
  private async buildCompleteResponse(
    customerProduct: CustomerProduct,
    eventProduct?: EventProduct,
    tickets?: EventProductTicket[],
  ): Promise<CompleteEventProductResponseDto> {
    const response = new CompleteEventProductResponseDto();

    // Map customer product data
    response.id = customerProduct.id;
    response.name = customerProduct.name;
    response.description = customerProduct.description || undefined;
    response.productType = customerProduct.productType;
    response.price = customerProduct.price as unknown as Record<string, unknown> || undefined;
    response.typePrice = customerProduct.typePrice || undefined;
    response.tags = customerProduct.tags || undefined;
    // ❌ REMOVED: Không hiển thị status cho user
    // response.status = customerProduct.status;
    response.customFields = customerProduct.customFields as unknown as Record<string, unknown>[] || undefined;
    response.createdAt = customerProduct.createdAt;
    response.updatedAt = customerProduct.updatedAt;
    response.userId = customerProduct.userId ?? 0;

    // Map event product data
    response.participationType = (eventProduct?.participationType as ParticipationTypeEnum) || ParticipationTypeEnum.ONLINE;
    response.location = eventProduct?.location || undefined;
    response.participationUrl = eventProduct?.participationUrl || undefined;
    response.startDate = eventProduct?.startDate || Date.now();
    response.endDate = eventProduct?.endDate || new Date();
    response.timeZone = eventProduct?.timeZone || 'Asia/Ho_Chi_Minh';

    // Map tickets data với images
    response.tickets = [];
    if (tickets && tickets.length > 0) {
      for (const ticket of tickets) {
        // Load images cho từng ticket
        const ticketImages = await this.loadTicketImages(ticket.id);

        response.tickets.push({
          id: ticket.id,
          eventProductId: ticket.eventProductId || 0,
          name: ticket.name,
          price: ticket.price,
          totalQuantity: ticket.totalQuantity,
          description: ticket.description || undefined,
          saleStart: ticket.saleStart,
          saleEnd: ticket.saleEnd,
          timeZone: ticket.timeZone,
          sku: ticket.sku || undefined,
          minQuantityPerOrder: ticket.minQuantityPerOrder || undefined,
          maxQuantityPerOrder: ticket.maxQuantityPerOrder || undefined,
          images: ticketImages,
        });
      }
    }

    // Load images từ entity_has_media join với media_data
    try {
      const mediaLinks = await this.entityHasMediaRepository.findByProductId(customerProduct.id);

      // Filter chỉ lấy ảnh product level (product_id có giá trị, ticket_varial = null)
      const productLevelLinks = mediaLinks.filter(link =>
        link.productId &&
        !link.physicalVarial &&
        !link.ticketVarial &&  // ticket_varial = null nghĩa là product level
        !link.versionId &&
        !link.productComboId &&
        !link.productPlanVarialId &&
        link.mediaId  // Phải có mediaId để join
      );

      // Lấy danh sách mediaId để query media_data
      const mediaIds = productLevelLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length > 0) {
        const mediaRecords = await this.mediaRepository.findByIds(mediaIds);
        response.images = mediaRecords.map(media => ({
          id: media.id,
          url: media.storageKey || '', // Media entity không có url field, dùng storageKey
          name: media.name || '',
          size: media.size || 0,
        }));
      } else {
        response.images = [];
      }

      // Calculate statistics
      if (response.tickets && response.tickets.length > 0) {
        response.totalTicketQuantity = response.tickets.reduce((sum, ticket) => sum + ticket.totalQuantity, 0);
        response.minTicketPrice = Math.min(...response.tickets.map(ticket => ticket.price));
        response.maxTicketPrice = Math.max(...response.tickets.map(ticket => ticket.price));
        response.ticketTypesCount = response.tickets.length;
      }

    } catch (error) {
      this.logger.warn(`Không thể load images cho event product ${customerProduct.id}: ${error.message}`);
      response.images = [];
    }

    return response;
  }
}
