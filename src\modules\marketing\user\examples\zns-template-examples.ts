/**
 * Các mẫu template ZNS đơn giản để test theo tài liệu Zalo API
 * https://developers.zalo.me/docs/zalo-notification-service/quan-ly-tai-san/tao-template
 */

export const ZNS_TEMPLATE_EXAMPLES = {
  /**
   * Template đơn giản nhất - chỉ có 1 paragraph không có params
   */
  ULTRA_SIMPLE: {
    template_name: "Thong bao don gian nhat",
    template_type: 1, // ZNS tùy chỉnh
    tag: "2", // Customer care
    layout: {
      body: {
        components: [
          {
            PARAGRAPH: {
              value: "Cam on ban da su dung dich vu cua chung toi."
            }
          }
        ]
      }
    },
    tracking_id: "ultra_simple_001"
  },

  /**
   * Template đơn giản với format khác - test format component
   */
  SIMPLE_PARAGRAPH: {
    template_name: "Thông báo đơn giản",
    template_type: 1, // ZNS tùy chỉnh
    tag: "2", // Customer care
    layout: {
      body: {
        components: [
          {
            PARAGRAPH: {
              value: "Xin chào <customer_name>, cảm ơn bạn đã sử dụng dịch vụ của chúng tôi."
            }
          }
        ]
      }
    },
    params: [
      {
        name: "customer_name",
        type: 1, // Tên khách hàng
        sample_value: "Nguyễn Văn A"
      }
    ],
    tracking_id: "simple_001"
  },

  /**
   * Template xác thực OTP theo đúng format Zalo
   */
  OTP_AUTHENTICATION: {
    template_name: "Mã xác thực đăng nhập",
    template_type: 2, // ZNS xác thực
    tag: "1", // Transaction
    layout: {
      header: {
        components: [
          {
            LOGO: {
              value: "https://example.com/logo.png"
            }
          }
        ]
      },
      body: {
        components: [
          {
            OTP: {
              value: "<otp_code>"
            }
          },
          {
            PARAGRAPH: {
              value: "Tuyệt đối KHÔNG chia sẻ mã xác thực cho bất kỳ ai dưới bất kỳ hình thức nào. Mã xác thực có hiệu lực trong 5 phút."
            }
          }
        ]
      }
    },
    params: [
      {
        name: "otp_code",
        type: 12, // OTP
        sample_value: "123456"
      }
    ],
    tracking_id: "otp_auth_001",
    note: "Template xác thực OTP đăng nhập"
  },

  /**
   * Template thông báo đơn hàng cơ bản
   */
  ORDER_NOTIFICATION: {
    template_name: "Thông báo đơn hàng cơ bản",
    template_type: 1, // ZNS tùy chỉnh
    tag: "1", // Transaction
    layout: {
      body: {
        components: [
          {
            TITLE: {
              value: "Xác nhận đơn hàng"
            }
          },
          {
            PARAGRAPH: {
              value: "Cảm ơn <customer_name> đã mua hàng tại cửa hàng. Đơn hàng <order_code> đã được xác nhận với tổng tiền <total_amount>."
            }
          }
        ]
      }
    },
    params: [
      {
        name: "customer_name",
        type: 1, // Tên khách hàng
        sample_value: "Nguyễn Văn A"
      },
      {
        name: "order_code",
        type: 4, // Mã số
        sample_value: "DH001"
      },
      {
        name: "total_amount",
        type: 14, // Tiền tệ (VNĐ)
        sample_value: "500000"
      }
    ],
    tracking_id: "order_basic_001",
    note: "Template thông báo đơn hàng cơ bản"
  },

  /**
   * Template chỉ có title - test tối thiểu
   */
  TITLE_ONLY: {
    template_name: "Thông báo tiêu đề",
    template_type: 1, // ZNS tùy chỉnh
    tag: "2", // Customer care
    layout: {
      body: {
        components: [
          {
            TITLE: {
              value: "Thông báo quan trọng"
            }
          }
        ]
      }
    },
    tracking_id: "title_only_001"
  },

  /**
   * Template không có params - test đơn giản nhất
   */
  NO_PARAMS: {
    template_name: "Thông báo không tham số",
    template_type: 1, // ZNS tùy chỉnh
    tag: "2", // Customer care
    layout: {
      body: {
        components: [
          {
            PARAGRAPH: {
              value: "Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi. Chúng tôi luôn nỗ lực để mang đến trải nghiệm tốt nhất."
            }
          }
        ]
      }
    },
    tracking_id: "no_params_001"
  },

  /**
   * Template với format array layout - test format khác
   */
  ARRAY_FORMAT: {
    template_name: "Test format array layout",
    template_type: 1, // ZNS tùy chỉnh
    tag: "2", // Customer care
    layout: [
      {
        type: "body",
        components: [
          {
            type: "PARAGRAPH",
            value: "Test format array layout."
          }
        ]
      }
    ],
    tracking_id: "array_format_001"
  },

  /**
   * Template với TEXT component thay vì PARAGRAPH
   */
  TEXT_COMPONENT: {
    template_name: "Test with TEXT component",
    template_type: 1, // ZNS tùy chỉnh
    tag: "2", // Customer care
    layout: {
      body: {
        components: [
          {
            TEXT: {
              value: "Cam on ban da su dung dich vu cua chung toi."
            }
          }
        ]
      }
    },
    tracking_id: "text_comp_001"
  },

  /**
   * Template với property text thay vì value
   */
  TEXT_PROPERTY: {
    template_name: "Test with text property",
    template_type: 1, // ZNS tùy chỉnh
    tag: "2", // Customer care
    layout: {
      body: {
        components: [
          {
            PARAGRAPH: {
              text: "Cam on ban da su dung dich vu cua chung toi."
            }
          }
        ]
      }
    },
    tracking_id: "text_prop_001"
  }
};

/**
 * Hàm helper để validate template theo quy tắc Zalo
 */
export function validateZnsTemplate(template: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate template_name
  if (!template.template_name || template.template_name.length < 10 || template.template_name.length > 60) {
    errors.push('template_name phải có độ dài từ 10-60 ký tự');
  }

  // Validate template_type
  if (![1, 2, 3, 4, 5].includes(template.template_type)) {
    errors.push('template_type phải là 1, 2, 3, 4, hoặc 5');
  }

  // Validate tag
  if (!['1', '2', '3'].includes(template.tag)) {
    errors.push('tag phải là "1", "2", hoặc "3"');
  }

  // Validate layout
  if (!template.layout || !template.layout.body || !template.layout.body.components) {
    errors.push('layout phải có body.components');
  }

  // Validate tracking_id
  if (!template.tracking_id) {
    errors.push('tracking_id là bắt buộc');
  }

  // Validate template_type và tag combination
  const validCombinations = {
    1: ['1', '2', '3'], // ZNS tùy chỉnh
    2: ['1'],           // ZNS xác thực
    3: ['1'],           // ZNS yêu cầu thanh toán
    4: ['1', '2', '3'], // ZNS voucher
    5: ['2']            // ZNS đánh giá dịch vụ
  };

  if (template.template_type && template.tag) {
    const allowedTags = validCombinations[template.template_type];
    if (!allowedTags || !allowedTags.includes(template.tag)) {
      errors.push(`template_type ${template.template_type} không hỗ trợ tag "${template.tag}"`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
