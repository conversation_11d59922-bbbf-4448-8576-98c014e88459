import { Injectable, Logger } from '@nestjs/common';
import { AdminAudienceCustomFieldDefinitionRepository } from '../repositories/admin-audience-custom-field-definition.repository';
import { Transactional } from 'typeorm-transactional';
import { AppException } from '@common/exceptions';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';
import {
  CreateAudienceCustomFieldDefinitionDto,
  UpdateAudienceCustomFieldDefinitionDto,
  AudienceCustomFieldDefinitionResponseDto,
  AudienceCustomFieldDefinitionQueryDto,
  CustomFieldDefinitionSortField,
} from '../dto/audience-custom-field-definition';
import { SortDirection } from '@common/dto/query.dto';
import { PaginatedResponseDto, PaginationMetaDto } from '../dto/common';
import { AdminAudienceCustomFieldDefinition } from '../entities/admin-audience-custom-field-definition.entity';
import { BulkDeleteResponseDto } from '@/modules/marketing/common/dto';

/**
 * Service xử lý logic nghiệp vụ cho trường tùy chỉnh của admin
 */
@Injectable()
export class AdminAudienceCustomFieldDefinitionService {
  private readonly logger = new Logger(AdminAudienceCustomFieldDefinitionService.name);

  constructor(
    private readonly customFieldRepository: AdminAudienceCustomFieldDefinitionRepository,
  ) {}

  /**
   * Tạo mới trường tùy chỉnh
   * @param employeeId ID của admin
   * @param createDto Dữ liệu tạo mới
   * @returns Thông tin trường tùy chỉnh đã tạo
   */
  @Transactional()
  async create(
    employeeId: number,
    createDto: CreateAudienceCustomFieldDefinitionDto,
  ): Promise<AudienceCustomFieldDefinitionResponseDto> {
    try {
      // Kiểm tra trường tùy chỉnh đã tồn tại chưa
      const existingField = await this.customFieldRepository.findOne({
        where: { fieldKey: createDto.fieldKey },
      });

      if (existingField) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_ALREADY_EXISTS,
          `Trường tùy chỉnh với định danh '${createDto.fieldKey}' đã tồn tại`,
        );
      }

      // Tạo mới trường tùy chỉnh
      const customField = await this.customFieldRepository.create({
        fieldKey: createDto.fieldKey,
        createdBy: employeeId,
        displayName: createDto.displayName,
        dataType: createDto.dataType,
        description: createDto.description,
        tags: createDto.tags || [],
        config: createDto.config || {},
      });

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(customField);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error creating custom field: ${error.message}`, error.stack);
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_CREATION_FAILED,
        `Tạo trường tùy chỉnh thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật trường tùy chỉnh
   * @param employeeId ID của admin
   * @param fieldKey Định danh của trường tùy chỉnh
   * @param updateDto Dữ liệu cập nhật
   * @returns Thông tin trường tùy chỉnh đã cập nhật
   */
  @Transactional()
  async update(
    fieldKey: string,
    updateDto: UpdateAudienceCustomFieldDefinitionDto,
  ): Promise<AudienceCustomFieldDefinitionResponseDto> {
    try {
      // Tìm kiếm trường tùy chỉnh
      const customField = await this.customFieldRepository.findOne({
        where: { fieldKey },
      });

      if (!customField) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy trường tùy chỉnh với định danh '${fieldKey}'`,
        );
      }

      // Cập nhật trường tùy chỉnh
      if (updateDto.displayName) {
        customField.displayName = updateDto.displayName;
      }
      if (updateDto.dataType) {
        customField.dataType = updateDto.dataType;
      }
      if (updateDto.description !== undefined) {
        customField.description = updateDto.description;
      }
      if (updateDto.tags !== undefined) {
        customField.tags = updateDto.tags;
      }
      if (updateDto.config !== undefined) {
        customField.config = updateDto.config;
      }

      // Lưu trường tùy chỉnh
      const updatedField = await this.customFieldRepository.save(customField);

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(updatedField);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating custom field: ${error.message}`, error.stack);
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_UPDATE_FAILED,
        `Cập nhật trường tùy chỉnh thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa trường tùy chỉnh
   * @param employeeId ID của admin
   * @param fieldKey Định danh của trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh đã xóa
   */
  @Transactional()
  async delete(employeeId: number, fieldKey: string): Promise<AudienceCustomFieldDefinitionResponseDto> {
    try {
      // Tìm kiếm trường tùy chỉnh
      const customField = await this.customFieldRepository.findOne({
        where: { fieldKey, createdBy: employeeId },
      });

      if (!customField) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy trường tùy chỉnh với định danh '${fieldKey}'`,
        );
      }

      // Lưu thông tin trường tùy chỉnh trước khi xóa
      const deletedField = { ...customField };

      // Xóa trường tùy chỉnh
      await this.customFieldRepository.remove(customField);

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(deletedField);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error deleting custom field: ${error.message}`, error.stack);
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_DELETION_FAILED,
        `Xóa trường tùy chỉnh thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin trường tùy chỉnh
   * @param employeeId ID của admin
   * @param fieldKey Định danh của trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh
   */
  async findOne(employeeId: number, fieldKey: string): Promise<AudienceCustomFieldDefinitionResponseDto> {
    try {
      // Tìm kiếm trường tùy chỉnh
      const customField = await this.customFieldRepository.findOne({
        where: { fieldKey, createdBy: employeeId },
      });

      if (!customField) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy trường tùy chỉnh với định danh '${fieldKey}'`,
        );
      }

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(customField);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error finding custom field: ${error.message}`, error.stack);
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
        `Không tìm thấy trường tùy chỉnh: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều trường tùy chỉnh
   * @param employeeId ID của admin
   * @param customFieldIds Danh sách ID trường tùy chỉnh cần xóa
   * @returns Kết quả xóa nhiều
   */
  @Transactional()
  async bulkDelete(employeeId: number, customFieldIds: number[]): Promise<BulkDeleteResponseDto> {
    const deletedIds: number[] = [];
    const failedIds: number[] = [];

    // Get all custom fields for this employee
    const customFields = await this.customFieldRepository.find({
      where: { createdBy: employeeId }
    });

    // Create a map of custom fields by their numeric position for easy lookup
    const customFieldsMap = new Map(
      customFields.map((field, index) => [index + 1, field])
    );

    for (const id of customFieldIds) {
      try {
        const customField = customFieldsMap.get(id);

        if (!customField) {
          failedIds.push(id);
          continue;
        }

        // Xóa trường tùy chỉnh
        await this.customFieldRepository.remove(customField);
        deletedIds.push(id);
      } catch (error) {
        this.logger.error(`Error deleting custom field ${id}: ${error.message}`, error.stack);
        failedIds.push(id);
      }
    }

    const deletedCount = deletedIds.length;
    const failedCount = failedIds.length;
    const message = failedCount > 0
      ? `Đã xóa ${deletedCount} trường tùy chỉnh thành công, ${failedCount} trường tùy chỉnh không thể xóa`
      : `Đã xóa ${deletedCount} trường tùy chỉnh thành công`;

    return {
      deletedCount,
      failedCount,
      deletedIds,
      failedIds,
      message,
    };
  }

  /**
   * Lấy danh sách trường tùy chỉnh
   * @param queryDto Tham số truy vấn
   * @returns Danh sách trường tùy chỉnh
   */
  async findAll(
    queryDto: AudienceCustomFieldDefinitionQueryDto,
  ): Promise<PaginatedResponseDto<AudienceCustomFieldDefinitionResponseDto>> {
    try {
      const { page = 1, limit = 10, fieldKey, displayName, dataType, sortBy, sortDirection, search } = queryDto;

      // Tính toán offset
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện sắp xếp
      const order: Record<string, 'ASC' | 'DESC'> = {};
      if (sortBy) {
        order[sortBy] = sortDirection || SortDirection.ASC;
      } else {
        order.displayName = SortDirection.ASC;
      }

      let queryBuilder = this.customFieldRepository
        .createQueryBuilder('customField');

      // Xử lý tìm kiếm chung (search)
      if (search) {
        queryBuilder = queryBuilder.where(
          '(customField.displayName ILIKE :search OR customField.fieldKey ILIKE :search OR customField.tags::text ILIKE :search)',
          { search: `%${search}%` }
        );
      }

      // Xử lý tìm kiếm cụ thể theo từng trường
      if (fieldKey) {
        queryBuilder = queryBuilder.andWhere('customField.fieldKey ILIKE :fieldKey', { fieldKey: `%${fieldKey}%` });
      }

      if (displayName) {
        queryBuilder = queryBuilder.andWhere('customField.displayName ILIKE :displayName', { displayName: `%${displayName}%` });
      }

      if (dataType) {
        queryBuilder = queryBuilder.andWhere('customField.dataType = :dataType', { dataType });
      }

      // Áp dụng sắp xếp
      Object.entries(order).forEach(([field, direction]) => {
        queryBuilder = queryBuilder.addOrderBy(`customField.${field}`, direction);
      });

      // Lấy tổng số items trước
      const totalItems = await queryBuilder.getCount();

      // Sau đó lấy danh sách với phân trang
      const customFields = await queryBuilder.skip(skip).take(limit).getMany();

      // Tính toán thông tin phân trang
      const meta = new PaginationMetaDto();
      meta.total = totalItems;
      meta.page = page;
      meta.limit = limit;
      meta.totalPages = Math.ceil(totalItems / limit);
      meta.hasPreviousPage = page > 1;
      meta.hasNextPage = page < meta.totalPages;

      // Chuyển đổi sang DTO
      const data = customFields.map((customField) => this.mapToResponseDto(customField));

      return {
        data,
        meta,
      };
    } catch (error) {
      this.logger.error(`Error finding custom fields: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Chuyển đổi entity sang DTO
   * @param customField Entity trường tùy chỉnh
   * @returns DTO trường tùy chỉnh
   */
  private mapToResponseDto(
    customField: AdminAudienceCustomFieldDefinition,
  ): AudienceCustomFieldDefinitionResponseDto {
    return {
      fieldKey: customField.fieldKey,
      createdBy: customField.createdBy,
      displayName: customField.displayName,
      dataType: customField.dataType,
      description: customField.description,
      tags: customField.tags || [],
      config: customField.config || {},
    };
  }
}
