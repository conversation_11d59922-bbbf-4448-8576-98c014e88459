# Changelog: Update Audience Avatar API

## Tổng quan thay đổi

Cập nhật API `PUT /v1/admin/marketing/audiences/:id` để hỗ trợ upload avatar thông qua S3 temporary URL pattern.

## Files đã thay đổi

### 1. `src/modules/marketing/admin/dto/audience/update-audience.dto.ts`
- **Thêm import**: `IsEnum` từ class-validator và `ImageTypeEnum`
- **Thêm field mới**: `avatarMediaType?: ImageTypeEnum`
  - Cho phép client chỉ định loại file avatar (image/jpeg, image/png, image/webp, image/gif)
  - <PERSON>hi có field này, API sẽ tạo URL tạm thời để upload

### 2. `src/modules/marketing/admin/dto/audience/audience-response.dto.ts`
- **Thêm class mới**: `UpdateAudienceResponseDto extends AudienceResponseDto`
- **Thêm fields**:
  - `avatarUploadUrl?: string` - URL tạm thời để upload
  - `avatarS3Key?: string` - S3 key của file avatar
  - `avatarUploadExpiresAt?: number` - Thời gian hết hạn URL

### 3. `src/modules/marketing/admin/services/admin-audience.service.ts`
- **Cập nhật import**: Thêm `UpdateAudienceResponseDto`
- **Cập nhật method `update`**:
  - Thay đổi return type từ `AudienceResponseDto` thành `UpdateAudienceResponseDto`
  - **Logic mới**: Khi có `avatarMediaType`:
    1. Tạo S3 key sử dụng `generateS3Key`
    2. **Lưu S3 key vào database ngay lập tức** (`audience.avatar = avatarS3Key`)
    3. Tạo presigned URL sử dụng `s3Service.createPresignedWithID`
    4. Trả về thông tin upload trong response

### 4. `src/modules/marketing/admin/controllers/admin-audience.controller.ts`
- **Cập nhật import**: Thêm `UpdateAudienceResponseDto`
- **Cập nhật endpoint `PUT /:id`**:
  - Thay đổi return type thành `UpdateAudienceResponseDto`
  - Cập nhật Swagger documentation với mô tả chi tiết

## Tính năng mới

### Avatar Upload Flow
1. **Client gửi request** với `avatarMediaType`
2. **Server tạo S3 key** và **lưu vào database ngay lập tức**
3. **Server tạo presigned URL** cho upload
4. **Server trả về** thông tin upload (URL, S3 key, thời gian hết hạn)
5. **Client upload file** lên S3 sử dụng presigned URL
6. **Avatar đã có sẵn** trong hệ thống (không cần API call thêm)

### Ưu điểm của approach này
- **Đơn giản**: Chỉ cần 1 API call để cập nhật audience + tạo upload URL
- **Atomic**: S3 key được lưu cùng lúc với việc cập nhật audience
- **Không cần API thêm**: Sau khi upload xong, avatar đã có sẵn
- **Consistent**: Đảm bảo database luôn có S3 key hợp lệ

## Cấu hình S3 Key

```typescript
const s3Key = generateS3Key({
  baseFolder: 'marketing',
  categoryFolder: CategoryFolderEnum.CUSTOMER_AVATAR,
  useTimeFolder: true,
  prefix: 'admin',
  fileName: `avatar.${mediaType.split('/')[1]}`,
});
```

**Ví dụ S3 key**: `marketing/customer_avatars/2024/01/admin/1234567890-uuid.jpg`

## Validation

- **Media types được hỗ trợ**: `image/jpeg`, `image/png`, `image/webp`, `image/gif`
- **Kích thước tối đa**: 5MB
- **Thời gian hết hạn URL**: 1 giờ

## Backward Compatibility

- API vẫn hoạt động bình thường khi không có `avatarMediaType`
- Trả về `AudienceResponseDto` chuẩn khi không có avatar upload
- Không breaking changes cho client hiện tại

## Testing

- Tạo file `test-update-audience-avatar.http` với các test cases
- Tạo documentation chi tiết trong `docs/update-audience-avatar-api.md`
- Bao gồm test cases cho cả success và error scenarios

## Error Handling

- Nếu có lỗi khi tạo presigned URL, audience vẫn được cập nhật thành công
- Lỗi được log nhưng không làm fail toàn bộ request
- Client có thể retry tạo upload URL nếu cần
