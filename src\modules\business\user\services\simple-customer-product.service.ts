import { Injectable, Logger } from '@nestjs/common';
import { CustomerProductRepository } from '@modules/business/repositories/customer-product.repository';
import { CustomerProduct } from '@modules/business/entities';
import { SimpleCreateCustomerProductDto } from '../dto/customer-product/simple-create-customer-product.dto';
import { SimpleCustomerProductResponseDto } from '../dto/customer-product/simple-customer-product-response.dto';
import { QueryCustomerProductDto } from '../dto/customer-product/query-customer-product.dto';
import { BulkDeleteCustomerProductDto, BulkDeleteCustomerProductResponseDto } from '../dto/customer-product/bulk-delete-customer-product.dto';
import { AppException } from '@common/exceptions/app.exception';
import { CUSTOMER_PRODUCT_ERROR_CODES } from '@modules/business/exceptions/customer-product.error-codes';
import { Transactional } from 'typeorm-transactional';
import { EntityStatusEnum } from '@modules/business/enums';
import { PaginatedResult } from '@common/response';

/**
 * Service xử lý các thao tác cơ bản với Customer Product
 * Chỉ tập trung vào CRUD đơn giản với 3 fields cơ bản
 */
@Injectable()
export class SimpleCustomerProductService {
  private readonly logger = new Logger(SimpleCustomerProductService.name);

  constructor(
    private readonly customerProductRepository: CustomerProductRepository,
  ) {}

  /**
   * Tạo sản phẩm khách hàng đơn giản với 3 fields cơ bản
   * @param dto DTO chứa thông tin cơ bản (name, description, productType)
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã tạo
   */
  @Transactional()
  async createSimpleCustomerProduct(
    dto: SimpleCreateCustomerProductDto,
    userId: number,
  ): Promise<SimpleCustomerProductResponseDto> {
    try {
      this.logger.log(`Tạo sản phẩm khách hàng đơn giản: ${dto.name} cho userId=${userId}`);

      // Validate input
      this.validateSimpleCreateInput(dto);

      // Tạo customer product entity với thông tin cơ bản
      const customerProductData: Partial<CustomerProduct> = {
        userId,
        name: dto.name.trim(),
        description: dto.description?.trim() || null,
        productType: dto.productType,
        status: EntityStatusEnum.PENDING, // Mặc định là PENDING
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      // Lưu vào database
      const savedProduct = await this.customerProductRepository.create(customerProductData);

      this.logger.log(`Tạo thành công sản phẩm khách hàng: ${savedProduct.name} (ID: ${savedProduct.id})`);

      // Chuyển đổi sang response DTO
      return this.mapToSimpleResponseDto(savedProduct);

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi tạo sản phẩm khách hàng: ${error.message}`, error.stack);
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.CREATION_FAILED,
        `Lỗi khi tạo sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách sản phẩm khách hàng với phân trang
   * @param queryDto DTO chứa các tham số truy vấn
   * @param userId ID của người dùng hiện tại
   * @returns Danh sách sản phẩm với phân trang
   */
  async getCustomerProducts(
    queryDto: QueryCustomerProductDto,
    userId: number,
  ): Promise<PaginatedResult<SimpleCustomerProductResponseDto>> {
    try {
      this.logger.log(`Lấy danh sách sản phẩm khách hàng cho userId=${userId}`);

      // Gọi repository để lấy dữ liệu
      const result = await this.customerProductRepository.findByUserIdWithPagination(userId, queryDto);

      // Chuyển đổi sang response DTOs
      const items = result.items.map(product => this.mapToSimpleResponseDto(product));

      return {
        items,
        meta: result.meta,
      };

    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách sản phẩm khách hàng: ${error.message}`, error.stack);
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.FIND_FAILED,
        `Lỗi khi lấy danh sách sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết sản phẩm khách hàng theo ID
   * @param id ID của sản phẩm
   * @param userId ID của người dùng hiện tại
   * @returns Chi tiết sản phẩm
   */
  async getCustomerProductDetail(
    id: number,
    userId: number,
  ): Promise<SimpleCustomerProductResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết sản phẩm khách hàng ID=${id} cho userId=${userId}`);

      const product = await this.customerProductRepository.findByIdAndUserId(id, userId);
      
      if (!product) {
        throw new AppException(
          CUSTOMER_PRODUCT_ERROR_CODES.NOT_FOUND,
          'Không tìm thấy sản phẩm khách hàng',
        );
      }

      return this.mapToSimpleResponseDto(product);

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi lấy chi tiết sản phẩm khách hàng: ${error.message}`, error.stack);
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.FIND_FAILED,
        `Lỗi khi lấy chi tiết sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Xóa batch sản phẩm khách hàng
   * @param dto DTO chứa danh sách ID cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả xóa
   */
  @Transactional()
  async deleteCustomerProducts(
    dto: BulkDeleteCustomerProductDto,
    userId: number,
  ): Promise<BulkDeleteCustomerProductResponseDto> {
    try {
      this.logger.log(`Xóa batch sản phẩm khách hàng: ${dto.ids.join(', ')} cho userId=${userId}`);

      const result = await this.customerProductRepository.softDeleteByIdsAndUserId(dto.ids, userId);

      this.logger.log(`Xóa thành công ${result.deletedCount}/${dto.ids.length} sản phẩm khách hàng`);

      // Tạo response
      const response: BulkDeleteCustomerProductResponseDto = {
        deletedCount: result.deletedCount,
        totalRequested: dto.ids.length,
        failedIds: result.failedIds,
        failedReasons: result.failedReasons || [],
        status: this.determineDeleteStatus(result.deletedCount, dto.ids.length),
      };

      return response;

    } catch (error) {
      this.logger.error(`Lỗi khi xóa batch sản phẩm khách hàng: ${error.message}`, error.stack);
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.DELETE_FAILED,
        `Lỗi khi xóa sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Validate input cho việc tạo sản phẩm đơn giản
   */
  private validateSimpleCreateInput(dto: SimpleCreateCustomerProductDto): void {
    if (!dto.name || dto.name.trim().length === 0) {
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.REQUIRED_FIELD_MISSING,
        'Tên sản phẩm không được để trống',
      );
    }

    if (dto.name.length > 500) {
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.VALIDATION_FAILED,
        'Tên sản phẩm không được vượt quá 500 ký tự',
      );
    }

    if (dto.description && dto.description.length > 10000) {
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.VALIDATION_FAILED,
        'Mô tả sản phẩm không được vượt quá 10000 ký tự',
      );
    }
  }

  /**
   * Chuyển đổi entity sang response DTO
   */
  private mapToSimpleResponseDto(product: CustomerProduct): SimpleCustomerProductResponseDto {
    return {
      id: product.id,
      name: product.name,
      description: product.description || undefined,
      productType: product.productType,
      status: product.status,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      userId: product.userId || 0,
    };
  }

  /**
   * Xác định trạng thái delete
   */
  private determineDeleteStatus(deletedCount: number, totalRequested: number): 'success' | 'partial_success' | 'failed' {
    if (deletedCount === 0) {
      return 'failed';
    } else if (deletedCount === totalRequested) {
      return 'success';
    } else {
      return 'partial_success';
    }
  }
}
