import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloService } from './zalo.service';
import {
  ZaloUserInfo,
  ZaloUserLabel,
  ZaloUserLabelList,
  ZaloCreateLabelRequest,
  ZaloUserLabelRequest,
  ZaloUserList,
  ZaloUpdateUserRequest,
  ZaloUserCustomInfo,
  ZaloUpdateCustomInfoRequest,
  ZaloUserGetListRequest,
  ZaloUserGetListResponse,
} from './zalo.interface';

/**
 * Service xử lý các API quản lý thông tin người dùng của Zalo Official Account
 * 
 * Điều kiện sử dụng từ tài liệu Zalo:
 * - Gắn nhãn khách hàng: Cần quyền quản lý người dùng, tối đa 20 nhãn cho mỗi người dùng
 * - Gỡ nhãn khách hàng: <PERSON><PERSON><PERSON> quyền quản lý người dùng
 * - <PERSON><PERSON><PERSON> danh sách nhãn: Cần quyền đọc thông tin người dùng
 * - Xóa nhãn: Cần quyền quản lý người dùng, sẽ tự động gỡ nhãn khỏi tất cả người dùng
 * - Truy xuất danh sách người dùng: Cần quyền đọc thông tin người dùng, hỗ trợ phân trang
 * - Truy xuất chi tiết người dùng: Cần quyền đọc thông tin người dùng
 * - Cập nhật chi tiết người dùng: Cần quyền quản lý người dùng
 * - Xóa thông tin khách hàng: Cần quyền quản lý người dùng, xóa vĩnh viễn
 * - Truy xuất thông tin tùy biến: Cần quyền đọc thông tin người dùng
 * - Cập nhật thông tin tùy biến: Cần quyền quản lý người dùng, tối đa 50 trường tùy biến
 */
@Injectable()
export class ZaloUserManagementService {
  private readonly logger = new Logger(ZaloUserManagementService.name);
  private readonly baseApiUrl = 'https://openapi.zalo.me/v2.0/oa';

  constructor(
    private readonly zaloService: ZaloService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Gắn nhãn cho khách hàng
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param labelId ID của nhãn cần gắn
   * @returns Kết quả gắn nhãn
   */
  async assignLabelToUser(
    accessToken: string,
    userId: string,
    labelId: string,
  ): Promise<{ success: boolean }> {
    try {
      const data: ZaloUserLabelRequest = {
        user_id: userId,
        label_id: labelId,
      };

      this.logger.debug(`Assigning label ${labelId} to user ${userId}`);
      return await this.zaloService.post<{ success: boolean }>(
        `${this.baseApiUrl}/label/assign`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error assigning label to user: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gắn nhãn cho khách hàng',
      );
    }
  }

  /**
   * Gỡ nhãn khỏi khách hàng
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param labelId ID của nhãn cần gỡ
   * @returns Kết quả gỡ nhãn
   */
  async removeLabelFromUser(
    accessToken: string,
    userId: string,
    labelId: string,
  ): Promise<{ success: boolean }> {
    try {
      const data: ZaloUserLabelRequest = {
        user_id: userId,
        label_id: labelId,
      };

      this.logger.debug(`Removing label ${labelId} from user ${userId}`);
      return await this.zaloService.post<{ success: boolean }>(
        `${this.baseApiUrl}/label/remove`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error removing label from user: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gỡ nhãn khỏi khách hàng',
      );
    }
  }

  /**
   * Lấy danh sách tất cả nhãn
   * @param accessToken Access token của Official Account
   * @param offset Vị trí bắt đầu (mặc định: 0)
   * @param count Số lượng nhãn tối đa trả về (1-50, mặc định: 20)
   * @returns Danh sách nhãn
   */
  async getLabels(
    accessToken: string,
    offset: number = 0,
    count: number = 20,
  ): Promise<ZaloUserLabelList> {
    try {
      const params = new URLSearchParams();
      params.append('offset', offset.toString());
      params.append('count', Math.min(Math.max(count, 1), 50).toString());

      this.logger.debug(`Getting labels with offset: ${offset}, count: ${count}`);
      return await this.zaloService.get<ZaloUserLabelList>(
        `${this.baseApiUrl}/label?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting labels: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách nhãn',
      );
    }
  }

  /**
   * Tạo nhãn mới
   * @param accessToken Access token của Official Account
   * @param labelData Thông tin nhãn cần tạo
   * @returns Thông tin nhãn đã tạo
   */
  async createLabel(
    accessToken: string,
    labelData: ZaloCreateLabelRequest,
  ): Promise<ZaloUserLabel> {
    try {
      this.logger.debug(`Creating new label: ${labelData.label_name}`);
      return await this.zaloService.post<ZaloUserLabel>(
        `${this.baseApiUrl}/label/create`,
        accessToken,
        labelData,
      );
    } catch (error) {
      this.logger.error(`Error creating label: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo nhãn mới',
      );
    }
  }

  /**
   * Xóa nhãn (sẽ tự động gỡ nhãn khỏi tất cả người dùng)
   * @param accessToken Access token của Official Account
   * @param labelId ID của nhãn cần xóa
   * @returns Kết quả xóa nhãn
   */
  async deleteLabel(
    accessToken: string,
    labelId: string,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.debug(`Deleting label ${labelId}`);
      return await this.zaloService.post<{ success: boolean }>(
        `${this.baseApiUrl}/label/${labelId}/delete`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error deleting label: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xóa nhãn',
      );
    }
  }

  /**
   * Truy xuất danh sách người dùng
   * @param accessToken Access token của Official Account
   * @param offset Vị trí bắt đầu (mặc định: 0)
   * @param count Số lượng người dùng tối đa trả về (1-50, mặc định: 20)
   * @param labelId ID nhãn để lọc người dùng (tùy chọn)
   * @returns Danh sách người dùng
   */
  async getUsers(
    accessToken: string,
    offset: number = 0,
    count: number = 20,
    labelId?: string,
  ): Promise<ZaloUserList> {
    try {
      const params = new URLSearchParams();
      params.append('offset', offset.toString());
      params.append('count', Math.min(Math.max(count, 1), 50).toString());
      if (labelId) {
        params.append('label_id', labelId);
      }

      this.logger.debug(`Getting users with offset: ${offset}, count: ${count}, labelId: ${labelId || 'none'}`);
      return await this.zaloService.get<ZaloUserList>(
        `${this.baseApiUrl}/user?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting users: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi truy xuất danh sách người dùng',
      );
    }
  }

  /**
   * Truy xuất chi tiết thông tin người dùng
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @returns Thông tin chi tiết người dùng
   */
  async getUserDetail(
    accessToken: string,
    userId: string,
  ): Promise<ZaloUserInfo> {
    try {
      this.logger.debug(`Getting user detail for user ${userId}`);
      return await this.zaloService.get<ZaloUserInfo>(
        `${this.baseApiUrl}/user/${userId}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting user detail: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi truy xuất chi tiết người dùng',
      );
    }
  }

  /**
   * Cập nhật thông tin chi tiết người dùng
   * @param accessToken Access token của Official Account
   * @param updateData Dữ liệu cập nhật người dùng
   * @returns Kết quả cập nhật
   */
  async updateUserDetail(
    accessToken: string,
    updateData: ZaloUpdateUserRequest,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.debug(`Updating user detail for user ${updateData.user_id}`);
      return await this.zaloService.post<{ success: boolean }>(
        `${this.baseApiUrl}/user/update`,
        accessToken,
        updateData,
      );
    } catch (error) {
      this.logger.error(`Error updating user detail: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi cập nhật thông tin người dùng',
      );
    }
  }

  /**
   * Xóa thông tin khách hàng quan tâm OA (xóa vĩnh viễn)
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @returns Kết quả xóa
   */
  async deleteUserInfo(
    accessToken: string,
    userId: string,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.debug(`Deleting user info for user ${userId}`);
      return await this.zaloService.post<{ success: boolean }>(
        `${this.baseApiUrl}/user/${userId}/delete`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error deleting user info: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xóa thông tin khách hàng',
      );
    }
  }

  /**
   * Truy xuất thông tin tùy biến của người dùng
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @returns Thông tin tùy biến của người dùng
   */
  async getUserCustomInfo(
    accessToken: string,
    userId: string,
  ): Promise<ZaloUserCustomInfo> {
    try {
      this.logger.debug(`Getting custom info for user ${userId}`);
      return await this.zaloService.get<ZaloUserCustomInfo>(
        `${this.baseApiUrl}/user/${userId}/custom`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting user custom info: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi truy xuất thông tin tùy biến của người dùng',
      );
    }
  }

  /**
   * Cập nhật thông tin tùy biến của người dùng
   * @param accessToken Access token của Official Account
   * @param customData Dữ liệu tùy biến cần cập nhật
   * @returns Kết quả cập nhật
   */
  async updateUserCustomInfo(
    accessToken: string,
    customData: ZaloUpdateCustomInfoRequest,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.debug(`Updating custom info for user ${customData.user_id}`);
      return await this.zaloService.post<{ success: boolean }>(
        `${this.baseApiUrl}/user/custom/update`,
        accessToken,
        customData,
      );
    } catch (error) {
      this.logger.error(`Error updating user custom info: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi cập nhật thông tin tùy biến của người dùng',
      );
    }
  }

  /**
   * Tìm kiếm người dùng theo tên hoặc ID
   * @param accessToken Access token của Official Account
   * @param query Từ khóa tìm kiếm (tên hoặc ID)
   * @param offset Vị trí bắt đầu (mặc định: 0)
   * @param count Số lượng kết quả tối đa (1-50, mặc định: 20)
   * @returns Danh sách người dùng tìm được
   */
  async searchUsers(
    accessToken: string,
    query: string,
    offset: number = 0,
    count: number = 20,
  ): Promise<ZaloUserList> {
    try {
      const params = new URLSearchParams();
      params.append('q', query);
      params.append('offset', offset.toString());
      params.append('count', Math.min(Math.max(count, 1), 50).toString());

      this.logger.debug(`Searching users with query: ${query}`);
      return await this.zaloService.get<ZaloUserList>(
        `${this.baseApiUrl}/user/search?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error searching users: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tìm kiếm người dùng',
      );
    }
  }

  /**
   * Lấy danh sách nhãn của một người dùng cụ thể
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @returns Danh sách nhãn của người dùng
   */
  async getUserLabels(
    accessToken: string,
    userId: string,
  ): Promise<ZaloUserLabelList> {
    try {
      this.logger.debug(`Getting labels for user ${userId}`);
      return await this.zaloService.get<ZaloUserLabelList>(
        `${this.baseApiUrl}/user/${userId}/labels`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting user labels: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách nhãn của người dùng',
      );
    }
  }

  /**
   * Cập nhật nhãn (chỉnh sửa thông tin nhãn)
   * @param accessToken Access token của Official Account
   * @param labelId ID của nhãn cần cập nhật
   * @param labelData Dữ liệu nhãn mới
   * @returns Thông tin nhãn đã cập nhật
   */
  async updateLabel(
    accessToken: string,
    labelId: string,
    labelData: Partial<ZaloCreateLabelRequest>,
  ): Promise<ZaloUserLabel> {
    try {
      this.logger.debug(`Updating label ${labelId}`);
      return await this.zaloService.post<ZaloUserLabel>(
        `${this.baseApiUrl}/label/${labelId}/update`,
        accessToken,
        labelData,
      );
    } catch (error) {
      this.logger.error(`Error updating label: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi cập nhật nhãn',
      );
    }
  }

  /**
   * Lấy danh sách người dùng theo API v3.0 với các tùy chọn lọc nâng cao
   * Endpoint: GET https://openapi.zalo.me/v3.0/oa/user/getlist
   * @param accessToken Access token của Official Account
   * @param request Tham số yêu cầu lấy danh sách người dùng
   * @returns Danh sách người dùng với thông tin chi tiết
   */
  async getUserList(
    accessToken: string,
    request: ZaloUserGetListRequest,
  ): Promise<ZaloUserGetListResponse> {
    try {
      const data = {
        offset: request.offset,
        count: Math.min(Math.max(request.count, 1), 50),
        ...(request.tag_name && { tag_name: request.tag_name }),
        ...(request.last_interaction_period && { last_interaction_period: request.last_interaction_period }),
        ...(request.is_follower !== undefined && { is_follower: request.is_follower }),
      };

      this.logger.debug(`Getting user list with params:`, data);

      return await this.zaloService.get<ZaloUserGetListResponse>(
        'https://openapi.zalo.me/v3.0/oa/user/getlist',
        accessToken,
        { data: JSON.stringify(data) },
      );
    } catch (error) {
      this.logger.error(`Error getting user list: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách người dùng',
      );
    }
  }
}
