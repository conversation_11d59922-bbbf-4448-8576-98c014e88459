import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { DigitalProductVersion } from '../entities/digital-product-version.entity';

/**
 * Repository cho DigitalProductVersion entity
 * Xử lý các thao tác database cho phiên bản sản phẩm số
 */
@Injectable()
export class DigitalProductVersionRepository {
  private readonly logger = new Logger(DigitalProductVersionRepository.name);

  constructor(
    @InjectRepository(DigitalProductVersion)
    private readonly repository: Repository<DigitalProductVersion>,
  ) {}

  /**
   * Tạo phiên bản sản phẩm số mới
   * @param data Dữ liệu phiên bản
   * @returns Phiên bản đã tạo
   */
  async create(data: Partial<DigitalProductVersion>): Promise<DigitalProductVersion> {
    const version = this.repository.create(data);
    return this.repository.save(version);
  }

  /**
   * Tìm phiên bản theo ID
   * @param id ID phiên bản
   * @returns Phiên bản hoặc null
   */
  async findById(id: number): Promise<DigitalProductVersion | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm tất cả phiên bản theo digital product ID
   * @param digitalProductId ID sản phẩm số
   * @returns Danh sách phiên bản
   */
  async findByDigitalProductId(digitalProductId: number): Promise<DigitalProductVersion[]> {
    return this.repository.find({
      where: { digitalProductId },
      order: { id: 'ASC' },
    });
  }

  /**
   * Cập nhật phiên bản
   * @param id ID phiên bản
   * @param data Dữ liệu cập nhật
   * @returns Phiên bản đã cập nhật
   */
  async update(id: number, data: Partial<DigitalProductVersion>): Promise<DigitalProductVersion | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa mềm phiên bản (có thể implement soft delete nếu cần)
   * @param id ID phiên bản
   */
  async softDelete(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Xóa cứng phiên bản
   * @param id ID phiên bản
   */
  async delete(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Tạo nhiều phiên bản cùng lúc
   * @param versions Danh sách dữ liệu phiên bản
   * @returns Danh sách phiên bản đã tạo
   */
  async bulkCreate(versions: Partial<DigitalProductVersion>[]): Promise<DigitalProductVersion[]> {
    if (!versions.length) return [];
    
    const createdVersions = this.repository.create(versions);
    return this.repository.save(createdVersions);
  }

  /**
   * Tìm phiên bản theo SKU
   * @param sku Mã SKU
   * @param excludeId ID phiên bản cần loại trừ (dùng khi update)
   * @returns Phiên bản hoặc null
   */
  async findBySku(sku: string, excludeId?: number): Promise<DigitalProductVersion | null> {
    const queryBuilder = this.repository.createQueryBuilder('version');
    queryBuilder.where('version.sku = :sku', { sku });
    
    if (excludeId) {
      queryBuilder.andWhere('version.id != :excludeId', { excludeId });
    }

    return queryBuilder.getOne();
  }

  /**
   * Kiểm tra SKU có tồn tại không
   * @param sku Mã SKU
   * @param excludeId ID phiên bản cần loại trừ
   * @returns true nếu SKU đã tồn tại
   */
  async existsBySku(sku: string, excludeId?: number): Promise<boolean> {
    const version = await this.findBySku(sku, excludeId);
    return !!version;
  }

  /**
   * Tìm phiên bản theo barcode
   * @param barcode Mã vạch
   * @param excludeId ID phiên bản cần loại trừ (dùng khi update)
   * @returns Phiên bản hoặc null
   */
  async findByBarcode(barcode: string, excludeId?: number): Promise<DigitalProductVersion | null> {
    const queryBuilder = this.repository.createQueryBuilder('version');
    queryBuilder.where('version.barcode = :barcode', { barcode });
    
    if (excludeId) {
      queryBuilder.andWhere('version.id != :excludeId', { excludeId });
    }

    return queryBuilder.getOne();
  }

  /**
   * Kiểm tra barcode có tồn tại không
   * @param barcode Mã vạch
   * @param excludeId ID phiên bản cần loại trừ
   * @returns true nếu barcode đã tồn tại
   */
  async existsByBarcode(barcode: string, excludeId?: number): Promise<boolean> {
    const version = await this.findByBarcode(barcode, excludeId);
    return !!version;
  }

  /**
   * Xóa tất cả phiên bản của một sản phẩm số
   * @param digitalProductId ID sản phẩm số
   */
  async deleteByDigitalProductId(digitalProductId: number): Promise<void> {
    await this.repository.delete({ digitalProductId });
  }

  /**
   * Đếm số lượng phiên bản của một sản phẩm số
   * @param digitalProductId ID sản phẩm số
   * @returns Số lượng phiên bản
   */
  async countByDigitalProductId(digitalProductId: number): Promise<number> {
    return this.repository.count({
      where: { digitalProductId },
    });
  }

  /**
   * Lưu phiên bản
   * @param version Phiên bản cần lưu
   * @returns Phiên bản đã lưu
   */
  async save(version: DigitalProductVersion): Promise<DigitalProductVersion> {
    return this.repository.save(version);
  }

  /**
   * Tìm phiên bản theo danh sách IDs
   * @param ids Danh sách ID
   * @returns Danh sách phiên bản
   */
  async findByIds(ids: number[]): Promise<DigitalProductVersion[]> {
    if (!ids.length) return [];

    return this.repository.find({
      where: { id: In(ids) },
    });
  }

  /**
   * Tìm phiên bản theo content link
   * @param contentLink Liên kết nội dung
   * @returns Phiên bản hoặc null
   */
  async findByContentLink(contentLink: string): Promise<DigitalProductVersion | null> {
    return this.repository.findOne({
      where: { contentLink },
    });
  }

  /**
   * Tìm phiên bản theo version name trong cùng digital product
   * @param digitalProductId ID sản phẩm số
   * @param versionName Tên phiên bản
   * @param excludeId ID phiên bản cần loại trừ
   * @returns Phiên bản hoặc null
   */
  async findByVersionName(
    digitalProductId: number, 
    versionName: string, 
    excludeId?: number
  ): Promise<DigitalProductVersion | null> {
    const queryBuilder = this.repository.createQueryBuilder('version');
    queryBuilder.where('version.digitalProductId = :digitalProductId', { digitalProductId });
    queryBuilder.andWhere('version.versionName = :versionName', { versionName });
    
    if (excludeId) {
      queryBuilder.andWhere('version.id != :excludeId', { excludeId });
    }

    return queryBuilder.getOne();
  }

  /**
   * Kiểm tra version name có tồn tại trong cùng digital product không
   * @param digitalProductId ID sản phẩm số
   * @param versionName Tên phiên bản
   * @param excludeId ID phiên bản cần loại trừ
   * @returns true nếu tên phiên bản đã tồn tại
   */
  async existsByVersionName(
    digitalProductId: number, 
    versionName: string, 
    excludeId?: number
  ): Promise<boolean> {
    const version = await this.findByVersionName(digitalProductId, versionName, excludeId);
    return !!version;
  }
}
