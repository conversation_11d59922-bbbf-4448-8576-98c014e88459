import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsArray,
  ValidateNested,
  IsObject,
  MaxLength,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  PriceTypeEnum,
  ProductTypeEnum
} from '@modules/business/enums';
import { CustomFieldInputDto } from '../custom-field-metadata.dto';
import { HasPriceDto, StringPriceDto } from '../price.dto';
import { DigitalProductVersionOperationDto } from './digital-product-version-operation.dto';
import { ProductImageOperationDto } from '../image-operations/image-operation.dto';

/**
 * DTO cho thông tin cơ bản sản phẩm (tái sử dụng từ physical product)
 */
export class BasicInfoDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Khóa học lập trình Python cơ bản',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  name?: string;

  @ApiProperty({
    description: '<PERSON><PERSON> tả sản phẩm',
    example: '<PERSON>h<PERSON><PERSON> học lập trình Python từ cơ bản đến nâng cao với nhiều bài tập thực hành',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.DIGITAL,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductTypeEnum)
  productType?: ProductTypeEnum;

  @ApiProperty({
    description: 'Danh sách tags',
    type: [String],
    example: ['khóa học', 'lập trình', 'python', 'online'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * DTO cho thông tin giá (tái sử dụng từ physical product)
 */
export class PricingInfoDto {
  @ApiProperty({
    description: 'Giá sản phẩm (HasPriceDto hoặc StringPriceDto)',
    example: {
      listPrice: 500000,
      salePrice: 450000,
      currency: 'VND'
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  price?: HasPriceDto | StringPriceDto;

  @ApiProperty({
    description: 'Loại giá sản phẩm',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
  })
  @IsOptional()
  @IsEnum(PriceTypeEnum)
  typePrice?: PriceTypeEnum;
}

/**
 * DTO cho thông tin số hóa sản phẩm (thay thế cho PhysicalInfoDto)
 */
export class DigitalInfoDto {
  @ApiProperty({
    description: 'Phương thức giao hàng',
    example: 'Tải xuống trực tiếp',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  deliveryMethod?: string;

  @ApiProperty({
    description: 'Thời điểm giao hàng',
    example: 'Ngay lập tức sau khi thanh toán',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  deliveryTime?: string;

  @ApiProperty({
    description: 'Thời gian chờ trước khi giao hàng',
    example: 'Xử lý trong 5 phút',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  waitingTime?: string;
}

/**
 * DTO cho các thao tác với phiên bản và hình ảnh
 */
export class DigitalOperationsDto {
  @ApiProperty({
    description: 'Danh sách thao tác với phiên bản sản phẩm số (ADD/UPDATE/DELETE)',
    type: [DigitalProductVersionOperationDto],
    example: [
      {
        operation: 'add',
        data: {
          versionName: 'Bản tiêu chuẩn',
          description: 'Phiên bản cơ bản với đầy đủ tính năng chính',
          customFields: { language: 'Tiếng Việt', format: 'PDF' },
          contentLink: 'https://example.com/download/course-v1.pdf',
          sku: 'COURSE-STD-V1',
          minQuantity: 1,
          maxQuantity: 1,
          imageOperations: [
            {
              operation: 'add',
              mediaId: '123e4567-e89b-12d3-a456-426614174000'
            }
          ],
          images: [
            {
              key: 'business/IMAGE/2025/06/1750127883864-cdbd9c71-70af-446b-abce-25c55e36535d',
              mediaId: '123e4567-e89b-12d3-a456-426614174000',
              url: 'https://cdn.redai.vn/business/IMAGE/2025/06/1750127883864-cdbd9c71-70af-446b-abce-25c55e36535d?expires=1750229454&signature=abc123'
            }
          ]
        }
      },
      {
        operation: 'update',
        id: 123,
        data: {
          versionName: 'Bản nâng cao - Cập nhật',
          description: 'Phiên bản nâng cao với nhiều tính năng bổ sung',
          imageOperations: [
            {
              operation: 'add',
              mediaId: '987f6543-e21c-34d5-b678-539725836241'
            },
            {
              operation: 'delete',
              entityMediaId: 123
            }
          ]
        }
      },
      {
        operation: 'delete',
        id: 124
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DigitalProductVersionOperationDto)
  versions?: DigitalProductVersionOperationDto[];

  @ApiProperty({
    description: 'Danh sách thao tác với hình ảnh sản phẩm (ADD/DELETE)',
    type: [ProductImageOperationDto],
    example: [
      {
        operation: 'add',
        mediaId: 'dcc8f803-04fc-49b3-953a-e660505ac353'
      },
      {
        operation: 'delete',
        entityMediaId: 456
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductImageOperationDto)
  images?: ProductImageOperationDto[];
}

// Union type cho price (tương thích với entity)
export type ProductPriceType = HasPriceDto | StringPriceDto;

/**
 * DTO hoàn chỉnh cho việc cập nhật sản phẩm số
 * Sử dụng cấu trúc nhóm theo chức năng để dễ đọc và maintain
 * Bao gồm tất cả thuộc tính từ:
 * - customer_products table (thông qua basicInfo và pricing)
 * - digital_products table (thông qua digitalInfo)
 * - digital_product_versions table (thông qua operations.versions)
 * - entity_has_media table (thông qua operations.images)
 */
export class CompleteUpdateDigitalProductDto {
  // ========== THÔNG TIN CƠ BẢN ==========
  @ApiProperty({
    description: 'Thông tin cơ bản sản phẩm (tên, mô tả, loại, tags)',
    type: BasicInfoDto,
    example: {
      name: 'Khóa học lập trình Python cơ bản',
      description: 'Khóa học lập trình Python từ cơ bản đến nâng cao với nhiều bài tập thực hành',
      productType: 'DIGITAL',
      tags: ['khóa học', 'lập trình', 'python', 'online']
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => BasicInfoDto)
  basicInfo?: BasicInfoDto;

  // ========== THÔNG TIN GIÁ ==========
  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    type: PricingInfoDto,
    example: {
      price: {
        listPrice: 500000,
        salePrice: 450000,
        currency: 'VND'
      },
      typePrice: 'HAS_PRICE'
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PricingInfoDto)
  pricing?: PricingInfoDto;

  // ========== THÔNG TIN SỐ HÓA ==========
  @ApiProperty({
    description: 'Thông tin số hóa sản phẩm (phương thức giao hàng, thời gian)',
    type: DigitalInfoDto,
    example: {
      deliveryMethod: 'Tải xuống trực tiếp',
      deliveryTime: 'Ngay lập tức sau khi thanh toán',
      waitingTime: 'Xử lý trong 5 phút'
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DigitalInfoDto)
  digitalInfo?: DigitalInfoDto;

  // ========== CUSTOM FIELDS ==========
  @ApiProperty({
    description: 'Danh sách custom fields',
    type: [CustomFieldInputDto],
    example: [
      {
        customFieldId: 1,
        value: { value: 'Tiếng Việt' }
      },
      {
        customFieldId: 2,
        value: { value: 'PDF' }
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];

  // Trường status đã được loại bỏ vì người dùng không có quyền thay đổi
  // Status mặc định sẽ là PENDING khi tạo/sửa sản phẩm

  // ========== THAO TÁC ==========
  @ApiProperty({
    description: 'Các thao tác với phiên bản và hình ảnh sản phẩm',
    type: DigitalOperationsDto,
    example: {
      versions: [
        {
          operation: 'add',
          data: {
            versionName: 'Bản tiêu chuẩn',
            description: 'Phiên bản cơ bản với đầy đủ tính năng chính',
            customFields: { language: 'Tiếng Việt', format: 'PDF' },
            contentLink: 'https://example.com/download/course-v1.pdf',
            sku: 'COURSE-STD-V1',
            minQuantity: 1,
            maxQuantity: 1,
            imageOperations: [
              {
                operation: 'add',
                mediaId: '123e4567-e89b-12d3-a456-426614174000'
              }
            ]
          }
        },
        {
          operation: 'update',
          id: 123,
          data: {
            versionName: 'Bản nâng cao - Cập nhật',
            description: 'Phiên bản nâng cao với nhiều tính năng bổ sung',
            imageOperations: [
              {
                operation: 'add',
                mediaId: '987f6543-e21c-34d5-b678-539725836241'
              },
              {
                operation: 'delete',
                entityMediaId: 123
              }
            ]
          }
        },
        {
          operation: 'delete',
          id: 124
        }
      ],
      images: [
        {
          operation: 'add',
          mediaId: 'dcc8f803-04fc-49b3-953a-e660505ac353'
        },
        {
          operation: 'delete',
          entityMediaId: 456
        }
      ]
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DigitalOperationsDto)
  operations?: DigitalOperationsDto;
}
