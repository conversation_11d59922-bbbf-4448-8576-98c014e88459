import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { AppException } from '@common/exceptions';
import { SERVICE_PRODUCT_ERROR_CODES } from '@modules/business/exceptions';
import { CustomerProduct, ServiceProduct, ServicePackageOption } from '@modules/business/entities';
import { CustomFieldInterface } from '@modules/business/interfaces/custom-field.interface';
import { ProductTypeEnum } from '@modules/business/enums';
import {
  CustomerProductRepository,
  ServiceProductRepository,
  ServicePackageOptionRepository,
  EntityHasMediaRepository,
} from '@modules/business/repositories';
import { MediaRepository } from '@modules/data/media/repositories';
import { CompleteUpdateServiceProductDto } from '@modules/business/user/dto/service-product';
import { CompleteServiceProductResponseDto } from '@modules/business/user/dto/service-product';
import { ServicePackageOperationDto, PackageOperationType } from '@modules/business/user/dto/service-product';

/**
 * Service xử lý các thao tác hoàn chỉnh với Service Product
 * Bao gồm customer_products + service_products + packages + images
 */
@Injectable()
export class CompleteServiceProductService {
  private readonly logger = new Logger(CompleteServiceProductService.name);

  constructor(
    private readonly customerProductRepository: CustomerProductRepository,
    private readonly serviceProductRepository: ServiceProductRepository,
    private readonly servicePackageOptionRepository: ServicePackageOptionRepository,
    private readonly entityHasMediaRepository: EntityHasMediaRepository,
    private readonly mediaRepository: MediaRepository,
  ) {}

  /**
   * Cập nhật hoàn chỉnh service product
   * @param id ID của sản phẩm
   * @param dto DTO chứa toàn bộ thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã cập nhật
   */
  @Transactional()
  async updateCompleteServiceProduct(
    id: number,
    dto: CompleteUpdateServiceProductDto,
    userId: number,
  ): Promise<CompleteServiceProductResponseDto> {
    try {
      this.logger.log(`Cập nhật hoàn chỉnh service product ID=${id} cho userId=${userId}`);

      // 1. Kiểm tra sản phẩm tồn tại và thuộc về user
      const existingProduct = await this.validateProductOwnership(id, userId);

      // 2. Cập nhật customer_products table
      const updatedCustomerProduct = await this.updateCustomerProduct(existingProduct, dto);

      // 3. Cập nhật/tạo service_products table
      const serviceProduct = await this.updateOrCreateServiceProduct(id, dto);

      // 4. Xử lý package operations (ADD/UPDATE/DELETE) và ảnh packages
      const packageResult = await this.processPackageOperations(id, dto.operations?.packages || []);

      // 5. Xử lý image operations (ADD/DELETE) cho product level
      await this.processImageOperations(id, dto.operations?.images || []);

      // 6. Tạo response hoàn chỉnh
      return await this.buildCompleteResponse(updatedCustomerProduct, serviceProduct, packageResult.packages);

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi cập nhật service product: ${error.message}`, error.stack);
      throw new AppException(
        SERVICE_PRODUCT_ERROR_CODES.SERVICE_UPDATE_FAILED,
        `Lỗi khi cập nhật sản phẩm dịch vụ: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết hoàn chỉnh service product
   * @param id ID của sản phẩm
   * @param userId ID của người dùng hiện tại
   * @returns Chi tiết sản phẩm hoàn chỉnh
   */
  async getCompleteServiceProduct(
    id: number,
    userId: number,
  ): Promise<CompleteServiceProductResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết hoàn chỉnh service product ID=${id} cho userId=${userId}`);

      // 1. Lấy customer product
      const customerProduct = await this.validateProductOwnership(id, userId);

      // 2. Lấy service product data
      const serviceProduct = await this.serviceProductRepository.findById(id);

      // 3. Lấy packages data
      const packages = await this.servicePackageOptionRepository.findByServiceProductId(id);

      // 4. Build response
      const response = await this.buildCompleteResponse(customerProduct, serviceProduct || undefined, packages);

      return response;

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi lấy chi tiết service product: ${error.message}`, error.stack);
      throw new AppException(
        SERVICE_PRODUCT_ERROR_CODES.SERVICE_NOT_FOUND,
        `Lỗi khi lấy chi tiết sản phẩm dịch vụ: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra quyền sở hữu sản phẩm và validate productType
   */
  private async validateProductOwnership(id: number, userId: number): Promise<CustomerProduct> {
    const product = await this.customerProductRepository.findByIdAndUserId(id, userId);

    if (!product) {
      throw new AppException(
        SERVICE_PRODUCT_ERROR_CODES.SERVICE_NOT_FOUND,
        'Không tìm thấy sản phẩm hoặc bạn không có quyền truy cập',
      );
    }

    // Kiểm tra productType phải là SERVICE
    if (product.productType !== ProductTypeEnum.SERVICE) {
      throw new AppException(
        SERVICE_PRODUCT_ERROR_CODES.INVALID_PRODUCT_TYPE,
        `Sản phẩm này có loại '${product.productType}', không thể sử dụng API cập nhật Service Product. Vui lòng sử dụng API phù hợp với loại sản phẩm.`,
      );
    }

    return product;
  }

  /**
   * Cập nhật customer_products table
   */
  private async updateCustomerProduct(
    existingProduct: CustomerProduct,
    dto: CompleteUpdateServiceProductDto,
  ): Promise<CustomerProduct> {
    const updateData: Partial<CustomerProduct> = {};

    // Basic info updates
    if (dto.basicInfo?.name) {
      updateData.name = dto.basicInfo.name;
    }
    if (dto.basicInfo?.description !== undefined) {
      updateData.description = dto.basicInfo.description;
    }
    if (dto.basicInfo?.tags) {
      updateData.tags = dto.basicInfo.tags;
    }

    // Pricing updates
    if (dto.pricing?.price) {
      updateData.price = dto.pricing.price;
    }
    if (dto.pricing?.typePrice) {
      updateData.typePrice = dto.pricing.typePrice;
    }

    // Custom fields updates
    if (dto.customFields) {
      updateData.customFields = dto.customFields as unknown as CustomFieldInterface;
    }

    // Update if there are changes
    if (Object.keys(updateData).length > 0) {
      const updated = await this.customerProductRepository.update(existingProduct.id, updateData);
      return updated || existingProduct;
    }

    return existingProduct;
  }

  /**
   * Cập nhật/tạo service_products table
   */
  private async updateOrCreateServiceProduct(
    id: number,
    dto: CompleteUpdateServiceProductDto,
  ): Promise<ServiceProduct> {
    let serviceProduct = await this.serviceProductRepository.findById(id);

    if (serviceProduct) {
      // Update existing service product
      const updateData: Partial<ServiceProduct> = {};

      if (dto.serviceInfo?.serviceType) {
        updateData.serviceType = dto.serviceInfo.serviceType;
      }
      if (dto.serviceInfo?.location !== undefined) {
        updateData.location = dto.serviceInfo.location;
      }
      if (dto.serviceInfo?.providerName !== undefined) {
        updateData.providerName = dto.serviceInfo.providerName;
      }

      if (Object.keys(updateData).length > 0) {
        updateData.updatedAt = Date.now();
        const updated = await this.serviceProductRepository.update(id, updateData);
        if (!updated) {
          throw new AppException(SERVICE_PRODUCT_ERROR_CODES.SERVICE_UPDATE_FAILED);
        }
        serviceProduct = updated;
      }
    } else {
      // Tạo mới service product record từ serviceInfo
      const createData: Partial<ServiceProduct> = {
        id: id, // Same as customer product ID
        serviceType: dto.serviceInfo?.serviceType || 'general',
        location: dto.serviceInfo?.location || null,
        providerName: dto.serviceInfo?.providerName || null,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      serviceProduct = await this.serviceProductRepository.create(createData);
    }

    return serviceProduct;
  }

  /**
   * Xử lý package operations (ADD/UPDATE/DELETE)
   */
  private async processPackageOperations(
    serviceProductId: number,
    operations: ServicePackageOperationDto[],
  ): Promise<{ packages: ServicePackageOption[] }> {
    for (const operation of operations) {
      switch (operation.operation) {
        case PackageOperationType.ADD:
          if (operation.data) {
            const newPackage = await this.servicePackageOptionRepository.create({
              serviceProductsId: serviceProductId,
              name: operation.data.name || '',
              description: operation.data.description || null,
              price: operation.data.price,
              duration: operation.data.duration || null,
              unit: operation.data.unit || null,
              features: operation.data.features || null,
              isActive: operation.data.isActive ?? true,
              isLimited: operation.data.isLimited ?? false,
              maxSales: operation.data.maxSales || null,
              createdAt: Date.now(),
              updatedAt: Date.now(),
            });

            // Process image operations for this package
            if (operation.data.imageOperations && operation.data.imageOperations.length > 0) {
              this.logger.log(`Processing ${operation.data.imageOperations.length} image operations for new package ID=${newPackage.id}`);
              await this.processPackageImageOperations(newPackage.id, operation.data.imageOperations);
            }
          }
          break;

        case PackageOperationType.UPDATE:
          if (operation.id && operation.data) {
            const updateData: Partial<ServicePackageOption> = {};

            if (operation.data.name) updateData.name = operation.data.name;
            if (operation.data.description !== undefined) updateData.description = operation.data.description;
            if (operation.data.price) updateData.price = operation.data.price;
            if (operation.data.duration !== undefined) updateData.duration = operation.data.duration;
            if (operation.data.unit !== undefined) updateData.unit = operation.data.unit;
            if (operation.data.features !== undefined) updateData.features = operation.data.features;
            if (operation.data.isActive !== undefined) updateData.isActive = operation.data.isActive;
            if (operation.data.isLimited !== undefined) updateData.isLimited = operation.data.isLimited;
            if (operation.data.maxSales !== undefined) updateData.maxSales = operation.data.maxSales;

            if (Object.keys(updateData).length > 0) {
              updateData.updatedAt = Date.now();
              await this.servicePackageOptionRepository.update(operation.id, updateData);
            }

            // Process image operations for this package
            if (operation.data.imageOperations && operation.data.imageOperations.length > 0) {
              await this.processPackageImageOperations(operation.id, operation.data.imageOperations);
            }
          }
          break;

        case PackageOperationType.DELETE:
          if (operation.id) {
            await this.servicePackageOptionRepository.delete(operation.id);
          }
          break;
      }
    }

    // Return all current packages for this service product
    const allPackages = await this.servicePackageOptionRepository.findByServiceProductId(serviceProductId);
    return { packages: allPackages };
  }

  /**
   * Xử lý image operations (ADD/DELETE) cho product level
   */
  private async processImageOperations(
    serviceProductId: number,
    operations: Array<{ operation: 'add' | 'delete'; mediaId?: string; entityHasMediaId?: number }>,
  ): Promise<void> {
    for (const operation of operations) {
      switch (operation.operation) {
        case 'add':
          if (operation.mediaId) {
            // Create entity_has_media link for product level
            await this.entityHasMediaRepository.create({
              productId: serviceProductId,
              mediaId: operation.mediaId,
              physicalVarial: null,
              ticketVarial: null,
              versionId: null,
              productComboId: null,
              productPlanVarialId: null, // null = product level image
            });
          }
          break;

        case 'delete':
          if (operation.entityHasMediaId) {
            // Delete entity_has_media record by ID
            await this.entityHasMediaRepository.delete(operation.entityHasMediaId);
          }
          break;
      }
    }
  }

  /**
   * Xử lý image operations cho package level
   */
  private async processPackageImageOperations(
    packageId: number,
    operations: Array<{ operation: 'add' | 'delete'; mediaId?: string; entityHasMediaId?: number }>,
  ): Promise<void> {
    for (const operation of operations) {
      switch (operation.operation) {
        case 'add':
          if (operation.mediaId) {
            this.logger.log(`Creating package image link: packageId=${packageId}, mediaId=${operation.mediaId}`);

            // ✅ Validate media tồn tại trước khi tạo link
            const mediaExists = await this.mediaRepository.findByIds([operation.mediaId]);
            if (mediaExists.length === 0) {
              this.logger.warn(`Media ID ${operation.mediaId} không tồn tại, bỏ qua tạo link`);
              continue;
            }

            // Create entity_has_media link for package level
            const createdLink = await this.entityHasMediaRepository.create({
              productId: null, // Package level không cần productId
              mediaId: operation.mediaId,
              physicalVarial: null,
              ticketVarial: null,
              versionId: null,
              productComboId: null,
              productPlanVarialId: packageId, // packageId = package level image
            });
            this.logger.log(`Created package image link: ID=${createdLink.id}`);
          }
          break;

        case 'delete':
          if (operation.entityHasMediaId) {
            // Delete entity_has_media record by ID
            await this.entityHasMediaRepository.delete(operation.entityHasMediaId);
          }
          break;
      }
    }
  }

  /**
   * Load images cho package
   */
  private async loadPackageImages(packageId: number): Promise<Record<string, unknown>[]> {
    try {
      this.logger.log(`Loading images for package ID=${packageId}`);

      // Tìm entity_has_media records cho package này
      const packageLevelLinks = await this.entityHasMediaRepository.findByProductPlanVarialId(packageId);
      this.logger.log(`Found ${packageLevelLinks.length} media links for package ${packageId}`);

      // Lấy danh sách mediaId để query media_data
      const mediaIds = packageLevelLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      this.logger.log(`Media IDs for package ${packageId}: ${JSON.stringify(mediaIds)}`);

      if (mediaIds.length > 0) {
        const mediaRecords = await this.mediaRepository.findByIds(mediaIds);
        this.logger.log(`Found ${mediaRecords.length} media records for package ${packageId}`);

        return mediaRecords.map(media => ({
          id: media.id,
          url: media.storageKey || '',
          name: media.name || '',
          size: media.size || 0,
        })) as Record<string, unknown>[];
      }

      return [];
    } catch (error) {
      this.logger.warn(`Không thể load images cho package ${packageId}: ${error.message}`);
      return [];
    }
  }

  /**
   * Build complete response
   */
  private async buildCompleteResponse(
    customerProduct: CustomerProduct,
    serviceProduct?: ServiceProduct,
    packages?: ServicePackageOption[],
  ): Promise<CompleteServiceProductResponseDto> {
    const response = new CompleteServiceProductResponseDto();

    // Map customer product data
    response.id = customerProduct.id;
    response.name = customerProduct.name;
    response.description = customerProduct.description || undefined;
    response.productType = customerProduct.productType;
    response.price = customerProduct.price as unknown as Record<string, unknown> || undefined;
    response.typePrice = customerProduct.typePrice || undefined;
    response.tags = customerProduct.tags || undefined;
    response.customFields = customerProduct.customFields as unknown as Record<string, unknown>[] || undefined;
    response.createdAt = customerProduct.createdAt;
    response.updatedAt = customerProduct.updatedAt;
    response.userId = customerProduct.userId ?? 0;

    // Map service product data
    response.serviceType = serviceProduct?.serviceType || 'general';
    response.location = serviceProduct?.location || undefined;
    response.providerName = serviceProduct?.providerName || undefined;

    // Map packages data với images
    response.packages = [];
    if (packages && packages.length > 0) {
      for (const packageItem of packages) {
        // Load images cho từng package
        const packageImages = await this.loadPackageImages(packageItem.id);

        response.packages.push({
          id: packageItem.id,
          serviceProductsId: packageItem.serviceProductsId || 0,
          name: packageItem.name,
          description: packageItem.description || undefined,
          price: packageItem.price,
          duration: packageItem.duration || undefined,
          unit: packageItem.unit || undefined,
          features: packageItem.features || undefined,
          isActive: packageItem.isActive || undefined,
          isLimited: packageItem.isLimited || undefined,
          maxSales: packageItem.maxSales || undefined,
          images: packageImages,
          createdAt: packageItem.createdAt,
          updatedAt: packageItem.updatedAt,
        });
      }
    }

    // Load images từ entity_has_media join với media_data
    try {
      const mediaLinks = await this.entityHasMediaRepository.findByProductId(customerProduct.id);

      // Filter chỉ lấy ảnh product level (product_id có giá trị, product_plan_varial_id = null)
      const productLevelLinks = mediaLinks.filter(link =>
        link.productId &&
        !link.physicalVarial &&
        !link.ticketVarial &&
        !link.versionId &&
        !link.productComboId &&
        !link.productPlanVarialId  // product_plan_varial_id = null nghĩa là product level
      );

      const mediaIds = productLevelLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length > 0) {
        const mediaRecords = await this.mediaRepository.findByIds(mediaIds);
        response.images = mediaRecords.map(media => ({
          id: media.id,
          url: media.storageKey || '',
          name: media.name || '',
          size: media.size || 0,
        })) as Record<string, unknown>[];
      } else {
        response.images = [];
      }
    } catch (error) {
      this.logger.warn(`Không thể load product images: ${error.message}`);
      response.images = [];
    }

    return response;
  }
}
