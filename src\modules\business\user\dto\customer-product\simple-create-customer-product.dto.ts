import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';
import { ProductTypeEnum } from '@modules/business/enums';

/**
 * DTO đơn giản cho việc tạo sản phẩm khách hàng mới
 * Chỉ bao gồm 3 trường cơ bản: name, description, productType
 * Sử dụng cho việc tạo sản phẩm nhanh với thông tin tối thiểu
 */
export class SimpleCreateCustomerProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: '<PERSON>o thun nam cao cấp',
    maxLength: 500,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(500)
  name: string;

  @ApiProperty({
    description: 'Mô tả chi tiết sản phẩm',
    example: '<PERSON><PERSON> thun nam chất liệu cotton 100%, thiết kế hiện đại',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
  })
  @IsEnum(ProductTypeEnum)
  @IsNotEmpty()
  productType: ProductTypeEnum;
}
