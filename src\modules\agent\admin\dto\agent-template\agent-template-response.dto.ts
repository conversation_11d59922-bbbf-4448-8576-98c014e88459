import { AgentTemplateStatus } from '@modules/agent/constants';
import { ConvertConfig } from '@modules/agent/interfaces/convert-config.interface';
import { ProfileAgent } from '@modules/agent/interfaces/profile-agent.interface';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ModelConfigResponseDto } from '../agent-system';
import { AgentMemoryDto } from './agent-memory.dto';
import { ConversionConfigDto } from './conversion-config.dto';

/**
 * DTO cho phản hồi thông tin chi tiết agent template
 */
export class AgentTemplateDetailDto {
  /**
   * ID của agent template
   */
  @ApiProperty({
    description: 'ID của agent template',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  /**
   * Tên hiển thị của agent
   */
  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  name: string;

  /**
   * URL avatar của agent
   */
  @ApiPropertyOptional({
    description: 'URL avatar của agent',
    example: 'https://example.com/system-assistant.png',
  })
  avatar: string | null;

  /**
   * Cấu hình model AI
   */
  @ApiProperty({
    description: 'Cấu hình model AI',
    type: ModelConfigResponseDto,
  })
  modelConfig: ModelConfigResponseDto;

  /**
   * Thông tin hồ sơ mẫu
   */
  @ApiPropertyOptional({
    description: 'Thông tin hồ sơ mẫu',
    type: Object,
  })
  profile?: ProfileAgent;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example: 'Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc',
  })
  instruction?: string;

  /**
   * Thông tin vector store
   */
  @ApiPropertyOptional({
    description: 'Thông tin vector store',
    type: Object,
    example: {
      vectorStoreId: 'vector-store-1',
      vectorStoreName: 'Vector Store 1',
    },
  })
  vector?: {
    vectorStoreId: string;
    vectorStoreName: string;
  };

  /**
   * Cấu hình conversion
   */
  @ApiPropertyOptional({
    description: 'Cấu hình conversion',
    type: [ConversionConfigDto],
    example: [
      {
        name: 'email',
        type: 'string',
        description: 'Địa chỉ email người dùng',
        required: true,
        active: true
      }
    ]
  })
  conversion?: ConversionConfigDto[];

  /**
   * Cấu hình chuyển đổi (legacy - deprecated)
   */
  @ApiPropertyOptional({
    description: 'Cấu hình chuyển đổi (legacy - deprecated)',
    type: Object,
  })
  convertConfig?: ConvertConfig;

  /**
   * Trạng thái của agent template
   */
  @ApiProperty({
    description: 'Trạng thái của agent template',
    enum: AgentTemplateStatus,
    example: AgentTemplateStatus.ACTIVE,
  })
  status: AgentTemplateStatus;

  /**
   * ID của loại agent
   */
  @ApiProperty({
    description: 'ID của loại agent',
    example: 1,
  })
  typeId: number;

  /**
   * Tên loại agent
   */
  @ApiProperty({
    description: 'Tên loại agent',
    example: 'Chatbot',
  })
  typeName: string;

  /**
   * ID của base model (legacy)
   */
  @ApiPropertyOptional({
    description: 'ID của base model (legacy)',
    example: 'base-model-uuid',
  })
  modelBaseId?: string | null;

  /**
   * ID của system model
   */
  @ApiPropertyOptional({
    description: 'ID của system model',
    example: 'model-system-uuid',
  })
  modelSystemId?: string | null;

  /**
   * Trạng thái có thể bán
   */
  @ApiProperty({
    description: 'Trạng thái có thể bán',
    example: true,
  })
  isForSale: boolean;

  /**
   * Memories của agent
   */
  @ApiPropertyOptional({
    description: 'Memories của agent',
    type: [AgentMemoryDto],
    example: [
      {
        content: 'memories'
      }
    ]
  })
  memories?: AgentMemoryDto[];

  /**
   * Thông tin người tạo
   */
  @ApiPropertyOptional({
    description: 'Thông tin người tạo',
    type: Object,
    example: {
      employeeId: 1,
      name: 'John Doe',
      avatar: 'https://example.com/avatar.jpg',
    },
  })
  createdBy?: {
    employeeId: number;
    name: string;
    avatar: string;
  } | null;

  /**
   * Thông tin người cập nhật
   */
  @ApiPropertyOptional({
    description: 'Thông tin người cập nhật',
    type: Object,
    example: {
      employeeId: 1,
      name: 'John Doe',
      avatar: 'https://example.com/avatar.jpg',
    },
  })
  updatedBy?: {
    employeeId: number;
    name: string;
    avatar: string;
  } | null;

  /**
   * Thời gian tạo
   */
  @ApiProperty({
    description: 'Thời gian tạo',
    example: '2023-12-01T10:00:00Z',
  })
  createdAt: Date;

  /**
   * Thời gian cập nhật
   */
  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: '2023-12-01T10:00:00Z',
  })
  updatedAt: Date;
}

/**
 * DTO cho phản hồi thông tin tóm tắt agent template trong danh sách
 */
export class AgentTemplateListItemDto {
  /**
   * ID của agent template
   */
  @ApiProperty({
    description: 'ID của agent template',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  /**
   * Tên hiển thị của agent
   */
  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  name: string;

  /**
   * URL avatar của agent
   */
  @ApiPropertyOptional({
    description: 'URL avatar của agent',
    example: 'https://example.com/system-assistant.png',
  })
  avatar?: string | null;

  /**
   * ID của loại agent
   */
  @ApiProperty({
    description: 'ID của loại agent',
    example: 1,
  })
  typeId: number;

  /**
   * Tên loại agent
   */
  @ApiProperty({
    description: 'Tên loại agent',
    example: 'Chatbot',
  })
  typeName: string;

  /**
   * Trạng thái của agent template
   */
  @ApiProperty({
    description: 'Trạng thái của agent template',
    enum: AgentTemplateStatus,
    example: AgentTemplateStatus.DRAFT,
  })
  status: AgentTemplateStatus;

  /**
   * Trạng thái có thể bán
   */
  @ApiProperty({
    description: 'Trạng thái có thể bán',
    example: true,
  })
  isForSale: boolean;

  /**
   * Thông tin người tạo
   */
  @ApiPropertyOptional({
    description: 'Thông tin người tạo',
    type: Object,
    example: {
      employeeId: 1,
      name: 'John Doe',
      avatar: 'https://example.com/avatar.jpg',
    },
  })
  createdBy?: {
    employeeId: number;
    name: string;
    avatar: string;
  } | null;

  /**
   * Thông tin người cập nhật
   */
  @ApiPropertyOptional({
    description: 'Thông tin người cập nhật',
    type: Object,
    example: {
      employeeId: 1,
      name: 'John Doe',
      avatar: 'https://example.com/avatar.jpg',
    },
  })
  updatedBy?: {
    employeeId: number;
    name: string;
    avatar: string;
  } | null;

  /**
   * Thời gian tạo
   */
  @ApiProperty({
    description: 'Thời gian tạo',
    example: '2023-12-01T10:00:00Z',
  })
  createdAt: Date;

  /**
   * Thời gian cập nhật
   */
  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: '2023-12-01T10:00:00Z',
  })
  updatedAt: Date;
}
