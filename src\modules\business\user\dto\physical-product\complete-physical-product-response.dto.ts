import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { ProductTypeEnum, EntityStatusEnum, PriceTypeEnum } from '@modules/business/enums';
import { ShipmentConfigType } from '@modules/business/interfaces';

/**
 * DTO cho thông tin cơ bản sản phẩm trong response
 */
export class BasicInfoResponseDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Áo thun nam cao cấp - Phiên bản mới',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: '<PERSON><PERSON> tả sản phẩm',
    example: 'Áo thun nam chất liệu cotton 100%, thiết kế hiện đại với logo mới',
    required: false,
  })
  @Expose()
  description?: string;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
  })
  @Expose()
  productType: ProductTypeEnum;

  @ApiProperty({
    description: 'Danh sách tags',
    type: [String],
    example: ['thời trang', 'nam', 'cotton', 'premium'],
    required: false,
  })
  @Expose()
  tags?: string[];
}

/**
 * DTO cho thông tin giá trong response
 */
export class PricingResponseDto {
  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    example: {
      listPrice: 500000,
      salePrice: 450000,
      currency: 'VND'
    },
    required: false,
  })
  @Expose()
  price?: any;

  @ApiProperty({
    description: 'Loại giá sản phẩm',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
  })
  @Expose()
  typePrice?: PriceTypeEnum;
}

/**
 * DTO cho thông tin vật lý sản phẩm trong response
 */
export class PhysicalInfoResponseDto {
  @ApiProperty({
    description: 'Số lượng tồn kho',
    example: 150,
    required: false,
  })
  @Expose()
  stockQuantity?: number;

  @ApiProperty({
    description: 'Mã SKU sản phẩm',
    example: 'SHIRT-001-V2',
    required: false,
  })
  @Expose()
  sku?: string;

  @ApiProperty({
    description: 'Mã vạch sản phẩm',
    example: '1234567890123',
    required: false,
  })
  @Expose()
  barcode?: string;

  @ApiProperty({
    description: 'Cấu hình vận chuyển',
    example: {
      widthCm: 25,
      heightCm: 5,
      lengthCm: 30,
      weightGram: 200
    },
    required: false,
  })
  @Expose()
  shipmentConfig?: ShipmentConfigType;
}

/**
 * DTO cho thông tin hình ảnh trong response
 */
export class ImageResponseDto {
  @ApiProperty({
    description: 'ID hình ảnh',
    example: 'img-001',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'URL hình ảnh CDN',
    example: 'https://cdn.redai.vn/products/shirt-001-v2/main-image.jpg',
  })
  @Expose()
  url: string;

  @ApiProperty({
    description: 'Key lưu trữ hình ảnh',
    example: 'products/shirt-001-v2/main-image.jpg',
  })
  @Expose()
  key: string;

  @ApiProperty({
    description: 'Alt text cho hình ảnh',
    example: 'Áo thun nam cao cấp - Ảnh chính',
    required: false,
  })
  @Expose()
  alt?: string;

  @ApiProperty({
    description: 'Có phải ảnh chính không',
    example: true,
    required: false,
  })
  @Expose()
  isPrimary?: boolean;

  @ApiProperty({
    description: 'Media ID',
    example: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2',
  })
  @Expose()
  mediaId: string;

  @ApiProperty({
    description: 'Thời gian upload',
    example: '2025-06-17T10:30:00Z',
  })
  @Expose()
  uploadedAt: string;
}

/**
 * DTO cho biến thể sản phẩm trong response
 */
export class VariantResponseDto {
  @ApiProperty({
    description: 'ID biến thể (string hoặc number)',
    example: 'VAR-001',
  })
  @Expose()
  id: string | number;

  @ApiProperty({
    description: 'Tên biến thể',
    example: 'Áo thun đỏ size M',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Mã SKU biến thể',
    example: 'SHIRT-RED-M-001',
    required: false,
  })
  @Expose()
  sku?: string;

  @ApiProperty({
    description: 'Thuộc tính biến thể',
    example: {
      color: 'Đỏ',
      size: 'M'
    },
    required: false,
  })
  @Expose()
  attributes?: any;

  @ApiProperty({
    description: 'Giá biến thể',
    example: {
      listPrice: 300000,
      salePrice: 250000,
      currency: 'VND'
    },
    required: false,
  })
  @Expose()
  price?: any;

  @ApiProperty({
    description: 'Số lượng tồn kho',
    example: 50,
    required: false,
  })
  @Expose()
  stockQuantity?: number;

  @ApiProperty({
    description: 'Cấu hình vận chuyển',
    example: {
      widthCm: 20,
      heightCm: 3,
      lengthCm: 25,
      weightGram: 150
    },
    required: false,
  })
  @Expose()
  shipmentConfig?: ShipmentConfigType;

  @ApiProperty({
    description: 'Danh sách hình ảnh biến thể',
    type: [ImageResponseDto],
    required: false,
  })
  @Expose()
  @Type(() => ImageResponseDto)
  images?: ImageResponseDto[];

  @ApiProperty({
    description: 'Trạng thái biến thể',
    example: 'ACTIVE',
    required: false,
  })
  @Expose()
  status?: string;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: '2025-06-17T10:32:00Z',
    required: false,
  })
  @Expose()
  createdAt?: string;

  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: '2025-06-17T10:32:00Z',
    required: false,
  })
  @Expose()
  updatedAt?: string;
}

/**
 * DTO cho kết quả thao tác với biến thể
 */
export class VariantOperationResultDto {
  @ApiProperty({
    description: 'Danh sách biến thể đã thêm',
    example: [
      {
        tempId: 'temp-var-001',
        newId: 'VAR-001',
        status: 'SUCCESS'
      }
    ],
    required: false,
  })
  @Expose()
  added?: Array<{
    tempId: string;
    newId: string;
    status: string;
  }>;

  @ApiProperty({
    description: 'Danh sách biến thể đã cập nhật',
    example: [
      {
        id: 123,
        status: 'SUCCESS'
      }
    ],
    required: false,
  })
  @Expose()
  updated?: Array<{
    id: number;
    status: string;
  }>;

  @ApiProperty({
    description: 'Danh sách biến thể đã xóa',
    example: [
      {
        id: 124,
        status: 'SUCCESS'
      }
    ],
    required: false,
  })
  @Expose()
  deleted?: Array<{
    id: number;
    status: string;
  }>;
}

/**
 * DTO cho kết quả thao tác với hình ảnh
 */
export class ImageOperationResultDto {
  @ApiProperty({
    description: 'Danh sách hình ảnh đã thêm',
    example: [
      {
        mediaId: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2',
        newImageId: 'img-001',
        status: 'SUCCESS'
      }
    ],
    required: false,
  })
  @Expose()
  added?: Array<{
    mediaId: string;
    newImageId: string;
    status: string;
  }>;

  @ApiProperty({
    description: 'Danh sách hình ảnh đã xóa',
    example: [
      {
        key: 'old-product-image.jpg',
        status: 'SUCCESS'
      }
    ],
    required: false,
  })
  @Expose()
  deleted?: Array<{
    key: string;
    status: string;
  }>;
}

/**
 * DTO cho kết quả các thao tác
 */
export class OperationResultsDto {
  @ApiProperty({
    description: 'Kết quả thao tác với biến thể',
    type: VariantOperationResultDto,
    required: false,
  })
  @Expose()
  @Type(() => VariantOperationResultDto)
  variants?: VariantOperationResultDto;

  @ApiProperty({
    description: 'Kết quả thao tác với hình ảnh',
    type: ImageOperationResultDto,
    required: false,
  })
  @Expose()
  @Type(() => ImageOperationResultDto)
  images?: ImageOperationResultDto;
}

/**
 * Response DTO hoàn chỉnh cho physical product update
 * Theo cấu trúc mới phù hợp với reference response
 */
export class CompletePhysicalProductResponseDto {
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 'PROD-001-2025',
  })
  @Expose()
  productId: string;

  @ApiProperty({
    description: 'Thông tin cơ bản sản phẩm',
    type: BasicInfoResponseDto,
  })
  @Expose()
  @Type(() => BasicInfoResponseDto)
  basicInfo: BasicInfoResponseDto;

  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    type: PricingResponseDto,
  })
  @Expose()
  @Type(() => PricingResponseDto)
  pricing: PricingResponseDto;

  @ApiProperty({
    description: 'Thông tin vật lý sản phẩm',
    type: PhysicalInfoResponseDto,
  })
  @Expose()
  @Type(() => PhysicalInfoResponseDto)
  physicalInfo: PhysicalInfoResponseDto;

  @ApiProperty({
    description: 'Danh sách custom fields',
    example: [
      {
        customFieldId: 1,
        value: { value: 'XL' }
      },
      {
        customFieldId: 2,
        value: { value: 'Đỏ' }
      }
    ],
    required: false,
  })
  @Expose()
  customFields?: Array<{
    customFieldId: number;
    value: { value: string };
  }>;

  @ApiProperty({
    description: 'Danh sách hình ảnh sản phẩm',
    type: [ImageResponseDto],
    required: false,
  })
  @Expose()
  @Type(() => ImageResponseDto)
  images?: ImageResponseDto[];

  @ApiProperty({
    description: 'Danh sách biến thể sản phẩm',
    type: [VariantResponseDto],
    required: false,
  })
  @Expose()
  @Type(() => VariantResponseDto)
  variants?: VariantResponseDto[];

  @ApiProperty({
    description: 'Kết quả các thao tác đã thực hiện',
    type: OperationResultsDto,
    required: false,
  })
  @Expose()
  @Type(() => OperationResultsDto)
  operationResults?: OperationResultsDto;
}
