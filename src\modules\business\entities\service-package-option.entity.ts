import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ProductPrice } from '@modules/business/interfaces';

/**
 * Entity đại diện cho bảng service_packages_option trong cơ sở dữ liệu
 * Bảng lưu thông tin các gói dịch vụ thuộc sản phẩm dịch vụ
 */
@Entity('service_packages_option')
export class ServicePackageOption {
  /**
   * ID tự tăng, khóa chính của gói dịch vụ
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Tên của gói dịch vụ
   */
  @Column({
    name: 'name',
    type: 'varchar',
    length: 255,
    nullable: false,
    comment: 'Tên của gói dịch vụ (ví dụ: Gói chăm sóc da cao cấp)',
  })
  name: string;

  /**
   * ID sản phẩm dịch vụ liên kết
   */
  @Column({
    name: 'service_products_id',
    type: 'bigint',
    nullable: true,
    comment: 'ID sản phẩm dịch vụ liên kết (khóa ngoại đến bảng service_products)',
  })
  serviceProductsId: number | null;

  /**
   * Mô tả chi tiết về gói dịch vụ
   */
  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
    comment: 'Mô tả chi tiết về gói dịch vụ',
  })
  description: string | null;

  /**
   * Giá của gói dịch vụ
   */
  @Column({
    name: 'price',
    type: 'jsonb',
    nullable: false,
    comment: 'Giá của gói dịch vụ, lưu dưới dạng JSONB để hỗ trợ các định dạng giá khác nhau (VD: theo khu vực, đơn vị tiền tệ)',
  })
  price: ProductPrice;

  /**
   * Thời lượng của gói dịch vụ
   */
  @Column({
    name: 'duration',
    type: 'integer',
    nullable: true,
    comment: 'Thời lượng của gói dịch vụ (theo đơn vị trong trường unit)',
  })
  duration: number | null;

  /**
   * Đơn vị tính cho thời lượng
   */
  @Column({
    name: 'unit',
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Đơn vị tính cho thời lượng (ví dụ: phút, giờ, ngày)',
  })
  unit: string | null;

  /**
   * Danh sách tính năng hoặc lợi ích đi kèm gói dịch vụ
   */
  @Column({
    name: 'features',
    type: 'jsonb',
    nullable: true,
    comment: 'Danh sách tính năng hoặc lợi ích đi kèm gói dịch vụ (lưu dạng JSONB)',
  })
  features: any | null;

  /**
   * Trạng thái hoạt động của gói dịch vụ
   */
  @Column({
    name: 'is_active',
    type: 'boolean',
    nullable: true,
    default: true,
    comment: 'Trạng thái hoạt động của gói dịch vụ (TRUE = đang hoạt động)',
  })
  isActive: boolean | null;

  /**
   * Gói dịch vụ có bị giới hạn số lần bán không
   */
  @Column({
    name: 'is_limited',
    type: 'boolean',
    nullable: true,
    default: false,
    comment: 'Gói dịch vụ có bị giới hạn số lần bán không',
  })
  isLimited: boolean | null;

  /**
   * Số lượng tối đa được bán nếu gói bị giới hạn
   */
  @Column({
    name: 'max_sales',
    type: 'integer',
    nullable: true,
    comment: 'Số lượng tối đa được bán nếu gói bị giới hạn',
  })
  maxSales: number | null;

  /**
   * Thời gian tạo bản ghi
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian tạo bản ghi (epoch milliseconds)',
  })
  createdAt: number | null;

  /**
   * Thời gian cập nhật bản ghi
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian cập nhật bản ghi (epoch milliseconds)',
  })
  updatedAt: number | null;
}
