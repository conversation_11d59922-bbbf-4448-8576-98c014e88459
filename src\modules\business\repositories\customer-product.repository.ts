import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder, In } from 'typeorm';
import { CustomerProduct } from '../entities/customer-product.entity';
import { PaginatedResult } from '@common/response';
import { EntityStatusEnum } from '../enums';

/**
 * Repository cho CustomerProduct entity
 * Xử lý các thao tác database cho sản phẩm khách hàng
 */
@Injectable()
export class CustomerProductRepository {
  private readonly logger = new Logger(CustomerProductRepository.name);

  constructor(
    @InjectRepository(CustomerProduct)
    private readonly repository: Repository<CustomerProduct>,
  ) {}

  /**
   * Tạo sản phẩm khách hàng mới
   * @param data Dữ liệu sản phẩm
   * @returns Sản phẩm đã tạo
   */
  async create(data: Partial<CustomerProduct>): Promise<CustomerProduct> {
    const product = this.repository.create({
      ...data,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return this.repository.save(product);
  }

  /**
   * Tìm sản phẩm theo ID
   * @param id ID sản phẩm
   * @returns Sản phẩm hoặc null
   */
  async findById(id: number): Promise<CustomerProduct | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm sản phẩm theo ID và userId
   * @param id ID sản phẩm
   * @param userId ID người dùng
   * @returns Sản phẩm hoặc null
   */
  async findByIdAndUserId(id: number, userId: number): Promise<CustomerProduct | null> {
    return this.repository.findOne({
      where: { id, userId },
    });
  }

  /**
   * Lấy danh sách sản phẩm với phân trang và filter
   * @param query Tham số truy vấn
   * @returns Danh sách sản phẩm với phân trang
   */
  async findAll(query: {
    page?: number;
    limit?: number;
    search?: string;
    status?: EntityStatusEnum;
    productType?: string;
    userId?: number;
  }): Promise<PaginatedResult<CustomerProduct>> {
    const {
      page = 1,
      limit = 10,
      search,
      status,
      productType,
      userId,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('product');

    // Filter theo userId nếu có
    if (userId) {
      queryBuilder.andWhere('product.userId = :userId', { userId });
    }

    // Filter theo status nếu có
    if (status) {
      queryBuilder.andWhere('product.status = :status', { status });
    } else {
      // Mặc định không lấy sản phẩm đã xóa
      queryBuilder.andWhere('product.status != :deletedStatus', { 
        deletedStatus: EntityStatusEnum.DELETED 
      });
    }

    // Filter theo productType nếu có
    if (productType) {
      queryBuilder.andWhere('product.productType = :productType', { productType });
    }

    // Tìm kiếm theo tên nếu có
    if (search) {
      queryBuilder.andWhere('product.name ILIKE :search', { 
        search: `%${search}%` 
      });
    }

    // Sắp xếp theo thời gian tạo mới nhất
    queryBuilder.orderBy('product.createdAt', 'DESC');

    // Phân trang
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Thực hiện truy vấn
    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Cập nhật sản phẩm
   * @param id ID sản phẩm
   * @param data Dữ liệu cập nhật
   * @returns Sản phẩm đã cập nhật
   */
  async update(id: number, data: Partial<CustomerProduct>): Promise<CustomerProduct> {
    await this.repository.update(id, {
      ...data,
      updatedAt: Date.now(),
    });

    const updatedProduct = await this.findById(id);
    if (!updatedProduct) {
      throw new Error(`CustomerProduct with ID ${id} not found after update`);
    }

    return updatedProduct;
  }

  /**
   * Xóa mềm sản phẩm (cập nhật status thành DELETED)
   * @param id ID sản phẩm
   */
  async softDelete(id: number): Promise<void> {
    await this.repository.update(id, {
      status: EntityStatusEnum.DELETED,
      updatedAt: Date.now(),
    });
  }

  /**
   * Xóa cứng sản phẩm khỏi database
   * @param id ID sản phẩm
   */
  async hardDelete(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Đếm số lượng sản phẩm theo userId
   * @param userId ID người dùng
   * @returns Số lượng sản phẩm
   */
  async countByUserId(userId: number): Promise<number> {
    return this.repository.count({
      where: { 
        userId,
        status: EntityStatusEnum.APPROVED // Chỉ đếm sản phẩm đã duyệt
      },
    });
  }

  /**
   * Lưu sản phẩm
   * @param product Sản phẩm cần lưu
   * @returns Sản phẩm đã lưu
   */
  async save(product: CustomerProduct): Promise<CustomerProduct> {
    return this.repository.save(product);
  }

  /**
   * Tìm sản phẩm theo danh sách IDs
   * @param ids Danh sách ID
   * @returns Danh sách sản phẩm
   */
  async findByIds(ids: number[]): Promise<CustomerProduct[]> {
    if (!ids.length) return [];

    return this.repository.find({
      where: { id: In(ids) },
    });
  }

  /**
   * Lấy danh sách sản phẩm theo userId với phân trang
   * @param userId ID người dùng
   * @param query Tham số truy vấn
   * @returns Danh sách sản phẩm với phân trang
   */
  async findByUserIdWithPagination(userId: number, query: any): Promise<PaginatedResult<CustomerProduct>> {
    return this.findAll({ ...query, userId });
  }

  /**
   * Xóa mềm nhiều sản phẩm theo IDs và userId
   * @param ids Danh sách ID sản phẩm
   * @param userId ID người dùng
   * @returns Kết quả xóa
   */
  async softDeleteByIdsAndUserId(ids: number[], userId: number): Promise<{
    deletedCount: number;
    failedIds: number[];
    failedReasons?: string[];
  }> {
    try {
      // Tìm các sản phẩm thuộc về user
      const products = await this.repository.find({
        where: {
          id: In(ids),
          userId,
          status: EntityStatusEnum.APPROVED // Chỉ xóa sản phẩm đã duyệt
        },
      });

      const foundIds = products.map(p => p.id);
      const failedIds = ids.filter(id => !foundIds.includes(id));

      // Xóa mềm các sản phẩm tìm được
      if (foundIds.length > 0) {
        await this.repository.update(
          { id: In(foundIds) },
          {
            status: EntityStatusEnum.DELETED,
            updatedAt: Date.now(),
          }
        );
      }

      return {
        deletedCount: foundIds.length,
        failedIds,
        failedReasons: failedIds.map(() => 'Không tìm thấy sản phẩm hoặc không có quyền xóa'),
      };

    } catch (error) {
      this.logger.error(`Lỗi khi xóa batch sản phẩm: ${error.message}`);
      return {
        deletedCount: 0,
        failedIds: ids,
        failedReasons: ids.map(() => 'Lỗi hệ thống'),
      };
    }
  }
}
