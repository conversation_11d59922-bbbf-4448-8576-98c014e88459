import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response của media tracking
 */
export class MediaTrackingResponseDto {
  @ApiProperty({
    description: 'ID của media đang được theo dõi',
    example: 'media-wtpRtUVPyEIouFZ0',
  })
  media_id: string;

  @ApiProperty({
    description: 'Tiến độ xử lý (0-100)',
    example: 75,
    minimum: 0,
    maximum: 100,
  })
  progress: number;

  @ApiProperty({
    description: 'Trạng thái xử lý',
    enum: ['pending', 'processing', 'completed', 'error', 'timeout'],
    example: 'processing',
  })
  status: 'pending' | 'processing' | 'completed' | 'error' | 'timeout';

  @ApiProperty({
    description: 'Thông điệp mô tả trạng thái hiện tại',
    example: 'Đang tạo embeddings cho ảnh',
  })
  message: string;

  @ApiProperty({
    description: 'Timestamp của event (Unix timestamp)',
    example: 1640995200000,
  })
  timestamp: number;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> bổ sung (tùy chọn)',
    required: false,
    example: {
      chunks_count: 10,
      processing_step: 'embedding_creation',
    },
  })
  metadata?: Record<string, any>;
}
