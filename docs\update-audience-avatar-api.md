# API Cập Nhật Audience với Avatar Upload

## Tổng quan

API `PUT /v1/admin/marketing/audiences/:id` đã được cập nhật để hỗ trợ tạo URL tạm thời cho việc upload avatar. Khi client gửi trường `avatarMediaType` trong request, API sẽ trả về thông tin để upload avatar lên S3.

## Endpoint

```
PUT /v1/admin/marketing/audiences/:id
```

## Request Body

### Các trường cơ bản (tù<PERSON> chọn)
- `name`: string - Tên khách hàng
- `email`: string - Email khách hàng  
- `phone`: string - Số điện thoại khách hàng
- `customFields`: array - Các trường tùy chỉnh
- `tagIds`: array - Danh sách ID của các tag

### Trường mới cho avatar
- `avatarMediaType`: enum - Loại file avatar (chỉ cho phép image)
  - `image/jpeg`
  - `image/png` 
  - `image/webp`
  - `image/gif`

## Response

### Khi không có `avatarMediaType`
Trả về `AudienceResponseDto` như bình thường.

### Khi có `avatarMediaType`
Trả về `UpdateAudienceResponseDto` bao gồm:

```json
{
  "success": true,
  "message": "Audience đã được cập nhật thành công",
  "data": {
    // Tất cả các trường của AudienceResponseDto
    "id": 1,
    "name": "Nguyễn Văn A",
    "email": "<EMAIL>",
    "phone": "+84912345678",
    "avatar": "existing-avatar-url",
    "customFields": [],
    "tags": [],
    "createdAt": 1619171200,
    "updatedAt": 1619174800,
    
    // Các trường mới cho avatar upload
    "avatarUploadUrl": "https://s3.amazonaws.com/bucket/path/to/upload?signature=...",
    "avatarS3Key": "marketing/customer_avatars/2024/01/admin/1234567890-uuid.jpg",
    "avatarUploadExpiresAt": 1619178400
  }
}
```

## Quy trình Upload Avatar

1. **Gửi request cập nhật audience với `avatarMediaType`**
   ```json
   {
     "name": "Nguyễn Văn A",
     "avatarMediaType": "image/jpeg"
   }
   ```

2. **Nhận response với thông tin upload**
   - `avatarUploadUrl`: URL để upload file
   - `avatarS3Key`: Key của file trên S3 (đã được lưu vào database)
   - `avatarUploadExpiresAt`: Thời gian hết hạn (Unix timestamp)

3. **Upload file lên S3 sử dụng `avatarUploadUrl`**
   ```javascript
   const formData = new FormData();
   formData.append('file', fileBlob);

   await fetch(avatarUploadUrl, {
     method: 'PUT',
     body: formData
   });
   ```

**Lưu ý quan trọng**: S3 key đã được tự động lưu vào database khi tạo URL upload. Sau khi upload file thành công, avatar sẽ có sẵn trong hệ thống mà không cần thêm bước cập nhật.

## Lưu ý

- URL upload có thời gian hết hạn 1 giờ
- Kích thước file tối đa: 5MB
- Chỉ chấp nhận các định dạng image: JPEG, PNG, WEBP, GIF
- **S3 key được tự động lưu vào database** khi tạo URL upload
- Sau khi upload file thành công, avatar sẽ có sẵn trong hệ thống
- Nếu có lỗi khi tạo URL upload, API vẫn cập nhật audience thành công nhưng không trả về thông tin upload
- Không cần gọi thêm API để cập nhật avatar sau khi upload

## Ví dụ sử dụng

### Cập nhật audience với avatar
```bash
curl -X PUT "http://localhost:3000/v1/admin/marketing/audiences/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Nguyễn Văn A",
    "email": "<EMAIL>",
    "avatarMediaType": "image/jpeg"
  }'
```

### Cập nhật audience không có avatar
```bash
curl -X PUT "http://localhost:3000/v1/admin/marketing/audiences/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Nguyễn Văn A",
    "email": "<EMAIL>"
  }'
```
