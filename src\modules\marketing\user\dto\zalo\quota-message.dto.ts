import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsNotEmpty } from 'class-validator';

/**
 * Enum cho loại sản phẩm quota message
 */
export enum QuotaProductType {
  CS = 'cs',
  TRANSACTION = 'transaction',
}

/**
 * Enum cho loại quota
 */
export enum QuotaType {
  SUB_QUOTA = 'sub_quota',
  PURCHASE_QUOTA = 'purchase_quota',
  REWARD_QUOTA = 'reward_quota',
}

/**
 * DTO cho yêu cầu lấy quota message
 */
export class QuotaMessageRequestDto {
  @ApiProperty({
    description: 'Thực thể sở hữu Quota muốn truy xuất OA / APP',
    example: 'OA',
  })
  @IsString()
  @IsNotEmpty()
  quotaOwner: string;

  @ApiProperty({
    description: 'Loại sản phẩm thuộc nhánh tính năng muốn truy xuất',
    enum: QuotaProductType,
    example: QuotaProductType.CS,
    required: false,
  })
  @IsOptional()
  @IsEnum(QuotaProductType)
  productType?: QuotaProductType;

  @ApiProperty({
    description: 'Loại quota (nguồn quota sử dụng) muốn truy xuất',
    enum: QuotaType,
    example: QuotaType.SUB_QUOTA,
    required: false,
  })
  @IsOptional()
  @IsEnum(QuotaType)
  quotaType?: QuotaType;
}

/**
 * DTO cho thông tin asset quota message
 */
export class QuotaMessageAssetDto {
  @ApiProperty({
    description: 'ID của asset',
    example: '216991e1ea59bc03e54c',
  })
  assetId: string;

  @ApiProperty({
    description: 'Loại sản phẩm thuộc nhánh tính năng',
    example: 'cs',
  })
  productType: string;

  @ApiProperty({
    description: 'Loại quota (nguồn quota sử dụng)',
    example: 'sub_quota',
  })
  quotaType: string;

  @ApiProperty({
    description: 'Ngày hết hạn của asset_id',
    example: '10/10/2024',
  })
  validThrough: string;

  @ApiProperty({
    description: 'Tổng số lượng quota CHƯA hết hạn (bao gồm available + used) của asset_id',
    example: 2000,
  })
  total: number;

  @ApiProperty({
    description: 'Số lượng quota ở trạng thái available của asset_id',
    example: 1990,
  })
  remain: number;
}

/**
 * DTO cho phản hồi quota message
 */
export class QuotaMessageResponseDto {
  @ApiProperty({
    description: 'Danh sách thông tin quota message',
    type: [QuotaMessageAssetDto],
  })
  data: QuotaMessageAssetDto[];

  @ApiProperty({
    description: 'Mã lỗi (0 = thành công)',
    example: 0,
  })
  error: number;

  @ApiProperty({
    description: 'Thông báo kết quả',
    example: 'success',
  })
  message: string;
}
