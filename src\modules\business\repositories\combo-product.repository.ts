import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ComboProduct } from '../entities/combo-product.entity';

/**
 * Repository cho ComboProduct entity
 * Xử lý các thao tác database cho sản phẩm combo
 */
@Injectable()
export class ComboProductRepository {
  private readonly logger = new Logger(ComboProductRepository.name);

  constructor(
    @InjectRepository(ComboProduct)
    private readonly repository: Repository<ComboProduct>,
  ) {}

  /**
   * Tạo sản phẩm combo mới
   * @param data Dữ liệu sản phẩm combo
   * @returns Sản phẩm combo đã tạo
   */
  async create(data: Partial<ComboProduct>): Promise<ComboProduct> {
    const comboProduct = this.repository.create(data);
    return this.repository.save(comboProduct);
  }

  /**
   * Tìm sản phẩm combo theo ID
   * @param id ID sản phẩm combo (cũng là customer product ID)
   * @returns Sản phẩm combo hoặc null
   */
  async findById(id: number): Promise<ComboProduct | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Cập nhật sản phẩm combo
   * @param id ID sản phẩm combo
   * @param data Dữ liệu cập nhật
   * @returns Sản phẩm combo đã cập nhật
   */
  async update(id: number, data: Partial<ComboProduct>): Promise<ComboProduct | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa sản phẩm combo
   * @param id ID sản phẩm combo
   */
  async delete(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Lưu sản phẩm combo
   * @param comboProduct Sản phẩm combo cần lưu
   * @returns Sản phẩm combo đã lưu
   */
  async save(comboProduct: ComboProduct): Promise<ComboProduct> {
    return this.repository.save(comboProduct);
  }

  /**
   * Tìm tất cả sản phẩm combo
   * @returns Danh sách sản phẩm combo
   */
  async findAll(): Promise<ComboProduct[]> {
    return this.repository.find();
  }

  /**
   * Đếm số lượng sản phẩm combo
   * @returns Số lượng sản phẩm combo
   */
  async count(): Promise<number> {
    return this.repository.count();
  }

  /**
   * Tìm combo products theo danh sách IDs
   * @param ids Danh sách ID sản phẩm combo
   * @returns Danh sách sản phẩm combo
   */
  async findByIds(ids: number[]): Promise<ComboProduct[]> {
    if (ids.length === 0) {
      return [];
    }
    
    return this.repository.findByIds(ids);
  }

  /**
   * Kiểm tra combo product có tồn tại không
   * @param id ID sản phẩm combo
   * @returns true nếu tồn tại
   */
  async exists(id: number): Promise<boolean> {
    const count = await this.repository.count({
      where: { id },
    });
    return count > 0;
  }

  /**
   * Cập nhật số lượt mua của combo
   * @param id ID sản phẩm combo
   * @param purchaseCount Số lượt mua mới
   * @returns Sản phẩm combo đã cập nhật
   */
  async updatePurchaseCount(id: number, purchaseCount: number): Promise<ComboProduct | null> {
    await this.repository.update(id, { 
      price: () => 'price', // Giữ nguyên giá hiện tại
      comboItems: () => 'combo_items', // Giữ nguyên combo items hiện tại
      maxQuantity: () => 'max_quantity', // Giữ nguyên max quantity hiện tại
    });
    
    // Cập nhật riêng purchase count thông qua raw query để tránh conflict
    await this.repository.query(
      'UPDATE combo_products SET purchase_count = $1 WHERE id = $2',
      [purchaseCount, id]
    );
    
    return this.findById(id);
  }

  /**
   * Tăng số lượt mua của combo
   * @param id ID sản phẩm combo
   * @param increment Số lượng tăng thêm (mặc định 1)
   * @returns Sản phẩm combo đã cập nhật
   */
  async incrementPurchaseCount(id: number, increment: number = 1): Promise<ComboProduct | null> {
    await this.repository.query(
      'UPDATE combo_products SET purchase_count = COALESCE(purchase_count, 0) + $1 WHERE id = $2',
      [increment, id]
    );
    
    return this.findById(id);
  }

  /**
   * Kiểm tra combo có thể mua được không (dựa trên maxQuantity)
   * @param id ID sản phẩm combo
   * @param requestedQuantity Số lượng muốn mua
   * @returns true nếu có thể mua
   */
  async canPurchase(id: number, requestedQuantity: number = 1): Promise<boolean> {
    const combo = await this.findById(id);
    if (!combo) {
      return false;
    }

    // Nếu không có giới hạn maxQuantity thì luôn có thể mua
    if (combo.maxQuantity === null || combo.maxQuantity === undefined) {
      return true;
    }

    // Kiểm tra số lượng hiện tại + số lượng muốn mua có vượt quá giới hạn không
    const currentPurchaseCount = combo.price?.purchaseCount || 0;
    return (currentPurchaseCount + requestedQuantity) <= combo.maxQuantity;
  }

  /**
   * Lấy thống kê combo product
   * @param id ID sản phẩm combo
   * @returns Thống kê combo
   */
  async getComboStats(id: number): Promise<{
    maxQuantity: number | null;
    currentPurchaseCount: number;
    remainingQuantity: number | null;
    isAvailable: boolean;
  } | null> {
    const combo = await this.findById(id);
    if (!combo) {
      return null;
    }

    const currentPurchaseCount = combo.price?.purchaseCount || 0;
    const maxQuantity = combo.maxQuantity;
    const remainingQuantity = maxQuantity !== null ? maxQuantity - currentPurchaseCount : null;
    const isAvailable = maxQuantity === null || currentPurchaseCount < maxQuantity;

    return {
      maxQuantity,
      currentPurchaseCount,
      remainingQuantity,
      isAvailable,
    };
  }
}
