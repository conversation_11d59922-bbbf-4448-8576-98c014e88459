import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateZaloZnsCampaignTable1737000000001 implements MigrationInterface {
  name = 'CreateZaloZnsCampaignTable1737000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Tạo bảng zalo_zns_campaigns
    await queryRunner.createTable(
      new Table({
        name: 'zalo_zns_campaigns',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'user_id',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'oa_id',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'template_id',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'template_data',
            type: 'jsonb',
            isNullable: false,
          },
          {
            name: 'phone_list',
            type: 'jsonb',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['draft', 'scheduled', 'running', 'completed', 'failed', 'cancelled'],
            default: "'draft'",
            isNullable: false,
          },
          {
            name: 'scheduled_at',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'started_at',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'completed_at',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'total_messages',
            type: 'int',
            default: 0,
            isNullable: false,
          },
          {
            name: 'sent_messages',
            type: 'int',
            default: 0,
            isNullable: false,
          },
          {
            name: 'failed_messages',
            type: 'int',
            default: 0,
            isNullable: false,
          },
          {
            name: 'job_ids',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'error_message',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'bigint',
            isNullable: false,
          },
          {
            name: 'updated_at',
            type: 'bigint',
            isNullable: false,
          },
        ],
      }),
      true,
    );

    // Tạo các index
    await queryRunner.createIndex(
      'zalo_zns_campaigns',
      new TableIndex({
        name: 'IDX_zalo_zns_campaigns_user_id',
        columnNames: ['user_id'],
      }),
    );

    await queryRunner.createIndex(
      'zalo_zns_campaigns',
      new TableIndex({
        name: 'IDX_zalo_zns_campaigns_oa_id',
        columnNames: ['oa_id'],
      }),
    );

    await queryRunner.createIndex(
      'zalo_zns_campaigns',
      new TableIndex({
        name: 'IDX_zalo_zns_campaigns_status',
        columnNames: ['status'],
      }),
    );

    await queryRunner.createIndex(
      'zalo_zns_campaigns',
      new TableIndex({
        name: 'IDX_zalo_zns_campaigns_scheduled_at',
        columnNames: ['scheduled_at'],
      }),
    );

    await queryRunner.createIndex(
      'zalo_zns_campaigns',
      new TableIndex({
        name: 'IDX_zalo_zns_campaigns_created_at',
        columnNames: ['created_at'],
      }),
    );

    await queryRunner.createIndex(
      'zalo_zns_campaigns',
      new TableIndex({
        name: 'IDX_zalo_zns_campaigns_user_oa',
        columnNames: ['user_id', 'oa_id'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa các index
    await queryRunner.dropIndex('zalo_zns_campaigns', 'IDX_zalo_zns_campaigns_user_oa');
    await queryRunner.dropIndex('zalo_zns_campaigns', 'IDX_zalo_zns_campaigns_created_at');
    await queryRunner.dropIndex('zalo_zns_campaigns', 'IDX_zalo_zns_campaigns_scheduled_at');
    await queryRunner.dropIndex('zalo_zns_campaigns', 'IDX_zalo_zns_campaigns_status');
    await queryRunner.dropIndex('zalo_zns_campaigns', 'IDX_zalo_zns_campaigns_oa_id');
    await queryRunner.dropIndex('zalo_zns_campaigns', 'IDX_zalo_zns_campaigns_user_id');

    // Xóa bảng
    await queryRunner.dropTable('zalo_zns_campaigns');
  }
}
