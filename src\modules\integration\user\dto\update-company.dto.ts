import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsIn, MaxLength, MinLength } from 'class-validator';

/**
 * DTO cho việc cập nhật thông tin công ty
 */
export class UpdateCompanyDto {
  @ApiProperty({
    description: 'Tên đầy đủ của công ty',
    example: 'Công ty TNHH Thương mại Dịch vụ ABC Updated',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Tên đầy đủ công ty phải là chuỗi' })
  @MinLength(3, { message: 'Tên đầy đủ công ty phải có ít nhất 3 ký tự' })
  @MaxLength(255, { message: 'Tên đầy đủ công ty không được vượt quá 255 ký tự' })
  fullName?: string;

  @ApiProperty({
    description: 'Tên viết tắt của công ty',
    example: 'ABC-NEW',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Tên viết tắt công ty phải là chuỗi' })
  @MinLength(2, { message: 'Tên viết tắt công ty phải có ít nhất 2 ký tự' })
  @MaxLength(50, { message: 'Tên viết tắt công ty không được vượt quá 50 ký tự' })
  shortName?: string;

  @ApiProperty({
    description: 'Trạng thái công ty',
    example: 'Active',
    enum: ['Pending', 'Active', 'Suspended', 'Terminated', 'Cancelled', 'Fraud'],
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Trạng thái công ty phải là chuỗi' })
  @IsIn(['Pending', 'Active', 'Suspended', 'Terminated', 'Cancelled', 'Fraud'], {
    message: 'Trạng thái công ty phải là một trong: Pending, Active, Suspended, Terminated, Cancelled, Fraud'
  })
  status?: string;
}
