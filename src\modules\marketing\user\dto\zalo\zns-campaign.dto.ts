import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString, Min } from 'class-validator';
import { ZaloZnsCampaignStatus } from '../../entities/zalo-zns-campaign.entity';

/**
 * DTO cho tạo chiến dịch ZNS
 */
export class CreateZnsCampaignDto {
  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Chiến dịch thông báo đơn hàng',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON> tả chiến dịch',
    example: '<PERSON><PERSON>i thông báo đơn hàng cho khách hàng',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'ID template ZNS',
    example: 'template123456789',
  })
  @IsString()
  @IsNotEmpty()
  templateId: string;

  @ApiProperty({
    description: 'Dữ liệu cho template',
    example: {
      shopName: 'RedAI Shop',
      orderStatus: 'Đã xác nhận',
    },
  })
  @IsObject()
  @IsNotEmpty()
  templateData: Record<string, any>;

  @ApiProperty({
    description: 'Danh sách số điện thoại',
    example: ['**********', '**********'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  phoneList: string[];

  @ApiPropertyOptional({
    description: 'Thời gian lên lịch gửi (Unix timestamp)',
    example: 1640995200000,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  scheduledAt?: number;
}

/**
 * DTO cho cập nhật chiến dịch ZNS
 */
export class UpdateZnsCampaignDto {
  @ApiPropertyOptional({
    description: 'Tên chiến dịch',
    example: 'Chiến dịch thông báo đơn hàng (cập nhật)',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiPropertyOptional({
    description: 'Mô tả chiến dịch',
    example: 'Gửi thông báo đơn hàng cho khách hàng VIP',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'ID template ZNS',
    example: 'template987654321',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  templateId?: string;

  @ApiPropertyOptional({
    description: 'Dữ liệu cho template',
    example: {
      shopName: 'RedAI Shop Pro',
      orderStatus: 'Đang xử lý',
    },
  })
  @IsOptional()
  @IsObject()
  templateData?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Danh sách số điện thoại',
    example: ['**********', '**********', '0123456789'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  phoneList?: string[];

  @ApiPropertyOptional({
    description: 'Thời gian lên lịch gửi (Unix timestamp)',
    example: 1640995200000,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  scheduledAt?: number;
}

/**
 * DTO cho query danh sách chiến dịch ZNS
 */
export class ZnsCampaignQueryDto {
  @ApiPropertyOptional({
    description: 'Số trang',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Số lượng item trên mỗi trang',
    example: 10,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Từ khóa tìm kiếm',
    example: 'thông báo',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Trạng thái chiến dịch',
    enum: ZaloZnsCampaignStatus,
    example: ZaloZnsCampaignStatus.COMPLETED,
  })
  @IsOptional()
  @IsEnum(ZaloZnsCampaignStatus)
  status?: ZaloZnsCampaignStatus;
}

/**
 * DTO cho response chiến dịch ZNS
 */
export class ZnsCampaignResponseDto {
  @ApiProperty({
    description: 'ID chiến dịch',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID người dùng',
    example: 123,
  })
  userId: number;

  @ApiProperty({
    description: 'ID Official Account',
    example: 'oa123456789',
  })
  oaId: string;

  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Chiến dịch thông báo đơn hàng',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Mô tả chiến dịch',
    example: 'Gửi thông báo đơn hàng cho khách hàng',
  })
  description?: string;

  @ApiProperty({
    description: 'ID template ZNS',
    example: 'template123456789',
  })
  templateId: string;

  @ApiProperty({
    description: 'Dữ liệu cho template',
    example: {
      shopName: 'RedAI Shop',
      orderStatus: 'Đã xác nhận',
    },
  })
  templateData: Record<string, any>;

  @ApiProperty({
    description: 'Danh sách số điện thoại',
    example: ['**********', '**********'],
  })
  phoneList: string[];

  @ApiProperty({
    description: 'Trạng thái chiến dịch',
    enum: ZaloZnsCampaignStatus,
    example: ZaloZnsCampaignStatus.COMPLETED,
  })
  status: ZaloZnsCampaignStatus;

  @ApiPropertyOptional({
    description: 'Thời gian lên lịch gửi (Unix timestamp)',
    example: 1640995200000,
  })
  scheduledAt?: number;

  @ApiPropertyOptional({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    example: 1640995200000,
  })
  startedAt?: number;

  @ApiPropertyOptional({
    description: 'Thời gian hoàn thành (Unix timestamp)',
    example: 1640995800000,
  })
  completedAt?: number;

  @ApiProperty({
    description: 'Tổng số tin nhắn',
    example: 100,
  })
  totalMessages: number;

  @ApiProperty({
    description: 'Số tin nhắn đã gửi',
    example: 95,
  })
  sentMessages: number;

  @ApiProperty({
    description: 'Số tin nhắn thất bại',
    example: 5,
  })
  failedMessages: number;

  @ApiPropertyOptional({
    description: 'Thông báo lỗi (nếu có)',
    example: 'Template không hợp lệ',
  })
  errorMessage?: string;

  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1640995000000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1640995800000,
  })
  updatedAt: number;
}
