import { Injectable, Logger } from '@nestjs/common';
import { UserWebsiteRepository } from '../../repositories';
import { CreateWebsiteDto, WebsiteQueryDto, WebsiteResponseDto, DeleteWebsitesDto, WebsiteScriptResponseDto, CreateWebsiteResponseDto } from '../dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException, ErrorCode } from '@/common';
import { INTEGRATION_ERROR_CODES } from '../../exceptions/integration-error.code';
import { S3Service } from '@/shared/services/s3.service';
import { CdnService } from '@/shared/services/cdn.service';
import { generateS3Key, CategoryFolderEnum } from '@/shared/utils/generators/s3-key-generator.util';
import { TimeIntervalEnum, ImageTypeEnum, ImageType, FileSizeEnum } from '@/shared/utils';
import axios from 'axios';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class UserWebsiteUserService {
  private readonly logger = new Logger(UserWebsiteUserService.name);

  constructor(
    private readonly userWebsiteRepository: UserWebsiteRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Lấy danh sách website của người dùng
   * @param queryDto Tham số truy vấn
   * @param userId ID của người dùng
   * @returns Danh sách website với phân trang
   */
  async findAll(
    queryDto: WebsiteQueryDto,
    userId: number,
  ): Promise<PaginatedResult<WebsiteResponseDto>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        isConnectAgent,
      } = queryDto;

      // Sử dụng repository method mới để lấy dữ liệu với join agent
      const result = await this.userWebsiteRepository.findWithPagination(
        userId,
        page,
        limit,
        search,
        isConnectAgent,
      );

      // Chuyển đổi và format dữ liệu thủ công
      const websiteResponseDtos = result.items.map(item => {
        // Manual mapping để đảm bảo tất cả trường được bao gồm
        const dto: WebsiteResponseDto = {
          id: item.id,
          host: item.host,
          verify: item.verify,
          agentId: item.agentId || null,
          agentName: item.agentName || null,
          logo: item.logo ? this.cdnService.generateUrlView(item.logo, TimeIntervalEnum.THIRTY_DAYS) : null,
          createdAt: item.createdAt,
        };

        return dto;
      });

      return {
        items: websiteResponseDtos,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting websites for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo mới website
   * @param createWebsiteDto Thông tin website cần tạo
   * @param userId ID của người dùng
   * @returns Thông tin website đã tạo với presigned URL cho logo (nếu có)
   */
  async create(
    createWebsiteDto: CreateWebsiteDto,
    userId: number,
  ): Promise<CreateWebsiteResponseDto> {
    try {
      // Kiểm tra xem host đã tồn tại chưa
      const existingWebsite = await this.userWebsiteRepository.findOne({
        where: { host: createWebsiteDto.host, userId },
      });

      if (existingWebsite) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Website với host ${createWebsiteDto.host} đã tồn tại`,
        );
      }

      // Kiểm tra xem website có tồn tại không
      await this.validateWebsite(createWebsiteDto.host);

      // Tạo mới website
      const newWebsite = this.userWebsiteRepository.create({
        websiteName: createWebsiteDto.websiteName,
        host: createWebsiteDto.host,
        userId,
        verify: false,
        createdAt: Date.now(),
      });

       // Tạo presigned URL cho logo nếu có logoMime
      let logoUploadUrl: string | undefined;

      if (createWebsiteDto.logoMime) {
        // Tạo S3 key cho logo
        const logoS3Key = generateS3Key({
          baseFolder: userId.toString(),
          categoryFolder: CategoryFolderEnum.IMAGE,
          fileName: `logo-${Date.now()}.${this.getFileExtensionFromMime(createWebsiteDto.logoMime)}`,
          useTimeFolder: true,
        });

        // Tạo presigned URL
        const expirationTime = TimeIntervalEnum.FIFTEEN_MINUTES;
        logoUploadUrl = await this.s3Service.createPresignedWithID(
          logoS3Key,
          expirationTime,
          ImageType.getType(createWebsiteDto.logoMime),
          FileSizeEnum.FIVE_MB, // 5MB max size
        );

        // Lưu S3 key vào website
        newWebsite.logo = logoS3Key;
      }

      // Lưu vào database
      await this.userWebsiteRepository.save(newWebsite);

      // Tạo response DTO - chỉ trả về logoUploadUrl
      const response: CreateWebsiteResponseDto = {
        logoUploadUrl,
      };

      return response;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error creating website for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Không thể tạo website: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra xem website có tồn tại không
   * @param host Host của website cần kiểm tra
   * @returns true nếu website tồn tại, false nếu không tồn tại
   */
  private async validateWebsite(host: string): Promise<boolean> {
    try {
      // Chuẩn hóa host
      let normalizedHost = host;
      if (!normalizedHost.startsWith('http://') && !normalizedHost.startsWith('https://')) {
        normalizedHost = `https://${normalizedHost}`;
      }

      // Kiểm tra xem website có tồn tại không
      const response = await axios.head(normalizedHost, {
        timeout: 5000, // Timeout 5 giây
        validateStatus: (status) => status < 500, // Chấp nhận status code < 500
      });

      // Nếu status code là 2xx hoặc 3xx, website tồn tại
      if (response.status >= 200 && response.status < 400) {
        return true;
      }

      // Nếu status code là 4xx, website không tồn tại hoặc không thể truy cập
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        `Website không tồn tại hoặc không thể truy cập (status code: ${response.status})`,
      );
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý lỗi từ axios
      if (axios.isAxiosError(error)) {
        if (error.code === 'ENOTFOUND') {
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            'Không thể kết nối đến website. Vui lòng kiểm tra lại tên miền.',
          );
        }
        if (error.code === 'ECONNREFUSED') {
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            'Kết nối bị từ chối. Website có thể không hoạt động.',
          );
        }
        if (error.code === 'ETIMEDOUT') {
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            'Kết nối bị timeout. Website phản hồi quá chậm hoặc không phản hồi.',
          );
        }
      }

      // Lỗi khác
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        `Không thể xác minh website: ${error.message}`,
      );
    }
  }

  /**
   * Xóa một website
   * @param websiteId ID của website cần xóa
   * @param userId ID của người dùng
   */
  @Transactional()
  async deleteWebsite(websiteId: string, userId: number): Promise<void> {
    try {
      // Tìm website thuộc về người dùng
      const website = await this.userWebsiteRepository.findByIdAndUserIdForDelete(websiteId, userId);

      if (!website) {
        throw new AppException(INTEGRATION_ERROR_CODES.WEBSITE_NOT_FOUND);
      }

      // Xóa website
      await this.userWebsiteRepository.remove(website);

      this.logger.log(`Đã xóa website ${website.websiteName} (ID: ${websiteId}) của user ${userId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi xóa website: ${error.message}`, error.stack);
      throw new AppException(INTEGRATION_ERROR_CODES.WEBSITE_DELETE_FAILED);
    }
  }

  /**
   * Xóa nhiều website cùng lúc
   * @param deleteDto DTO chứa danh sách ID website cần xóa
   * @param userId ID của người dùng
   * @returns Thông tin về số lượng website đã xóa và danh sách website lỗi
   */
  @Transactional()
  async deleteMultipleWebsites(
    deleteDto: DeleteWebsitesDto,
    userId: number,
  ): Promise<{ deletedCount: number; errorWebsites?: string[] }> {
    try {
      const { websiteIds } = deleteDto;

      if (!websiteIds || websiteIds.length === 0) {
        return { deletedCount: 0 };
      }

      // Tìm tất cả website thuộc về người dùng
      const websites = await this.userWebsiteRepository.findByIdsAndUserId(websiteIds, userId);

      if (websites.length === 0) {
        return { deletedCount: 0 };
      }

      this.logger.log(`Tìm thấy ${websites.length} website cần xóa cho user ${userId}`);

      const errorWebsites: string[] = [];
      let deletedCount = 0;

      // Xóa từng website
      for (const website of websites) {
        try {
          await this.userWebsiteRepository.remove(website);
          deletedCount++;
          this.logger.log(`Đã xóa website ${website.websiteName} (ID: ${website.id})`);
        } catch (error) {
          this.logger.error(`Lỗi khi xóa website ${website.websiteName}: ${error.message}`, error.stack);
          errorWebsites.push(website.websiteName);
        }
      }

      return {
        deletedCount,
        errorWebsites: errorWebsites.length > 0 ? errorWebsites : undefined,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi xóa nhiều website: ${error.message}`, error.stack);
      throw new AppException(INTEGRATION_ERROR_CODES.WEBSITE_DELETE_FAILED);
    }
  }

  /**
   * Lấy script widget tích hợp cho website
   * @param userId ID của người dùng
   * @returns Script HTML
   */
  async getWebsiteScript(userId: number): Promise<WebsiteScriptResponseDto> {
    try {
      // Tạo script HTML với format ref="redai_{userId}"
      const script = `<script src="https://cdn.redai.com/widget.js" ref="redai_${userId}"></script>`;

      const response: WebsiteScriptResponseDto = {
        script,
      };

      this.logger.log(`Đã tạo widget script cho user ${userId}`);

      return response;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi tạo script tích hợp: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi tạo script tích hợp website'
      );
    }
  }

  /**
   * Cập nhật logo S3 key cho website sau khi upload thành công
   * @param websiteId ID của website
   * @param userId ID của người dùng
   * @param logoS3Key S3 key của logo đã upload
   */
  @Transactional()
  async updateWebsiteLogo(
    websiteId: string,
    userId: number,
    logoS3Key: string,
  ): Promise<void> {
    try {
      // Tìm website thuộc về người dùng
      const website = await this.userWebsiteRepository.findByIdAndUserId(websiteId, userId);

      if (!website) {
        throw new AppException(INTEGRATION_ERROR_CODES.WEBSITE_NOT_FOUND);
      }

      // Cập nhật logo S3 key
      await this.userWebsiteRepository.update(
        { id: websiteId, userId },
        { logo: logoS3Key }
      );

      this.logger.log(`Đã cập nhật logo cho website ${websiteId} với S3 key: ${logoS3Key}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi cập nhật logo website: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi cập nhật logo website'
      );
    }
  }

  /**
   * Lấy file extension từ MIME type
   * @param mimeType MIME type của file
   * @returns File extension
   */
  private getFileExtensionFromMime(mimeType: string): string {
    const mimeToExtension: Record<string, string> = {
      'image/png': 'png',
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/gif': 'gif',
      'image/webp': 'webp',
    };

    return mimeToExtension[mimeType] || 'png';
  }

}
