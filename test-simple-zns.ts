/**
 * <PERSON>ript test các template ZNS đơn giản
 * Chạy: npx ts-node test-simple-zns.ts
 */

import { ZNS_TEMPLATE_EXAMPLES, validateZnsTemplate } from './src/modules/marketing/user/examples/zns-template-examples';

console.log('🧪 Testing ZNS Template Examples\n');

// Test validation cho từng template
Object.entries(ZNS_TEMPLATE_EXAMPLES).forEach(([name, template]) => {
  console.log(`📋 Testing template: ${name}`);
  console.log(`   Template name: ${template.template_name}`);
  console.log(`   Template type: ${template.template_type}`);
  console.log(`   Tag: ${template.tag}`);
  
  const validation = validateZnsTemplate(template);
  
  if (validation.isValid) {
    console.log(`   ✅ Valid template`);
  } else {
    console.log(`   ❌ Invalid template:`);
    validation.errors.forEach(error => {
      console.log(`      - ${error}`);
    });
  }
  
  console.log(`   📄 Request body size: ${JSON.stringify(template).length} bytes`);
  console.log('');
});

// Hiển thị template đơn giản nhất để test
console.log('🎯 Template đơn giản nhất để test:');
console.log('```json');
console.log(JSON.stringify(ZNS_TEMPLATE_EXAMPLES.NO_PARAMS, null, 2));
console.log('```\n');

// Hiển thị template có params để test
console.log('🔐 Template đơn giản có params để test:');
console.log('```json');
console.log(JSON.stringify(ZNS_TEMPLATE_EXAMPLES.SIMPLE_PARAGRAPH, null, 2));
console.log('```\n');

console.log('📝 Lưu ý:');
console.log('- Tag phải là string ("1", "2", "3") không phải number');
console.log('- template_name phải có 10-60 ký tự');
console.log('- layout.body.components là bắt buộc');
console.log('- tracking_id là bắt buộc');
console.log('- params chỉ cần khi có placeholder trong layout');
