import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
  ProductTypeEnum,
  PriceTypeEnum,
  EntityStatusEnum,
  ParticipationTypeEnum,
} from '@modules/business/enums';
import { EventProductTicketResponseDto } from './event-product-ticket-response.dto';

/**
 * Response DTO hoàn chỉnh cho event product
 * <PERSON>o gồm tất cả thông tin từ customer_products + event_products + tickets + images
 */
export class CompleteEventProductResponseDto {
  // ========== THÔNG TIN TỪ CUSTOMER_PRODUCTS ==========
  
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 123,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Hội thảo Marketing Digital 2024',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: '<PERSON><PERSON> tả sản phẩm',
    example: '<PERSON>ội thảo về xu hướng marketing digital mới nhất năm 2024',
    required: false,
  })
  @Expose()
  description?: string;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.EVENT,
  })
  @Expose()
  productType: ProductTypeEnum;

  @ApiProperty({
    description: 'Giá sản phẩm',
    example: {
      listPrice: 500000,
      salePrice: 450000,
      currency: 'VND'
    },
    required: false,
  })
  @Expose()
  price?: Record<string, unknown>;

  @ApiProperty({
    description: 'Kiểu giá sản phẩm',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
  })
  @Expose()
  typePrice?: PriceTypeEnum;

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: [String],
    example: ['hội thảo', 'marketing', 'digital', '2024'],
    required: false,
  })
  @Expose()
  tags?: string[];

  // ❌ REMOVED: Không hiển thị status cho user vì đã set mặc định PENDING
  // @ApiProperty({
  //   description: 'Trạng thái sản phẩm',
  //   enum: EntityStatusEnum,
  //   example: EntityStatusEnum.PENDING,
  // })
  // @Expose()
  // status: EntityStatusEnum;

  @ApiProperty({
    description: 'Custom fields',
    example: [
      {
        customFieldId: 1,
        value: { value: 'Hội thảo chuyên sâu' }
      }
    ],
    required: false,
  })
  @Expose()
  customFields?: Record<string, unknown>[];

  @ApiProperty({
    description: 'Thời gian tạo sản phẩm (timestamp)',
    example: 1704067200000,
    nullable: true,
  })
  @Expose()
  createdAt: number | null;

  @ApiProperty({
    description: 'Thời gian cập nhật sản phẩm (timestamp)',
    example: 1704067200000,
    nullable: true,
  })
  @Expose()
  updatedAt: number | null;

  @ApiProperty({
    description: 'ID người tạo sản phẩm',
    example: 1,
  })
  @Expose()
  userId: number;

  // ========== THÔNG TIN TỪ EVENT_PRODUCTS ==========

  @ApiProperty({
    description: 'Hình thức tham gia sự kiện',
    example: ParticipationTypeEnum.ONLINE,
    enum: ParticipationTypeEnum,
    enumName: 'ParticipationTypeEnum',
    examples: {
      online: { value: ParticipationTypeEnum.ONLINE, description: 'Sự kiện trực tuyến' },
      offline: { value: ParticipationTypeEnum.OFFLINE, description: 'Sự kiện trực tiếp' }
    },
  })
  @Expose()
  participationType: ParticipationTypeEnum;

  @ApiProperty({
    description: 'Địa điểm tham gia sự kiện',
    example: 'Trung tâm Hội nghị Quốc gia, Hà Nội',
    required: false,
  })
  @Expose()
  location?: string;

  @ApiProperty({
    description: 'Đường dẫn tham gia sự kiện',
    example: 'https://zoom.us/j/123456789',
    required: false,
  })
  @Expose()
  participationUrl?: string;

  @ApiProperty({
    description: 'Thời gian bắt đầu sự kiện (epoch milliseconds)',
    example: 1704067200000,
  })
  @Expose()
  startDate: number;

  @ApiProperty({
    description: 'Ngày và giờ kết thúc sự kiện kèm múi giờ',
    example: '2024-01-15T18:00:00.000Z',
  })
  @Expose()
  endDate: Date;

  @ApiProperty({
    description: 'Tên múi giờ chuẩn',
    example: 'Asia/Ho_Chi_Minh',
  })
  @Expose()
  timeZone: string;

  // ========== THÔNG TIN 1:MANY RELATIONSHIPS ==========

  @ApiProperty({
    description: 'Danh sách vé sự kiện',
    type: [EventProductTicketResponseDto],
    required: false,
  })
  @Expose()
  @Type(() => EventProductTicketResponseDto)
  tickets?: EventProductTicketResponseDto[];

  @ApiProperty({
    description: 'Danh sách hình ảnh sản phẩm',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2' },
        url: { type: 'string', example: 'https://cdn.example.com/event-image.jpg' },
        name: { type: 'string', example: 'event-banner.jpg' },
        size: { type: 'number', example: 2048000 },
        mimeType: { type: 'string', example: 'image/jpeg' }
      }
    },
    required: false,
  })
  @Expose()
  images?: Record<string, unknown>[];

  // ========== THÔNG TIN THỐNG KÊ (OPTIONAL) ==========

  @ApiProperty({
    description: 'Tổng số lượng vé của sự kiện',
    example: 500,
    required: false,
  })
  @Expose()
  totalTicketQuantity?: number;

  @ApiProperty({
    description: 'Giá vé thấp nhất',
    example: 200000,
    required: false,
  })
  @Expose()
  minTicketPrice?: number;

  @ApiProperty({
    description: 'Giá vé cao nhất',
    example: 1000000,
    required: false,
  })
  @Expose()
  maxTicketPrice?: number;

  @ApiProperty({
    description: 'Số loại vé khác nhau',
    example: 3,
    required: false,
  })
  @Expose()
  ticketTypesCount?: number;
}
