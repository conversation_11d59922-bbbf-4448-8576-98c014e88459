import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { CustomerProductRepository, ComboProductRepository } from '@modules/business/repositories';
import { ProductTypeEnum, EntityStatusEnum } from '@modules/business/enums';
import { ComboItemOperationDto, ComboItemDataDto } from '../dto/combo-product/combo-operations.dto';

/**
 * ValidationHelper cho Combo Product
 * Xử lý validation logic đặc thù cho sản phẩm combo
 */
@Injectable()
export class ComboValidationHelper {
  private readonly logger = new Logger(ComboValidationHelper.name);

  constructor(
    private readonly customerProductRepository: CustomerProductRepository,
    private readonly comboProductRepository: ComboProductRepository,
  ) {}

  /**
   * Validate combo items trong create/update operations
   * @param comboItems Danh sách combo items
   * @param userId ID người dùng (để kiểm tra quyền sở hữu sản phẩm)
   * @throws AppException nếu validation thất bại
   */
  async validateComboItems(comboItems: ComboItemDataDto[], userId: number): Promise<void> {
    if (!comboItems || comboItems.length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Combo phải chứa ít nhất 1 sản phẩm'
      );
    }

    // Kiểm tra không có sản phẩm trùng lặp
    const productIds = comboItems.map(item => item.productId);
    const uniqueProductIds = new Set(productIds);
    if (productIds.length !== uniqueProductIds.size) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Combo không được chứa sản phẩm trùng lặp'
      );
    }

    // Validate từng combo item
    for (let i = 0; i < comboItems.length; i++) {
      const item = comboItems[i];
      await this.validateSingleComboItem(item, userId, i + 1);
    }
  }

  /**
   * Validate một combo item
   * @param item Combo item cần validate
   * @param index Vị trí trong danh sách (để hiển thị lỗi)
   * @param userId ID người dùng
   */
  private async validateSingleComboItem(
    item: ComboItemDataDto,
    userId: number,
    index: number
  ): Promise<void> {
    // Validate basic fields
    if (!item.productId || !item.total) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Sản phẩm thứ ${index} trong combo: productId và total là bắt buộc`
      );
    }

    if (item.productId <= 0 || item.total <= 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Sản phẩm thứ ${index} trong combo: productId và total phải lớn hơn 0`
      );
    }

    // Kiểm tra sản phẩm có tồn tại và thuộc về user không
    const product = await this.customerProductRepository.findByIdAndUserId(item.productId, userId);

    if (!product) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
        `Sản phẩm thứ ${index} trong combo: Không tìm thấy sản phẩm với ID ${item.productId} hoặc bạn không có quyền sử dụng`
      );
    }

    // Kiểm tra trạng thái sản phẩm
    if (product.status !== EntityStatusEnum.APPROVED) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Sản phẩm thứ ${index} trong combo: Sản phẩm "${product.name}" chưa được duyệt, không thể thêm vào combo`
      );
    }

    // Kiểm tra không thể thêm combo vào combo (tránh vòng lặp)
    if (product.productType === ProductTypeEnum.COMBO) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Sản phẩm thứ ${index} trong combo: Không thể thêm combo vào combo khác`
      );
    }
  }



  /**
   * Validate một combo operation
   * @param operation Operation cần validate
   * @param index Vị trí trong danh sách
   * @param userId ID người dùng
   */
  private async validateSingleComboOperation(
    operation: ComboItemOperationDto,
    index: number,
    userId: number
  ): Promise<void> {
    switch (operation.operation) {
      case 'add':
        if (!operation.data) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVALID_INPUT,
            `Operation thứ ${index}: ADD operation phải có data`
          );
        }
        await this.validateSingleComboItem(operation.data, userId, index);
        break;

      case 'update':
        if (!operation.id) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVALID_INPUT,
            `Operation thứ ${index}: UPDATE operation phải có id`
          );
        }
        if (!operation.data) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVALID_INPUT,
            `Operation thứ ${index}: UPDATE operation phải có data`
          );
        }
        await this.validateSingleComboItem(operation.data, userId, index);
        break;

      case 'delete':
        if (!operation.id) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVALID_INPUT,
            `Operation thứ ${index}: DELETE operation phải có id`
          );
        }
        break;

      default:
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          `Operation thứ ${index}: Loại operation "${operation.operation}" không hợp lệ`
        );
    }
  }

  /**
   * Validate combo pricing
   * @param price Giá combo
   * @param comboItems Danh sách sản phẩm trong combo
   * @throws AppException nếu validation thất bại
   */
  validateComboPricing(price: any, comboItems: ComboItemDataDto[]): void {
    if (!price) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Combo phải có thông tin giá'
      );
    }

    // Validate price structure
    if (typeof price !== 'object') {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Thông tin giá combo không hợp lệ'
      );
    }

    // Validate required price fields
    if (!price.listPrice && !price.salePrice) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Combo phải có ít nhất listPrice hoặc salePrice'
      );
    }

    // Validate price values
    if (price.listPrice && (typeof price.listPrice !== 'number' || price.listPrice <= 0)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'listPrice phải là số dương'
      );
    }

    if (price.salePrice && (typeof price.salePrice !== 'number' || price.salePrice <= 0)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'salePrice phải là số dương'
      );
    }

    // Validate salePrice <= listPrice if both exist
    if (price.listPrice && price.salePrice && price.salePrice > price.listPrice) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Giá bán không được lớn hơn giá niêm yết'
      );
    }
  }

  /**
   * Validate combo inventory (maxQuantity)
   * @param maxQuantity Số lượng tối đa
   * @param purchaseCount Số lượt mua hiện tại
   * @throws AppException nếu validation thất bại
   */
  validateComboInventory(maxQuantity?: number, purchaseCount?: number): void {
    if (maxQuantity !== undefined && maxQuantity !== null) {
      if (typeof maxQuantity !== 'number' || maxQuantity < 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Số lượng tối đa phải là số không âm'
        );
      }

      if (purchaseCount && purchaseCount > maxQuantity) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Số lượt mua hiện tại không được lớn hơn số lượng tối đa'
        );
      }
    }

    if (purchaseCount !== undefined && purchaseCount !== null) {
      if (typeof purchaseCount !== 'number' || purchaseCount < 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Số lượt mua phải là số không âm'
        );
      }
    }
  }

  /**
   * Validate combo có thể được mua không
   * @param comboId ID combo
   * @param requestedQuantity Số lượng muốn mua
   * @returns true nếu có thể mua
   */
  async validateComboPurchaseAvailability(
    comboId: number, 
    requestedQuantity: number = 1
  ): Promise<boolean> {
    const canPurchase = await this.comboProductRepository.canPurchase(comboId, requestedQuantity);
    
    if (!canPurchase) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
        'Combo đã hết hàng hoặc không đủ số lượng'
      );
    }

    return true;
  }
}
