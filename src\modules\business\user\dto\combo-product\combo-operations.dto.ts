import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNumber,
  IsOptional,
  ValidateNested,
  Min,
  IsArray,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ProductImageOperationDto } from '../image-operations/image-operation.dto';

/**
 * Enum cho các loại operation trên combo items
 */
export enum ComboItemOperationType {
  ADD = 'add',
  UPDATE = 'update',
  DELETE = 'delete',
}

/**
 * DTO cho data của combo item (dùng trong ADD và UPDATE operations)
 */
export class ComboItemDataDto {
  @ApiProperty({
    description: 'ID sản phẩm trong combo',
    example: 123,
  })
  @IsNumber()
  productId: number;

  @ApiProperty({
    description: 'Số lượng sản phẩm trong combo',
    example: 2,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  total: number;
}

/**
 * DTO cho combo item operation (ADD/UPDATE/DELETE)
 */
export class ComboItemOperationDto {
  @ApiProperty({
    description: 'Loại operation',
    enum: ComboItemOperationType,
    example: ComboItemOperationType.ADD,
  })
  @IsEnum(ComboItemOperationType)
  operation: ComboItemOperationType;

  @ApiProperty({
    description: 'ID của combo item (bắt buộc cho UPDATE và DELETE)',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  id?: number;

  @ApiProperty({
    description: 'Dữ liệu combo item (bắt buộc cho ADD và UPDATE)',
    type: ComboItemDataDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ComboItemDataDto)
  data?: ComboItemDataDto;
}

/**
 * DTO cho tất cả operations (combo items + images)
 * 🎯 CÁCH DUY NHẤT để thao tác combo items và images
 */
export class ComboOperationsDto {
  @ApiProperty({
    description: 'Operations cho combo items (ADD/UPDATE/DELETE)',
    type: [ComboItemOperationDto],
    example: [
      {
        operation: 'add',
        data: { productId: 123, total: 2 }
      },
      {
        operation: 'update',
        id: 1,
        data: { productId: 456, total: 1 }
      },
      {
        operation: 'delete',
        id: 2
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ComboItemOperationDto)
  comboItems?: ComboItemOperationDto[];

  @ApiProperty({
    description: 'Operations cho images (ADD/DELETE) - Type-safe với ProductImageOperationDto',
    type: [ProductImageOperationDto],
    example: [
      {
        operation: 'add',
        mediaId: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2'
      },
      {
        operation: 'delete',
        entityMediaId: 456
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductImageOperationDto)
  images?: ProductImageOperationDto[]; // Type-safe thay vì any[]
}
