import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString, IsArray, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho giá của phân loại sản phẩm
 */
export class ClassificationPriceDto {
  @ApiProperty({
    description: 'Giá trị',
    example: 35990000,
  })
  @IsNumber()
  value: number;

  @ApiProperty({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
  })
  @IsString()
  currency: string;
}

/**
 * DTO cho trường tùy chỉnh với giá trị chi tiết
 */
export class CustomFieldValueWithDetailsResponseDto {
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 1,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'ID cấu hình',
    example: 'product_color',
  })
  @IsString()
  configId: string;

  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: '<PERSON><PERSON><PERSON> sắc',
  })
  @IsString()
  label: string;

  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: 'Cấu hình JSON',
    example: { placeholder: 'Nhập màu sắc' },
  })
  configJson: any;

  @ApiProperty({
    description: 'ID nhân viên tạo',
    example: 1,
    nullable: true,
  })
  @IsOptional()
  @IsNumber()
  employeeId: number | null;

  @ApiProperty({
    description: 'ID người dùng tạo',
    example: 1,
    nullable: true,
  })
  @IsOptional()
  @IsNumber()
  userId: number | null;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: 1625097600000,
  })
  @IsNumber()
  createAt: number;

  @ApiProperty({
    description: 'Trạng thái',
    example: 'APPROVED',
  })
  @IsString()
  status: string;

  @ApiProperty({
    description: 'Giá trị của trường',
    example: 'Đỏ',
  })
  value: any;
}

/**
 * DTO cho phân loại sản phẩm
 */
export class ClassificationResponseDto {
  @ApiProperty({
    description: 'ID phân loại',
    example: 1,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Loại phân loại',
    example: 'color',
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: 'Giá phân loại',
    type: ClassificationPriceDto,
    nullable: true,
  })
  @IsOptional()
  @Type(() => ClassificationPriceDto)
  price: ClassificationPriceDto | null;

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh',
    type: [CustomFieldValueWithDetailsResponseDto],
    nullable: true,
  })
  @IsOptional()
  @IsArray()
  @Type(() => CustomFieldValueWithDetailsResponseDto)
  customFields: CustomFieldValueWithDetailsResponseDto[] | null;
}
