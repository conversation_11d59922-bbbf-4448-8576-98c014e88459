import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho memories của agent
 */
export class AgentMemoryDto {

  /**
   * Tiêu đề của memories
   */
  @ApiProperty({
    description: 'Tiêu đề của memories',
    example: 'memories',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  /**
   * Lý do cho memories
   */
  @ApiProperty({
    description: 'Lý do cho memories',
    example: 'memories',
  })
  @IsString()
  @IsNotEmpty()
  reason: string;

  /**
   * Nội dung ghi nhớ
   */
  @ApiProperty({
    description: 'Nội dung ghi nhớ',
    example: 'memories',
  })
  @IsString()
  @IsNotEmpty()
  content: string;
}
