import { Injectable } from '@nestjs/common';
import { PointRepository } from '@modules/r-point/repositories';
import { Point } from '@modules/r-point/entities';
import { CreatePointDto, PointFilterDto, UpdatePointDto } from '../dto';
import { FindOptionsWhere, ILike, Repository } from 'typeorm';
import { AppException, ErrorCode } from '@/common';

/**
 * Service xử lý logic liên quan đến point cho admin
 */
@Injectable()
export class PointAdminService {
  constructor(private readonly pointRepository: PointRepository) {}

  /**
   * Tạo mới gói point
   * @param createPointDto Thông tin gói point cần tạo
   * @returns Gói point đã tạo
   */
  async create(createPointDto: CreatePointDto): Promise<Point> {
    try {
      // Mặc định tạo gói thường (không phải customize)
      // Không cần tính toán rate cho gói không phải customize

      // Tạo entity point mới (gói thường, không phải customize)
      const newPoint = this.pointRepository.create({
        name: createPointDto.name,
        cash: createPointDto.cash,
        // Không đặt rate cho gói không phải customize
        point: createPointDto.point,
        description: createPointDto.description,
        isCustomize: false, // Mặc định là gói thường, không phải customize
      });

      // Lưu vào database
      return this.pointRepository.save(newPoint);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.INVALID_POINT_DATA, `Không thể tạo gói point: ${error.message}`);
    }
  }

  /**
   * Lấy danh sách gói point với phân trang và lọc
   * @param filterDto Thông tin lọc và phân trang
   * @returns Danh sách gói point và thông tin phân trang
   */
  async findAll(filterDto: PointFilterDto): Promise<{ items: Point[]; total: number; page: number; limit: number }> {
    try {
      const { page = 1, limit = 10, search, isCustomize, sortBy = 'id', sortOrder = 'ASC' } = filterDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: FindOptionsWhere<Point> = {};

      // Tìm kiếm theo tên nếu có
      if (search) {
        where.name = ILike(`%${search}%`);
      }

      // Lọc theo loại gói (customize hoặc không)
      if (isCustomize !== undefined) {
        where.isCustomize = isCustomize;
      }

      // Thực hiện truy vấn với phân trang và sắp xếp
      const [items, total] = await this.pointRepository.findAndCount({
        where,
        order: { [sortBy]: sortOrder },
        skip,
        take: limit,
      });

      return {
        items,
        total,
        page,
        limit,
      };
    } catch (error) {
      throw new AppException(ErrorCode.INVALID_POINT_DATA, `Không thể lấy danh sách gói point: ${error.message}`);
    }
  }

  /**
   * Lấy thông tin chi tiết của một gói point
   * @param id ID của gói point
   * @returns Thông tin chi tiết gói point
   */
  async findOne(id: number): Promise<Point> {
    try {
      const point = await this.pointRepository.findOne({ where: { id } });

      if (!point) {
        throw new AppException(ErrorCode.POINT_NOT_FOUND, `Không tìm thấy gói point với ID ${id}`);
      }

      return point;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.INVALID_POINT_DATA, `Không thể lấy thông tin gói point: ${error.message}`);
    }
  }

  /**
   * Cập nhật thông tin gói point
   * @param id ID của gói point
   * @param updatePointDto Thông tin cần cập nhật
   * @returns Gói point đã cập nhật
   */
  async update(id: number, updatePointDto: UpdatePointDto): Promise<Point> {
    try {
      // Kiểm tra gói point có tồn tại không
      const point = await this.findOne(id);

      // Kiểm tra nếu cập nhật thành gói customize
      if (updatePointDto.isCustomize === true) {
        // Nếu chuyển từ gói thường sang gói customize, cần có min, max và rate
        const min = updatePointDto.min !== undefined ? updatePointDto.min : point.min;
        const max = updatePointDto.max !== undefined ? updatePointDto.max : point.max;
        const rate = updatePointDto.rate !== undefined ? updatePointDto.rate : point.rate;

        if (!min || !max || !rate) {
          throw new AppException(ErrorCode.INVALID_POINT_DATA, 'Gói customize phải có giá trị tối thiểu, tối đa và tỷ lệ quy đổi');
        }

        if (min >= max) {
          throw new AppException(ErrorCode.INVALID_POINT_DATA, 'Giá trị tối thiểu phải nhỏ hơn giá trị tối đa');
        }

        if (min < 20000 || max > 500000) {
          throw new AppException(ErrorCode.INVALID_POINT_DATA, 'Giá trị tối thiểu phải lớn hơn hoặc bằng 20,000 VND và giá trị tối đa phải nhỏ hơn hoặc bằng 500,000 VND');
        }
      }

      // Tính lại số point nếu có thay đổi cash hoặc rate
      let newPoint = point.point;

      // Nếu là gói thường (không phải customize)
      if (!point.isCustomize && updatePointDto.isCustomize !== true) {
        if (updatePointDto.cash !== undefined) {
          // Kiểm tra giới hạn cash
          if (updatePointDto.cash < 20000 || updatePointDto.cash > 500000) {
            throw new AppException(ErrorCode.INVALID_POINT_DATA, 'Số tiền phải trong khoảng từ 20,000 VND đến 500,000 VND');
          }

          const rate = updatePointDto.rate !== undefined ? updatePointDto.rate : point.rate;
          newPoint = Math.floor(updatePointDto.cash / rate);
        } else if (updatePointDto.rate !== undefined) {
          newPoint = Math.floor(point.cash / updatePointDto.rate);
        }
      }
      // Nếu là gói customize hoặc đang chuyển thành gói customize
      else if (point.isCustomize || updatePointDto.isCustomize === true) {
        // Đối với gói customize, point được tính dựa trên cash và rate
        // Nhưng cash có thể thay đổi trong khoảng min-max, nên chỉ cập nhật rate
        if (updatePointDto.rate !== undefined) {
          // Sử dụng cash mặc định cho gói customize
          const defaultCash = 100000; // Giá trị mặc định để tính point
          newPoint = Math.floor(defaultCash / updatePointDto.rate);
        }
      }

      // Cập nhật thông tin
      const updatedPoint = await this.pointRepository.save({
        ...point,
        ...updatePointDto,
        point: newPoint,
      });

      return updatedPoint;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.INVALID_POINT_DATA, `Không thể cập nhật gói point: ${error.message}`);
    }
  }

  /**
   * Xóa gói point
   * @param id ID của gói point
   * @returns Thông báo xóa thành công
   */
  async remove(id: number): Promise<{ message: string }> {
    try {
      // Kiểm tra gói point có tồn tại không
      await this.findOne(id);

      // Xóa gói point
      await this.pointRepository.delete(id);

      return { message: `Đã xóa gói point với ID ${id}` };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.INVALID_POINT_DATA, `Không thể xóa gói point: ${error.message}`);
    }
  }

  /**
   * Xóa nhiều gói point
   * @param ids Danh sách ID của các gói point cần xóa
   */
  async bulkDelete(ids: string[]): Promise<void> {
    try {
      if (!ids || ids.length === 0) {
        throw new AppException(ErrorCode.INVALID_POINT_DATA, 'Danh sách ID không được để trống');
      }

      // Kiểm tra xem các gói point có tồn tại không
      const points = await this.pointRepository.findBy({ id: { $in: ids } as any });
      if (points.length !== ids.length) {
        throw new AppException(ErrorCode.POINT_NOT_FOUND, 'Một số gói point không tồn tại');
      }

      // Xóa các gói point
      await this.pointRepository.delete(ids);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.INVALID_POINT_DATA, `Không thể xóa các gói point: ${error.message}`);
    }
  }
}
