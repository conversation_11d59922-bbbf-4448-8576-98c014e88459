import { ApiProperty } from '@nestjs/swagger';
import { CustomFieldResponseDto } from './custom-field-response.dto';
import { TagResponseDto } from '../tag/tag-response.dto';

/**
 * DTO cho phản hồi audience
 */
export class AudienceResponseDto {
  /**
   * ID của audience
   * @example 1
   */
  @ApiProperty({
    description: 'ID của audience',
    example: 1,
  })
  id: number;

  /**
   * ID của employee
   * @example 1
   */
  @ApiProperty({
    description: 'ID của employee',
    example: 1,
  })
  employeeId: number;

  /**
   * Tên của khách hàng
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({
    description: 'Tên của khách hàng',
    example: 'Nguyễn Văn A',
    nullable: true,
  })
  name: string | null;

  /**
   * Email của khách hàng
   * @example "<EMAIL>"
   */
  @ApiProperty({
    description: 'Email của khách hàng',
    example: '<EMAIL>',
  })
  email: string;

  /**
   * <PERSON><PERSON> điện thoại của khách hàng
   * @example "+84912345678"
   */
  @ApiProperty({
    description: 'Số điện thoại của khách hàng',
    example: '+84912345678',
    nullable: true,
  })
  phone: string | null;

  /**
   * Mã quốc gia của số điện thoại
   * @example "+84"
   */
  @ApiProperty({
    description: 'Mã quốc gia của số điện thoại',
    example: '+84',
    nullable: true,
  })
  countryCode: string | null;

  /**
   * URL avatar của khách hàng
   * @example "https://cdn.example.com/avatars/customer-123.jpg"
   */
  @ApiProperty({
    description: 'URL avatar của khách hàng',
    example: 'https://cdn.example.com/avatars/customer-123.jpg',
    nullable: true,
  })
  avatar: string | null;

  /**
   * Các trường tùy chỉnh
   */
  @ApiProperty({
    description: 'Các trường tùy chỉnh',
    type: [CustomFieldResponseDto],
  })
  customFields: CustomFieldResponseDto[];

  /**
   * Các tag
   */
  @ApiProperty({
    description: 'Các tag',
    type: [TagResponseDto],
  })
  tags: TagResponseDto[];

  /**
   * Thời gian tạo (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1619171200,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1619171200,
  })
  updatedAt: number;
}

/**
 * DTO cho phản hồi cập nhật audience (bao gồm thông tin avatar upload nếu có)
 */
export class UpdateAudienceResponseDto extends AudienceResponseDto {
  /**
   * URL tạm thời để upload avatar (nếu có yêu cầu upload avatar)
   * @example "https://s3.amazonaws.com/bucket/path/to/upload?signature=..."
   */
  @ApiProperty({
    description: 'URL tạm thời để upload avatar (nếu có yêu cầu upload avatar)',
    example: 'https://s3.amazonaws.com/bucket/path/to/upload?signature=...',
    nullable: true,
    required: false,
  })
  avatarUploadUrl?: string;

  /**
   * S3 key của avatar (nếu có yêu cầu upload avatar)
   * @example "marketing/customer_avatars/2024/01/admin/1234567890-uuid.jpg"
   */
  @ApiProperty({
    description: 'S3 key của avatar (nếu có yêu cầu upload avatar)',
    example: 'marketing/customer_avatars/2024/01/admin/1234567890-uuid.jpg',
    nullable: true,
    required: false,
  })
  avatarS3Key?: string;

  /**
   * Thời gian hết hạn của URL upload (Unix timestamp) (nếu có yêu cầu upload avatar)
   * @example 1619174800
   */
  @ApiProperty({
    description: 'Thời gian hết hạn của URL upload (Unix timestamp) (nếu có yêu cầu upload avatar)',
    example: 1619174800,
    nullable: true,
    required: false,
  })
  avatarUploadExpiresAt?: number;
}
