import { Column, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

/**
 * Enum trạng thái hợp đồng
 */
export enum ContractStatusEnum {
  DRAFT = 'DRAFT',
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

/**
 * Enum loại hợp đồng
 */
export enum ContractTypeEnum {
  INDIVIDUAL = 'INDIVIDUAL',
  BUSINESS = 'BUSINESS',
}

/**
 * Entity hợp đồng nguyên tắc
 */
@Entity({ name: 'rule_contract' })
export class RuleContract {
  /**
   * ID hợp đồng
   */
  @PrimaryGeneratedColumn({ type: 'integer' })
  id: number;

  /**
   * Mã người dùng
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number;

  /**
   * Tr<PERSON>ng thái hợp đồng
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: ContractStatusEnum,
    nullable: false,
  })
  status: ContractStatusEnum;

  /**
   * Loại hợp đồng
   */
  @Column({
    name: 'type',
    type: 'enum',
    enum: ContractTypeEnum,
    nullable: true,
  })
  type: ContractTypeEnum;

  /**
   * Đường dẫn hợp đồng
   */
  @Column({ name: 'contract_url_pdf', type: 'varchar', length: 255, nullable: true })
  contractUrlPdf: string;

  /**
   * Thời gian tạo hợp đồng
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number;

  /**
   * Thời gian đối tác ký hợp đồng
   */
  @Column({ name: 'user_signature_at', type: 'bigint', nullable: true })
  userSignatureAt: number;

  /**
   * Thời gian quản trị ký hợp đồng
   */
  @Column({ name: 'admin_signature_at', type: 'bigint', nullable: true })
  adminSignatureAt: number;

  /**
   * Lý do từ chối hợp đồng
   */
  @Column({ name: 'reject_reason', type: 'text', nullable: true })
  rejectReason: string;

  /**
   * Thời gian cập nhật hợp đồng (Unix timestamp)
   */
  @UpdateDateColumn({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)',
  })
  updatedAt: number;


  @Column({ name: 'info', type: 'jsonb', nullable: true })
  info: Record<string, any>;
}