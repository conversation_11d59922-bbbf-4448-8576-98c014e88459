import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { AppException } from '@/common/exceptions';
import { PaginatedResult } from '@/common/response';
import { MARKETING_ERROR_CODES } from '../../errors/marketing-error.code';
import { ZaloZnsCampaignRepository } from '../repositories/zalo-zns-campaign.repository';
import { ZaloOfficialAccountRepository } from '../repositories/zalo-official-account.repository';
import { ZaloZnsCampaign, ZaloZnsCampaignStatus } from '../entities/zalo-zns-campaign.entity';
import { CreateZnsCampaignDto, UpdateZnsCampaignDto, ZnsCampaignQueryDto } from '../dto/zalo/zns-campaign.dto';
import { QueueName, ZaloZnsJobName } from '@/shared/queue/queue.constants';
import { SendZnsCampaignJobData, SendZnsJobData, SendBatchZnsJobData } from '@/shared/queue/interfaces';

/**
 * Service xử lý chiến dịch ZNS
 */
@Injectable()
export class ZaloZnsCampaignService {
  private readonly logger = new Logger(ZaloZnsCampaignService.name);

  constructor(
    private readonly znsCampaignRepository: ZaloZnsCampaignRepository,
    private readonly zaloOfficialAccountRepository: ZaloOfficialAccountRepository,
    @InjectQueue(QueueName.ZALO_ZNS) private readonly znsQueue: Queue,
  ) {}

  /**
   * Tạo chiến dịch ZNS mới
   */
  async createCampaign(
    userId: number,
    oaId: string,
    createDto: CreateZnsCampaignDto,
  ): Promise<ZaloZnsCampaign> {
    try {
      // Kiểm tra quyền truy cập Official Account
      const officialAccount = await this.zaloOfficialAccountRepository.findByUserIdAndOaId(userId, oaId);
      if (!officialAccount) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED, 'Không tìm thấy Official Account');
      }

      const now = Date.now();
      const totalMessages = createDto.phoneList.length;

      // Tạo chiến dịch
      const campaign = await this.znsCampaignRepository.create({
        userId,
        oaId,
        name: createDto.name,
        description: createDto.description,
        templateId: createDto.templateId,
        templateData: createDto.templateData,
        phoneList: createDto.phoneList,
        totalMessages,
        status: createDto.scheduledAt ? ZaloZnsCampaignStatus.SCHEDULED : ZaloZnsCampaignStatus.DRAFT,
        scheduledAt: createDto.scheduledAt,
        createdAt: now,
        updatedAt: now,
      });

      this.logger.log(`Created ZNS campaign ${campaign.id} for user ${userId}`);
      return campaign;
    } catch (error) {
      this.logger.error(`Failed to create ZNS campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED, 'Không thể tạo chiến dịch ZNS');
    }
  }

  /**
   * Lấy danh sách chiến dịch với phân trang
   */
  async getCampaigns(
    userId: number,
    oaId: string,
    queryDto: ZnsCampaignQueryDto,
  ): Promise<PaginatedResult<ZaloZnsCampaign>> {
    try {
      // Kiểm tra quyền truy cập Official Account
      const officialAccount = await this.zaloOfficialAccountRepository.findByUserIdAndOaId(userId, oaId);
      if (!officialAccount) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED, 'Không tìm thấy Official Account');
      }

      return this.znsCampaignRepository.findWithPagination(
        userId,
        oaId,
        queryDto.page,
        queryDto.limit,
        queryDto.search,
        queryDto.status,
      );
    } catch (error) {
      this.logger.error(`Failed to get ZNS campaigns: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_GET_LIST_FAILED, 'Không thể lấy danh sách chiến dịch ZNS');
    }
  }

  /**
   * Lấy chi tiết chiến dịch
   */
  async getCampaignDetail(userId: number, campaignId: number): Promise<ZaloZnsCampaign> {
    try {
      const campaign = await this.znsCampaignRepository.findByUserIdAndId(userId, campaignId);
      if (!campaign) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND, 'Không tìm thấy chiến dịch ZNS');
      }

      return campaign;
    } catch (error) {
      this.logger.error(`Failed to get ZNS campaign detail: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND, 'Không thể lấy chi tiết chiến dịch ZNS');
    }
  }

  /**
   * Cập nhật chiến dịch
   */
  async updateCampaign(
    userId: number,
    campaignId: number,
    updateDto: UpdateZnsCampaignDto,
  ): Promise<ZaloZnsCampaign> {
    try {
      const campaign = await this.znsCampaignRepository.findByUserIdAndId(userId, campaignId);
      if (!campaign) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND, 'Không tìm thấy chiến dịch ZNS');
      }

      // Chỉ cho phép cập nhật chiến dịch ở trạng thái DRAFT hoặc SCHEDULED
      if (![ZaloZnsCampaignStatus.DRAFT, ZaloZnsCampaignStatus.SCHEDULED].includes(campaign.status)) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_INVALID_STATUS_FOR_UPDATE, 'Không thể cập nhật chiến dịch đang chạy hoặc đã hoàn thành');
      }

      const updateData: Partial<ZaloZnsCampaign> = {
        ...updateDto,
        updatedAt: Date.now(),
      };

      // Cập nhật tổng số tin nhắn nếu danh sách số điện thoại thay đổi
      if (updateDto.phoneList) {
        updateData.totalMessages = updateDto.phoneList.length;
      }

      // Cập nhật trạng thái nếu có scheduledAt
      if (updateDto.scheduledAt !== undefined) {
        updateData.status = updateDto.scheduledAt ? ZaloZnsCampaignStatus.SCHEDULED : ZaloZnsCampaignStatus.DRAFT;
      }

      const updatedCampaign = await this.znsCampaignRepository.update(campaignId, updateData);
      if (!updatedCampaign) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_UPDATE_FAILED, 'Không thể cập nhật chiến dịch ZNS');
      }

      this.logger.log(`Updated ZNS campaign ${campaignId} for user ${userId}`);
      return updatedCampaign;
    } catch (error) {
      this.logger.error(`Failed to update ZNS campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_UPDATE_FAILED, 'Không thể cập nhật chiến dịch ZNS');
    }
  }

  /**
   * Xóa chiến dịch
   */
  async deleteCampaign(userId: number, campaignId: number): Promise<boolean> {
    try {
      const campaign = await this.znsCampaignRepository.findByUserIdAndId(userId, campaignId);
      if (!campaign) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND, 'Không tìm thấy chiến dịch ZNS');
      }

      // Chỉ cho phép xóa chiến dịch ở trạng thái DRAFT, COMPLETED, FAILED, CANCELLED
      if ([ZaloZnsCampaignStatus.RUNNING, ZaloZnsCampaignStatus.SCHEDULED].includes(campaign.status)) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_INVALID_STATUS_FOR_DELETE, 'Không thể xóa chiến dịch đang chạy hoặc đã lên lịch');
      }

      const deleted = await this.znsCampaignRepository.delete(campaignId);
      if (!deleted) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_DELETE_FAILED, 'Không thể xóa chiến dịch ZNS');
      }

      this.logger.log(`Deleted ZNS campaign ${campaignId} for user ${userId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete ZNS campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_DELETE_FAILED, 'Không thể xóa chiến dịch ZNS');
    }
  }

  /**
   * Chạy chiến dịch ngay lập tức
   */
  async runCampaign(userId: number, campaignId: number): Promise<ZaloZnsCampaign> {
    try {
      const campaign = await this.znsCampaignRepository.findByUserIdAndId(userId, campaignId);
      if (!campaign) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND, 'Không tìm thấy chiến dịch ZNS');
      }

      // Chỉ cho phép chạy chiến dịch ở trạng thái DRAFT hoặc SCHEDULED
      if (![ZaloZnsCampaignStatus.DRAFT, ZaloZnsCampaignStatus.SCHEDULED].includes(campaign.status)) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_INVALID_STATUS_FOR_RUN, 'Chiến dịch không ở trạng thái có thể chạy');
      }

      // Cập nhật trạng thái thành RUNNING
      const now = Date.now();
      await this.znsCampaignRepository.update(campaignId, {
        status: ZaloZnsCampaignStatus.RUNNING,
        startedAt: now,
        updatedAt: now,
      });

      // Tạo job để gửi ZNS - đẩy vào queue để worker bên khác xử lý
      const jobData: SendZnsCampaignJobData = {
        campaignId,
        oaId: campaign.oaId,
        templateId: campaign.templateId,
        templateData: campaign.templateData,
        phoneList: campaign.phoneList,
        batchSize: 10, // Gửi 10 tin nhắn mỗi batch
        batchDelay: 1000, // Delay 1 giây giữa các batch
      };

      const job = await this.znsQueue.add(ZaloZnsJobName.SEND_ZNS_CAMPAIGN, jobData, {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: 10, // Giữ lại 10 job hoàn thành
        removeOnFail: 50, // Giữ lại 50 job thất bại để debug
      });

      // Lưu job ID
      await this.znsCampaignRepository.update(campaignId, {
        jobIds: [job.id.toString()],
        updatedAt: Date.now(),
      });

      this.logger.log(`Started ZNS campaign ${campaignId} with job ${job.id}`);

      const updatedCampaign = await this.znsCampaignRepository.findById(campaignId);
      if (!updatedCampaign) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND, 'Không tìm thấy chiến dịch sau khi chạy');
      }

      return updatedCampaign;
    } catch (error) {
      this.logger.error(`Failed to run ZNS campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_RUN_FAILED, 'Không thể chạy chiến dịch ZNS');
    }
  }

  /**
   * Hủy chiến dịch
   */
  async cancelCampaign(userId: number, campaignId: number): Promise<ZaloZnsCampaign> {
    try {
      const campaign = await this.znsCampaignRepository.findByUserIdAndId(userId, campaignId);
      if (!campaign) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND, 'Không tìm thấy chiến dịch ZNS');
      }

      // Chỉ cho phép hủy chiến dịch ở trạng thái SCHEDULED hoặc RUNNING
      if (![ZaloZnsCampaignStatus.SCHEDULED, ZaloZnsCampaignStatus.RUNNING].includes(campaign.status)) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_INVALID_STATUS_FOR_CANCEL, 'Không thể hủy chiến dịch ở trạng thái hiện tại');
      }

      // Hủy các job đang chạy
      if (campaign.jobIds && campaign.jobIds.length > 0) {
        for (const jobId of campaign.jobIds) {
          try {
            const job = await this.znsQueue.getJob(jobId);
            if (job) {
              await job.remove();
            }
          } catch (error) {
            this.logger.warn(`Failed to remove job ${jobId}: ${error.message}`);
          }
        }
      }

      // Cập nhật trạng thái thành CANCELLED
      const updatedCampaign = await this.znsCampaignRepository.update(campaignId, {
        status: ZaloZnsCampaignStatus.CANCELLED,
        updatedAt: Date.now(),
      });

      if (!updatedCampaign) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND, 'Không tìm thấy chiến dịch sau khi hủy');
      }

      this.logger.log(`Cancelled ZNS campaign ${campaignId} for user ${userId}`);
      return updatedCampaign;
    } catch (error) {
      this.logger.error(`Failed to cancel ZNS campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CANCEL_FAILED, 'Không thể hủy chiến dịch ZNS');
    }
  }

  /**
   * Tạo job gửi ZNS đơn lẻ
   */
  async createSingleZnsJob(
    oaId: string,
    phone: string,
    templateId: string,
    templateData: Record<string, any>,
    campaignId?: number,
    trackingId?: string,
  ): Promise<string> {
    try {
      const jobData: SendZnsJobData = {
        oaId,
        phone,
        templateId,
        templateData,
        campaignId,
        trackingId: trackingId || `single_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      };

      const job = await this.znsQueue.add(ZaloZnsJobName.SEND_ZNS, jobData, {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: 10,
        removeOnFail: 50,
      });

      this.logger.log(`Created single ZNS job ${job.id} for phone ${phone}`);
      return job.id.toString();
    } catch (error) {
      this.logger.error(`Failed to create single ZNS job: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_JOB_CREATION_FAILED, 'Không thể tạo job gửi ZNS');
    }
  }

  /**
   * Tạo job gửi batch ZNS
   */
  async createBatchZnsJob(
    oaId: string,
    messages: Array<{
      phone: string;
      templateId: string;
      templateData: Record<string, any>;
      trackingId?: string;
    }>,
    campaignId?: number,
    batchIndex: number = 0,
    totalBatches: number = 1,
  ): Promise<string> {
    try {
      const jobData: SendBatchZnsJobData = {
        oaId,
        messages,
        campaignId,
        batchIndex,
        totalBatches,
      };

      const job = await this.znsQueue.add(ZaloZnsJobName.SEND_BATCH_ZNS, jobData, {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: 10,
        removeOnFail: 50,
      });

      this.logger.log(`Created batch ZNS job ${job.id} with ${messages.length} messages`);
      return job.id.toString();
    } catch (error) {
      this.logger.error(`Failed to create batch ZNS job: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_JOB_CREATION_FAILED, 'Không thể tạo job gửi batch ZNS');
    }
  }
}
