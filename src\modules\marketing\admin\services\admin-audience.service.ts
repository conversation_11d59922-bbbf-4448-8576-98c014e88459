import { Injectable } from '@nestjs/common';
import { AdminAudienceHasTagRepository, AdminAudienceRepository } from '@modules/marketing/admin/repositories';
import { AdminAudienceCustomFieldRepository } from '@modules/marketing/admin/repositories';
import { AdminTagRepository } from '../repositories/admin-tag.repository';
import { In, Like, FindOptionsWhere, Or } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import {
  CreateAudienceDto,
  UpdateAudienceDto,
  AudienceResponseDto,
  UpdateAudienceResponseDto,
  CustomFieldResponseDto,
  AudienceQueryDto,
  CreateAvatarUploadUrlDto,
  AvatarUploadUrlResponseDto,
  UpdateAvatarDto,
} from '../dto/audience';
import { TagResponseDto } from '../dto/tag';
import { PaginatedResponseDto, PaginationMetaDto } from '../dto/common';
import { AdminAudience, AdminAudienceCustomField, AdminAudienceHasTag, AdminTag } from '../entities';
import { AppException, ErrorCode } from '@/common';
import { BulkDeleteResponseDto } from '@/modules/marketing/common/dto';
import { S3Service } from '@/shared/services/s3.service';
import { CdnService } from '@/shared/services/cdn.service';
import { generateS3Key, CategoryFolderEnum } from '@/shared/utils/generators/s3-key-generator.util';
import { TimeIntervalEnum, FileSizeEnum } from '@/shared/utils';
import { ImageTypeEnum } from '@shared/utils/file/image-media_type.util';

/**
 * Service xử lý logic liên quan đến audience
 */
@Injectable()
export class AdminAudienceService {
  constructor(
    private readonly adminAudienceRepository: AdminAudienceRepository,
    private readonly adminAudienceCustomFieldRepository: AdminAudienceCustomFieldRepository,
    private readonly adminTagRepository: AdminTagRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly adminAudienceHasTagRepository: AdminAudienceHasTagRepository,
  ) {}

  /**
   * Tạo audience mới
   * @param employeeId ID của employee
   * @param createAudienceDto Dữ liệu tạo audience
   * @returns Audience đã tạo
   */
  @Transactional()
  async create(
    createAudienceDto: CreateAudienceDto,
  ): Promise<AudienceResponseDto> {
    // Tạo audience mới
    const audience = new AdminAudience();
    audience.name = createAudienceDto.name || '';
    audience.email = createAudienceDto.email;
    audience.phone = createAudienceDto.phone || '';
    audience.createdAt = Math.floor(Date.now() / 1000);
    audience.updatedAt = Math.floor(Date.now() / 1000);

    // Lưu audience
    const savedAudience = await this.adminAudienceRepository.save(audience);

    // Lưu tag
    if (createAudienceDto.tagIds && createAudienceDto.tagIds.length > 0) {
      const audienceHasTags = createAudienceDto.tagIds.map(tagId => {
        const audienceHasTag = new AdminAudienceHasTag();
        audienceHasTag.audienceId = (savedAudience as AdminAudience).id;
        audienceHasTag.tagId = tagId;
        return audienceHasTag;
      });
      await this.adminAudienceHasTagRepository.save(audienceHasTags);
    }



    // Tạo các trường tùy chỉnh nếu có
    let customFields: AdminAudienceCustomField[] = [];
    if (
      createAudienceDto.customFields &&
      createAudienceDto.customFields.length > 0
    ) {
      customFields = createAudienceDto.customFields.map((field) => {
        const customField = new AdminAudienceCustomField();
        customField.audienceId = (savedAudience as AdminAudience).id;
        customField.fieldName = field.fieldName;
        customField.fieldValue = field.fieldValue;
        customField.fieldType = field.fieldType;
        customField.createdAt = Math.floor(Date.now() / 1000);
        customField.updatedAt = Math.floor(Date.now() / 1000);
        return customField;
      });

      customFields =
        await this.adminAudienceCustomFieldRepository.save(customFields);
    }

    // Lấy các tag nếu có
    let tags: AdminTag[] = [];
    if (createAudienceDto.tagIds && createAudienceDto.tagIds.length > 0) {
      tags = await this.adminTagRepository.find({
        where: {
          id: In(createAudienceDto.tagIds),
        },
      });
    }

    // Đảm bảo savedAudience là một đối tượng AdminAudience, không phải mảng
    return this.mapToDto(savedAudience as AdminAudience, customFields, tags);
  }

  /**
   * Cập nhật audience
   * @param id ID của audience
   * @param updateAudienceDto Dữ liệu cập nhật audience
   * @returns Audience đã cập nhật (bao gồm thông tin avatar upload nếu có)
   */
  @Transactional()
  async update(
    id: number,
    updateAudienceDto: UpdateAudienceDto,
  ): Promise<UpdateAudienceResponseDto> {
    // Kiểm tra audience tồn tại
    const audience = await this.adminAudienceRepository.findOne({
      where: { id },
    });

    if (!audience) {
      throw new AppException(ErrorCode.AUDIENCE_NOT_FOUND, `Audience với ID ${id} không tồn tại`);
    }

    // Cập nhật thông tin audience
    if (updateAudienceDto.name !== undefined) {
      audience.name = updateAudienceDto.name || '';
    }

    if (updateAudienceDto.email) {
      audience.email = updateAudienceDto.email;
    }

    if (updateAudienceDto.phone !== undefined) {
      audience.phone = updateAudienceDto.phone || '';
    }

    audience.updatedAt = Math.floor(Date.now() / 1000);

    // Xử lý avatar nếu có yêu cầu upload
    let avatarS3Key: string | undefined;
    if (updateAudienceDto.avatarMediaType) {
      // Tạo S3 key cho avatar
      avatarS3Key = generateS3Key({
        baseFolder: 'marketing',
        categoryFolder: CategoryFolderEnum.CUSTOMER_AVATAR,
        useTimeFolder: true,
        prefix: `admin`,
        fileName: `avatar.${updateAudienceDto.avatarMediaType.split('/')[1]}`,
      });

      // Lưu S3 key vào database
      audience.avatar = avatarS3Key;
    }

    // Lưu audience
    const updatedAudience = await this.adminAudienceRepository.save(audience);

    // Cập nhật các trường tùy chỉnh nếu có
    let customFields: AdminAudienceCustomField[] = [];
    if (
      updateAudienceDto.customFields &&
      updateAudienceDto.customFields.length > 0
    ) {
      // Xóa các trường tùy chỉnh cũ
      await this.adminAudienceCustomFieldRepository.delete({ audienceId: id });

      // Tạo các trường tùy chỉnh mới
      customFields = updateAudienceDto.customFields.map((field) => {
        const customField = new AdminAudienceCustomField();
        customField.audienceId = id;
        customField.fieldName = field.fieldName;
        customField.fieldValue = field.fieldValue;
        customField.fieldType = field.fieldType;
        customField.createdAt = Math.floor(Date.now() / 1000);
        customField.updatedAt = Math.floor(Date.now() / 1000);
        return customField;
      });

      const savedCustomFields =
        await this.adminAudienceCustomFieldRepository.save(customFields);
      customFields = Array.isArray(savedCustomFields)
        ? savedCustomFields
        : [savedCustomFields];
    } else {
      // Lấy các trường tùy chỉnh hiện tại
      customFields = await this.adminAudienceCustomFieldRepository.find({
        where: { audienceId: id },
      });
    }

    // Lấy các tag nếu có
    let tags: AdminTag[] = [];
    if (updateAudienceDto.tagIds && updateAudienceDto.tagIds.length > 0) {
      tags = await this.adminTagRepository.find({
        where: {
          id: In(updateAudienceDto.tagIds),
        },
      });
    }

    // Tạo response DTO cơ bản
    const baseResponse = this.mapToDto(updatedAudience as AdminAudience, customFields, tags);

    // Tạo response với thông tin avatar upload nếu có
    const response = new UpdateAudienceResponseDto();
    Object.assign(response, baseResponse);

    // Xử lý avatar upload nếu có yêu cầu
    if (updateAudienceDto.avatarMediaType && avatarS3Key) {
      try {
        // Tạo presigned URL với S3 key đã được lưu vào database
        const uploadUrl = await this.s3Service.createPresignedWithID(
          avatarS3Key,
          TimeIntervalEnum.ONE_HOUR, // URL hết hạn sau 1 giờ
          updateAudienceDto.avatarMediaType as any, // Cast to MediaType
          FileSizeEnum.FIVE_MB, // Giới hạn 5MB cho avatar
        );

        // Tính thời gian hết hạn
        const expiresAt = Math.floor(Date.now() / 1000) + (60 * 60); // 1 giờ

        // Thêm thông tin avatar upload vào response
        response.avatarUploadUrl = uploadUrl;
        response.avatarS3Key = avatarS3Key;
        response.avatarUploadExpiresAt = expiresAt;
      } catch (error) {
        // Log lỗi nhưng không làm fail toàn bộ request
        console.error('Error creating avatar upload URL:', error);
      }
    }

    return response;
  }

  /**
   * Lấy danh sách audience của employee với phân trang và filter
   * @param employeeId ID của employee
   * @param query Tham số truy vấn
   * @returns Danh sách audience với phân trang
   */
  async findAll(
    query: AudienceQueryDto,
  ): Promise<PaginatedResponseDto<AudienceResponseDto>> {
    const {
      page = 1,
      limit = 10,
      search,
      name,
      email,
      phone,
      tagId,
      customFieldName,
      customFieldValue,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = query;

    // Tính toán offset
    const offset = (page - 1) * limit;

    // Tạo điều kiện cơ bản
    let where: FindOptionsWhere<AdminAudience> | FindOptionsWhere<AdminAudience>[] = {};

    // Thêm điều kiện tìm kiếm tổng hợp (search trong name, email, phone)
    if (search) {
      where = [
        { name: Like(`%${search}%`) },
        { email: Like(`%${search}%`) },
        { phone: Like(`%${search}%`) }
      ];
    } else {
      // Thêm điều kiện tìm kiếm theo tên
      if (name) {
        (where as FindOptionsWhere<AdminAudience>).name = Like(`%${name}%`);
      }

      // Thêm điều kiện tìm kiếm theo email
      if (email) {
        (where as FindOptionsWhere<AdminAudience>).email = Like(`%${email}%`);
      }

      // Thêm điều kiện tìm kiếm theo số điện thoại
      if (phone) {
        (where as FindOptionsWhere<AdminAudience>).phone = Like(`%${phone}%`);
      }
    }

    // Nếu có filter theo tagId, cần lấy audience IDs từ bảng liên kết trước
    let filteredAudienceIds: number[] | undefined;
    if (tagId) {
      const audienceHasTagsForFilter = await this.adminAudienceHasTagRepository.find({
        where: { tagId },
      });
      filteredAudienceIds = audienceHasTagsForFilter.map(aht => aht.audienceId);

      // Nếu không có audience nào có tag này, trả về kết quả rỗng
      if (filteredAudienceIds.length === 0) {
        return {
          data: [],
          meta: {
            total: 0,
            page,
            limit,
            totalPages: 0,
            hasPreviousPage: false,
            hasNextPage: false,
          },
        };
      }

      // Thêm điều kiện filter theo audience IDs
      if (Array.isArray(where)) {
        where = where.map(w => ({ ...w, id: In(filteredAudienceIds!) }));
      } else {
        where = { ...where, id: In(filteredAudienceIds!) };
      }
    }

    // Đếm tổng số audience
    const total = await this.adminAudienceRepository.count({ where });

    // Lấy danh sách audience với phân trang và sắp xếp
    const audiences = await this.adminAudienceRepository.find({
      where,
      order: { [sortBy]: sortDirection },
      skip: offset,
      take: limit,
    });

    // Lấy danh sách ID của audience
    const audienceIds = audiences.map((a) => a.id);

    // Lấy tất cả các trường tùy chỉnh của các audience
    let customFieldsQuery: FindOptionsWhere<AdminAudienceCustomField> = {
      audienceId: In(audienceIds),
    };

    // Thêm điều kiện tìm kiếm theo tên trường tùy chỉnh
    if (customFieldName) {
      customFieldsQuery.fieldName = Like(`%${customFieldName}%`);
    }

    // Thêm điều kiện tìm kiếm theo giá trị trường tùy chỉnh
    if (customFieldValue) {
      customFieldsQuery.fieldValue = Like(`%${customFieldValue}%`);
    }

    const customFields = await this.adminAudienceCustomFieldRepository.find({
      where: customFieldsQuery,
    });

    // Lấy tất cả tags của các audience
    const audienceHasTags = await this.adminAudienceHasTagRepository.find({
      where: { audienceId: In(audienceIds) },
    });

    // Lấy tất cả tag IDs
    const tagIds = [...new Set(audienceHasTags.map(aht => aht.tagId))];

    // Lấy thông tin chi tiết của các tags
    let allTags: AdminTag[] = [];
    if (tagIds.length > 0) {
      allTags = await this.adminTagRepository.find({
        where: { id: In(tagIds) },
      });
    }

    // Tạo map để dễ dàng tìm tags theo audience ID
    const audienceTagsMap = new Map<number, AdminTag[]>();
    for (const audienceHasTag of audienceHasTags) {
      const tag = allTags.find(t => t.id === audienceHasTag.tagId);
      if (tag) {
        if (!audienceTagsMap.has(audienceHasTag.audienceId)) {
          audienceTagsMap.set(audienceHasTag.audienceId, []);
        }
        audienceTagsMap.get(audienceHasTag.audienceId)!.push(tag);
      }
    }

    // Chuyển đổi kết quả thành DTO
    const data: AudienceResponseDto[] = [];
    for (const audience of audiences) {
      const audienceCustomFields = customFields.filter(
        (cf) => cf.audienceId === audience.id,
      );
      const audienceTags = audienceTagsMap.get(audience.id) || [];
      data.push(this.mapToDto(audience, audienceCustomFields, audienceTags));
    }

    // Tạo thông tin phân trang
    const totalPages = Math.ceil(total / limit);
    const meta: PaginationMetaDto = {
      total,
      page,
      limit,
      totalPages,
      hasPreviousPage: page > 1,
      hasNextPage: page < totalPages,
    };

    return {
      data,
      meta,
    };
  }

  /**
   * Lấy audience theo ID
   * @param employeeId ID của employee
   * @param id ID của audience
   * @returns Audience
   */
  async findOne(id: number): Promise<AudienceResponseDto> {
    // Kiểm tra audience tồn tại
    const audience = await this.adminAudienceRepository.findOne({
      where: { id },
    });

    if (!audience) {
      throw new AppException(ErrorCode.AUDIENCE_NOT_FOUND, `Audience với ID ${id} không tồn tại`);
    }

    // Lấy các trường tùy chỉnh
    const customFields = await this.adminAudienceCustomFieldRepository.find({
      where: { audienceId: id },
    });

    // Lấy các tag của audience
    const audienceHasTags = await this.adminAudienceHasTagRepository.find({
      where: { audienceId: id },
    });

    // Lấy thông tin chi tiết của các tags
    let tags: AdminTag[] = [];
    if (audienceHasTags.length > 0) {
      const tagIds = audienceHasTags.map(aht => aht.tagId);
      tags = await this.adminTagRepository.find({
        where: { id: In(tagIds) },
      });
    }

    return this.mapToDto(audience, customFields, tags);
  }

  /**
   * Xóa audience
   * @param employeeId ID của employee
   * @param id ID của audience
   * @returns true nếu xóa thành công
   */
  @Transactional()
  async remove(id: number): Promise<boolean> {
    // Kiểm tra audience tồn tại
    const audience = await this.adminAudienceRepository.findOne({
      where: { id },
    });

    if (!audience) {
      throw new AppException(ErrorCode.AUDIENCE_NOT_FOUND);
    }

    // Xóa các trường tùy chỉnh
    await this.adminAudienceCustomFieldRepository.delete({ audienceId: id });

    // Xóa các liên kết với tags
    await this.adminAudienceHasTagRepository.deleteByAudienceId(id);

    // Xóa audience
    await this.adminAudienceRepository.remove(audience);

    return true;
  }

  /**
   * Xóa nhiều audience
   * @param ids Danh sách ID audience cần xóa
   * @returns Kết quả xóa nhiều
   */
  @Transactional()
  async bulkDelete(ids: number[]): Promise<BulkDeleteResponseDto> {
    const deletedIds: number[] = [];
    const failedIds: number[] = [];

    for (const id of ids) {
      try {
        const audience = await this.adminAudienceRepository.findOne({ where: { id } });
        if (!audience) {
          failedIds.push(id);
          continue;
        }

        // Xóa các trường tùy chỉnh
        await this.adminAudienceCustomFieldRepository.delete({ audienceId: id });

        // Xóa các liên kết với tags
        await this.adminAudienceHasTagRepository.deleteByAudienceId(id);

        // Xóa audience
        await this.adminAudienceRepository.remove(audience);
        deletedIds.push(id);
      } catch (error) {
        failedIds.push(id);
      }
    }

    const deletedCount = deletedIds.length;
    const failedCount = failedIds.length;
    const message = failedCount > 0
      ? `Đã xóa ${deletedCount} audience thành công, ${failedCount} audience không thể xóa`
      : `Đã xóa ${deletedCount} audience thành công`;

    return {
      deletedCount,
      failedCount,
      deletedIds,
      failedIds,
      message,
    };
  }

  /**
   * Chuyển đổi từ entity sang DTO
   * @param audience Audience entity
   * @param customFields Các trường tùy chỉnh
   * @param tags Các tag
   * @returns Audience DTO
   */
  private mapToDto(
    audience: AdminAudience,
    customFields: AdminAudienceCustomField[],
    tags: AdminTag[],
  ): AudienceResponseDto {
    const dto = new AudienceResponseDto();
    dto.id = audience.id;
    dto.employeeId = 0; // TODO: Cần thêm employeeId vào AdminAudience entity
    dto.name = audience.name;
    dto.email = audience.email;
    dto.phone = audience.phone;

    // Chuyển đổi avatar S3 key thành CDN URL
    let avatarUrl: string | null = audience.avatar;
    if (audience.avatar) {
      try {
        const cdnUrl = this.cdnService.generateUrlView(audience.avatar, TimeIntervalEnum.ONE_DAY);
        avatarUrl = cdnUrl || audience.avatar; // Fallback to original key if CDN fails
      } catch (error) {
        console.warn(`Không thể tạo URL CDN cho avatar audience ${audience.id}: ${error.message}`);
        avatarUrl = audience.avatar; // Keep original key as fallback
      }
    }
    dto.avatar = avatarUrl;

    dto.createdAt = audience.createdAt;
    dto.updatedAt = audience.updatedAt;

    // Chuyển đổi các trường tùy chỉnh
    dto.customFields = customFields.map((field) => {
      const fieldDto = new CustomFieldResponseDto();
      fieldDto.id = field.id;
      fieldDto.audienceId = field.audienceId;
      fieldDto.fieldName = field.fieldName;
      fieldDto.fieldValue = field.fieldValue;
      fieldDto.fieldType = field.fieldType as any;
      fieldDto.createdAt = field.createdAt;
      fieldDto.updatedAt = field.updatedAt;
      return fieldDto;
    });

    // Chuyển đổi các tag
    dto.tags = tags.map((tag) => {
      const tagDto = new TagResponseDto();
      tagDto.id = tag.id;
      tagDto.name = tag.name;
      tagDto.color = tag.color;
      tagDto.createdBy = tag.createdBy;
      tagDto.updatedBy = tag.updatedBy;
      tagDto.createdAt = tag.createdAt;
      tagDto.updatedAt = tag.updatedAt;
      return tagDto;
    });

    return dto;
  }

  /**
   * Tạo presigned URL để upload avatar
   * @param id ID của audience
   * @param createAvatarUploadUrlDto Dữ liệu tạo URL upload
   * @returns Thông tin URL upload
   */
  async createAvatarUploadUrl(
    id: number,
    createAvatarUploadUrlDto: CreateAvatarUploadUrlDto,
  ): Promise<AvatarUploadUrlResponseDto> {
    // Kiểm tra audience tồn tại
    const audience = await this.adminAudienceRepository.findOne({
      where: { id },
    });

    if (!audience) {
      throw new AppException(ErrorCode.AUDIENCE_NOT_FOUND, `Audience với ID ${id} không tồn tại`);
    }

    // Tạo S3 key cho avatar
    const s3Key = generateS3Key({
      baseFolder: 'marketing',
      categoryFolder: CategoryFolderEnum.CUSTOMER_AVATAR,
      useTimeFolder: true,
      prefix: `admin`,
      fileName: `avatar.${createAvatarUploadUrlDto.mediaType.split('_')[1].toLowerCase()}`,
    });

    // Tạo presigned URL
    const uploadUrl = await this.s3Service.createPresignedWithID(
      s3Key,
      TimeIntervalEnum.ONE_HOUR, // URL hết hạn sau 1 giờ
      createAvatarUploadUrlDto.mediaType as any, // Cast to MediaType
      FileSizeEnum.FIVE_MB, // Giới hạn 5MB cho avatar
    );

    // Tính thời gian hết hạn
    const expiresAt = Math.floor(Date.now() / 1000) + (60 * 60); // 1 giờ

    return {
      uploadUrl,
      s3Key,
      expiresAt,
    };
  }

  /**
   * Cập nhật avatar sau khi upload thành công
   * @param id ID của audience
   * @param updateAvatarDto Dữ liệu cập nhật avatar
   * @returns Audience đã cập nhật
   */
  async updateAvatar(
    id: number,
    updateAvatarDto: UpdateAvatarDto,
  ): Promise<AudienceResponseDto> {
    // Kiểm tra audience tồn tại
    const audience = await this.adminAudienceRepository.findOne({
      where: { id },
    });

    if (!audience) {
      throw new AppException(ErrorCode.AUDIENCE_NOT_FOUND, `Audience với ID ${id} không tồn tại`);
    }

    // Cập nhật avatar
    audience.avatar = updateAvatarDto.s3Key;
    audience.updatedAt = Math.floor(Date.now() / 1000);

    // Lưu audience
    const updatedAudience = await this.adminAudienceRepository.save(audience);

    // Lấy các trường tùy chỉnh
    const customFields = await this.adminAudienceCustomFieldRepository.find({
      where: { audienceId: id },
    });

    // Lấy các tag của audience
    const audienceHasTags = await this.adminAudienceHasTagRepository.find({
      where: { audienceId: id },
    });

    // Lấy thông tin chi tiết của các tags
    let tags: AdminTag[] = [];
    if (audienceHasTags.length > 0) {
      const tagIds = audienceHasTags.map(aht => aht.tagId);
      tags = await this.adminTagRepository.find({
        where: { id: In(tagIds) },
      });
    }

    return this.mapToDto(updatedAudience as AdminAudience, customFields, tags);
  }

  /**
   * Xóa avatar của audience
   * @param id ID của audience
   * @returns Audience đã cập nhật
   */
  async removeAvatar(id: number): Promise<AudienceResponseDto> {
    // Kiểm tra audience tồn tại
    const audience = await this.adminAudienceRepository.findOne({
      where: { id },
    });

    if (!audience) {
      throw new AppException(ErrorCode.AUDIENCE_NOT_FOUND, `Audience với ID ${id} không tồn tại`);
    }

    // Xóa avatar
    audience.avatar = null;
    audience.updatedAt = Math.floor(Date.now() / 1000);

    // Lưu audience
    const updatedAudience = await this.adminAudienceRepository.save(audience);

    // Lấy các trường tùy chỉnh
    const customFields = await this.adminAudienceCustomFieldRepository.find({
      where: { audienceId: id },
    });

    // Lấy các tag của audience
    const audienceHasTags = await this.adminAudienceHasTagRepository.find({
      where: { audienceId: id },
    });

    // Lấy thông tin chi tiết của các tags
    let tags: AdminTag[] = [];
    if (audienceHasTags.length > 0) {
      const tagIds = audienceHasTags.map(aht => aht.tagId);
      tags = await this.adminTagRepository.find({
        where: { id: In(tagIds) },
      });
    }

    return this.mapToDto(updatedAudience as AdminAudience, customFields, tags);
  }
}
