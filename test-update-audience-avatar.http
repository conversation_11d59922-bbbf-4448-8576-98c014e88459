### Test Update Audience API với Avatar Upload
### S3 key sẽ được tự động lưu vào database khi tạo URL upload

# 1. Test cập nhật audience không có avatar
PUT http://localhost:3000/v1/admin/marketing/audiences/1
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "name": "Nguyễn Văn A Updated",
  "email": "<EMAIL>",
  "phone": "+84987654321"
}

###

# 2. Test cập nhật audience với yêu cầu upload avatar
PUT http://localhost:3000/v1/admin/marketing/audiences/1
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "name": "Nguyễn Văn B",
  "email": "<EMAIL>", 
  "phone": "+84912345678",
  "avatarMediaType": "image/jpeg"
}

###

# 3. Test cập nhật audience với avatar PNG
PUT http://localhost:3000/v1/admin/marketing/audiences/1
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "name": "Nguyễn Văn C",
  "avatarMediaType": "image/png"
}

###

# 4. Test cập nhật audience với avatar WEBP
PUT http://localhost:3000/v1/admin/marketing/audiences/1
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "avatarMediaType": "image/webp"
}

###

# 5. Test với custom fields và avatar
PUT http://localhost:3000/v1/admin/marketing/audiences/1
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "name": "Nguyễn Văn D",
  "email": "<EMAIL>",
  "avatarMediaType": "image/gif",
  "customFields": [
    {
      "fieldName": "age",
      "fieldValue": "25",
      "fieldType": "number"
    },
    {
      "fieldName": "city",
      "fieldValue": "Ho Chi Minh",
      "fieldType": "text"
    }
  ],
  "tagIds": [1, 2]
}

###

# 6. Test với invalid avatar media type (should fail)
PUT http://localhost:3000/v1/admin/marketing/audiences/1
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "name": "Test Invalid",
  "avatarMediaType": "video/mp4"
}

###

# 7. Test kiểm tra audience sau khi cập nhật với avatar (để verify S3 key đã được lưu)
GET http://localhost:3000/v1/admin/marketing/audiences/1
Authorization: Bearer YOUR_JWT_TOKEN

###

# 8. Test upload file lên S3 (sử dụng avatarUploadUrl từ response của test #2)
# PUT {{avatarUploadUrl}}
# Content-Type: image/jpeg
#
# < path/to/your/image.jpg
