import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsEmail, IsEnum, IsNotEmpty, IsOptional, IsString, MinLength } from 'class-validator';
import { ImageTypeEnum } from '@shared/utils/file/image-media_type.util';

/**
 * DTO cho việc tạo nhân viên mới
 */
export class CreateEmployeeDto {
  /**
   * Tên đầy đủ của nhân viên
   */
  @ApiProperty({
    description: 'Tên đầy đủ của nhân viên',
    example: 'Nguyễn Văn A',
  })
  @IsNotEmpty({ message: 'Tên đầy đủ không được để trống' })
  @IsString({ message: 'Tên đầy đủ phải là chuỗi' })
  fullName: string;

  /**
   * Email nhân viên
   */
  @ApiProperty({
    description: 'Email nhân viên',
    example: '<EMAIL>',
  })
  @IsNotEmpty({ message: '<PERSON><PERSON> không được để trống' })
  @IsEmail({}, { message: '<PERSON>ail không hợp lệ' })
  email: string;

  /**
   * Số điện thoại nhân viên
   */
  @ApiProperty({
    description: 'Số điện thoại nhân viên',
    example: '0912345678',
  })
  @IsNotEmpty({ message: 'Số điện thoại không được để trống' })
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  phoneNumber: string;

  /**
   * Mật khẩu nhân viên
   */
  @ApiProperty({
    description: 'Mật khẩu nhân viên',
    example: 'Haianh123@123',
  })
  @IsNotEmpty({ message: 'Mật khẩu không được để trống' })
  @IsString({ message: 'Mật khẩu phải là chuỗi' })
  @MinLength(6, { message: 'Mật khẩu phải có ít nhất 6 ký tự' })
  password: string;

  /**
   * Địa chỉ nhân viên
   */
  @ApiProperty({
    description: 'Địa chỉ nhân viên',
    example: 'Số 1, Đường ABC, Quận XYZ, Hà Nội',
  })
  @IsNotEmpty({ message: 'Địa chỉ không được để trống' })
  @IsString({ message: 'Địa chỉ phải là chuỗi' })
  address: string;

  /**
   * Trạng thái hoạt động của tài khoản
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động của tài khoản',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trạng thái phải là boolean' })
  enable?: boolean = true;

  /**
   * Danh sách ID của các role
   */
  @ApiProperty({
    description: 'Danh sách ID của các role',
    example: [1, 2],
    type: [Number],
  })
  @IsOptional()
  @IsArray({ message: 'Danh sách role phải là mảng' })
  roleIds?: number[];

  /**
   * Loại hình ảnh avatar (nếu muốn tạo URL tạm thời để upload avatar)
   */
  @ApiPropertyOptional({
    description: 'Loại hình ảnh avatar (nếu muốn tạo URL tạm thời để upload avatar)',
    enum: ImageTypeEnum,
    example: 'image/jpeg',
  })
  @IsOptional()
  @IsEnum(ImageTypeEnum, { message: 'Loại hình ảnh không hợp lệ' })
  avatarImageType?: ImageTypeEnum;

  /**
   * Kích thước tối đa của file avatar (bytes)
   */
  @ApiPropertyOptional({
    description: 'Kích thước tối đa của file avatar (bytes)',
    example: 2097152, // 2MB
  })
  @IsOptional()
  avatarMaxSize?: number;
}
