import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsNumber,
  IsArray,
  ValidateNested,
  IsObject,
  IsEnum,
  IsDate,
  MaxLength,
  IsUrl,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import {
  PriceTypeEnum,
  EntityStatusEnum,
  ProductTypeEnum,
  ParticipationTypeEnum
} from '@modules/business/enums';
import { CustomFieldInputDto } from '../custom-field-metadata.dto';
import { HasPriceDto, StringPriceDto } from '../price.dto';
import { EventProductTicketOperationDto } from './event-product-ticket-operation.dto';

/**
 * DTO cho thông tin cơ bản sản phẩm
 */
export class BasicInfoDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Hội thảo Marketing Digital 2024',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  @ApiProperty({
    description: '<PERSON><PERSON> tả sản phẩm',
    example: '<PERSON>ội thảo về xu hướng marketing digital mới nhất năm 2024',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.EVENT,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductTypeEnum)
  productType?: ProductTypeEnum;

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: [String],
    example: ['hội thảo', 'marketing', 'digital', '2024'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  // ❌ REMOVED: Không cho phép user set status, mặc định sẽ là PENDING
  // @ApiProperty({
  //   description: 'Trạng thái sản phẩm',
  //   enum: EntityStatusEnum,
  //   example: EntityStatusEnum.PENDING,
  //   required: false,
  // })
  // @IsOptional()
  // @IsEnum(EntityStatusEnum)
  // status?: EntityStatusEnum;
}

/**
 * DTO cho thông tin giá cả
 */
export class PricingInfoDto {
  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    oneOf: [
      { $ref: '#/components/schemas/HasPriceDto' },
      { $ref: '#/components/schemas/StringPriceDto' }
    ],
    example: {
      listPrice: 500000,
      salePrice: 450000,
      currency: 'VND'
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  price?: HasPriceDto | StringPriceDto;

  @ApiProperty({
    description: 'Kiểu giá sản phẩm',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
  })
  @IsOptional()
  @IsEnum(PriceTypeEnum)
  typePrice?: PriceTypeEnum;
}

/**
 * DTO cho thông tin sự kiện
 */
export class EventInfoDto {
  @ApiProperty({
    description: 'Hình thức tham gia sự kiện',
    example: ParticipationTypeEnum.ONLINE,
    enum: ParticipationTypeEnum,
    enumName: 'ParticipationTypeEnum',
    examples: {
      online: { value: ParticipationTypeEnum.ONLINE, description: 'Sự kiện trực tuyến' },
      offline: { value: ParticipationTypeEnum.OFFLINE, description: 'Sự kiện trực tiếp' }
    },
    required: false,
  })
  @IsOptional()
  @IsEnum(ParticipationTypeEnum)
  participationType?: ParticipationTypeEnum;

  @ApiProperty({
    description: 'Địa điểm tham gia sự kiện (nếu offline hoặc hybrid)',
    example: 'Trung tâm Hội nghị Quốc gia, Hà Nội',
    required: false,
  })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiProperty({
    description: 'Đường dẫn tham gia sự kiện (nếu online hoặc hybrid)',
    example: 'https://zoom.us/j/123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsUrl()
  participationUrl?: string;

  @ApiProperty({
    description: 'Thời gian bắt đầu sự kiện (epoch milliseconds)',
    example: 1704067200000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  startDate?: number;

  @ApiProperty({
    description: 'Ngày và giờ kết thúc sự kiện kèm múi giờ',
    example: '2024-01-15T18:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDate()
  @Transform(({ value }) => value ? new Date(value) : undefined)
  endDate?: Date;

  @ApiProperty({
    description: 'Tên múi giờ chuẩn',
    example: 'Asia/Ho_Chi_Minh',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  timeZone?: string;
}

/**
 * DTO cho thao tác với ảnh sản phẩm
 */
export class ImageOperationDto {
  @ApiProperty({
    description: 'Loại thao tác với ảnh',
    enum: ['add', 'delete'],
    example: 'add',
  })
  @IsEnum(['add', 'delete'])
  operation: 'add' | 'delete';

  @ApiProperty({
    description: 'ID media (cho thao tác add)',
    example: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2',
    required: false,
  })
  @IsOptional()
  @IsString()
  mediaId?: string;

  @ApiProperty({
    description: 'ID của entity_has_media record cần xóa (cho thao tác delete)',
    example: 123,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  entityHasMediaId?: number;
}

/**
 * DTO cho các thao tác với sự kiện
 */
export class EventOperationsDto {
  @ApiProperty({
    description: 'Danh sách thao tác với vé sự kiện (ADD/UPDATE/DELETE)',
    type: [EventProductTicketOperationDto],
    example: [
      {
        operation: 'add',
        data: {
          name: 'Vé VIP',
          price: 500000,
          totalQuantity: 50,
          description: 'Vé VIP bao gồm chỗ ngồi hạng nhất và buffet',
          saleStart: 1704067200000,
          saleEnd: 1704153600000,
          timeZone: 'Asia/Ho_Chi_Minh',
          sku: 'TICKET-VIP-001',
          minQuantityPerOrder: 1,
          maxQuantityPerOrder: 10,
          imageOperations: [
            {
              operation: 'add',
              mediaId: 'dcc8f803-04fc-49b3-953a-e660505ac353',
            }
          ]
        }
      },
      {
        operation: 'update',
        id: 123,
        data: {
          name: 'Vé VIP - Cập nhật',
          price: 450000,
          imageOperations: [
            {
              operation: 'delete',
              entityHasMediaId: 456
            }
          ]
        }
      },
      {
        operation: 'delete',
        id: 124
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EventProductTicketOperationDto)
  tickets?: EventProductTicketOperationDto[];

  @ApiProperty({
    description: 'Danh sách thao tác với ảnh sản phẩm (ADD/DELETE)',
    type: [ImageOperationDto],
    example: [
      {
        operation: 'add',
        mediaId: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2',
      },
      {
        operation: 'delete',
        entityHasMediaId: 789
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ImageOperationDto)
  images?: ImageOperationDto[];
}

/**
 * DTO hoàn chỉnh cho việc cập nhật sản phẩm sự kiện
 * Sử dụng cấu trúc nhóm theo chức năng để dễ đọc và maintain
 * Bao gồm tất cả thuộc tính từ:
 * - customer_products table (thông qua basicInfo và pricing)
 * - event_products table (thông qua eventInfo)
 * - event_product_tickets table (thông qua operations.tickets)
 * - entity_has_media table (thông qua operations.images)
 */
export class CompleteUpdateEventProductDto {
  // ========== THÔNG TIN CƠ BẢN ==========
  @ApiProperty({
    description: 'Thông tin cơ bản sản phẩm (tên, mô tả, loại, tags)',
    type: BasicInfoDto,
    example: {
      name: 'Hội thảo Marketing Digital 2024 - Phiên bản mới',
      description: 'Hội thảo về xu hướng marketing digital mới nhất năm 2024 với các chuyên gia hàng đầu',
      productType: 'EVENT',
      tags: ['hội thảo', 'marketing', 'digital', '2024']
      // ❌ REMOVED: Không có status - sẽ mặc định PENDING
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => BasicInfoDto)
  basicInfo?: BasicInfoDto;

  // ========== THÔNG TIN GIÁ CẢ ==========
  @ApiProperty({
    description: 'Thông tin giá cả sản phẩm',
    type: PricingInfoDto,
    examples: {
      hasPrice: {
        summary: 'Sản phẩm có giá cụ thể',
        value: {
          price: {
            listPrice: 500000,
            salePrice: 450000,
            currency: 'VND'
          },
          typePrice: 'HAS_PRICE'
        }
      },
      stringPrice: {
        summary: 'Sản phẩm có mô tả giá',
        value: {
          price: {
            priceDescription: 'Giá sẽ được công bố sau'
          },
          typePrice: 'STRING_PRICE'
        }
      }
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PricingInfoDto)
  pricing?: PricingInfoDto;

  // ========== THÔNG TIN SỰ KIỆN ==========
  @ApiProperty({
    description: 'Thông tin sự kiện (hình thức, địa điểm, thời gian)',
    type: EventInfoDto,
    examples: {
      online: {
        summary: 'Sự kiện trực tuyến',
        value: {
          participationType: 'online',
          participationUrl: 'https://zoom.us/j/123456789',
          startDate: 1704067200000,
          endDate: '2024-01-15T18:00:00.000Z',
          timeZone: 'Asia/Ho_Chi_Minh'
        }
      },
      offline: {
        summary: 'Sự kiện trực tiếp',
        value: {
          participationType: 'offline',
          location: 'Trung tâm Hội nghị Quốc gia, Hà Nội',
          startDate: 1704067200000,
          endDate: '2024-01-15T18:00:00.000Z',
          timeZone: 'Asia/Ho_Chi_Minh'
        }
      }
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => EventInfoDto)
  eventInfo?: EventInfoDto;

  // ========== CUSTOM FIELDS ==========
  @ApiProperty({
    description: 'Danh sách custom fields',
    type: [CustomFieldInputDto],
    example: [
      {
        customFieldId: 1,
        value: { value: 'Hội thảo chuyên sâu' }
      },
      {
        customFieldId: 2,
        value: { value: 'Cấp độ nâng cao' }
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];

  // ========== THAO TÁC ==========
  @ApiProperty({
    description: 'Các thao tác với vé và hình ảnh sự kiện',
    type: EventOperationsDto,
    example: {
      tickets: [
        {
          operation: 'add',
          data: {
            name: 'Vé VIP',
            price: 500000,
            totalQuantity: 50,
            description: 'Vé VIP bao gồm chỗ ngồi hạng nhất và buffet',
            saleStart: 1704067200000,
            saleEnd: 1704153600000,
            timeZone: 'Asia/Ho_Chi_Minh',
            sku: 'TICKET-VIP-001',
            minQuantityPerOrder: 1,
            maxQuantityPerOrder: 10,
            imageOperations: [
              {
                operation: 'add',
                mediaId: 'dcc8f803-04fc-49b3-953a-e660505ac353'
              }
            ]
          }
        },
        {
          operation: 'update',
          id: 123,
          data: {
            name: 'Vé VIP - Cập nhật',
            price: 450000,
            imageOperations: [
              {
                operation: 'delete',
                entityHasMediaId: 456
              }
            ]
          }
        },
        {
          operation: 'delete',
          id: 124
        }
      ],
      images: [
        {
          operation: 'add',
          mediaId: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2'
        },
        {
          operation: 'delete',
          entityHasMediaId: 789
        }
      ]
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => EventOperationsDto)
  operations?: EventOperationsDto;
}
