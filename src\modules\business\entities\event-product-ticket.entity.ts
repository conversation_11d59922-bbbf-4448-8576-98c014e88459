import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng event_product_tickets trong cơ sở dữ liệu
 * Thông tin các loại vé được mở bán cho sự kiện (event_products)
 */
@Entity('event_product_tickets')
export class EventProductTicket {
  /**
   * ID tự tăng của vé sự kiện
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Khóa ngoại liên kết đến bảng event_products
   */
  @Column({
    name: 'event_product_id',
    type: 'bigint',
    nullable: false,
    comment: 'Khóa ngoại liên kết đến bảng event_products',
  })
  eventProductId: number;

  /**
   * Tên loại vé
   */
  @Column({
    name: 'name',
    type: 'varchar',
    length: 255,
    nullable: false,
    comment: 'Tên loại vé',
  })
  name: string;

  /**
   * <PERSON><PERSON><PERSON> vé (VND)
   */
  @Column({
    name: 'price',
    type: 'numeric',
    precision: 12,
    scale: 2,
    nullable: false,
    comment: 'Giá vé (VND)',
  })
  price: number;

  /**
   * Tổng số lượng vé được phát hành
   */
  @Column({
    name: 'total_quantity',
    type: 'integer',
    nullable: false,
    comment: 'Tổng số lượng vé được phát hành',
  })
  totalQuantity: number;

  /**
   * Mô tả chi tiết cho loại vé
   */
  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
    comment: 'Mô tả chi tiết cho loại vé',
  })
  description: string | null;

  /**
   * Thời gian bắt đầu mở bán
   */
  @Column({
    name: 'sale_start',
    type: 'bigint',
    nullable: false,
    comment: 'Thời gian bắt đầu mở bán (epoch milliseconds)',
  })
  saleStart: number;

  /**
   * Thời gian kết thúc bán vé
   */
  @Column({
    name: 'sale_end',
    type: 'bigint',
    nullable: false,
    comment: 'Thời gian kết thúc bán vé (epoch milliseconds)',
  })
  saleEnd: number;

  /**
   * Múi giờ áp dụng cho thời gian mở bán
   */
  @Column({
    name: 'time_zone',
    type: 'varchar',
    length: 100,
    nullable: false,
    comment: 'Múi giờ áp dụng cho thời gian mở bán',
  })
  timeZone: string;

  /**
   * Mã định danh SKU cho vé
   */
  @Column({
    name: 'sku',
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Mã định danh SKU cho vé',
  })
  sku: string | null;

  /**
   * Số lượng tối thiểu phải mua trong 1 lần đặt vé
   */
  @Column({
    name: 'min_quantity_per_order',
    type: 'integer',
    nullable: true,
    default: 1,
    comment: 'Số lượng tối thiểu phải mua trong 1 lần đặt vé',
  })
  minQuantityPerOrder: number | null;

  /**
   * Số lượng tối đa được phép mua trong 1 lần đặt vé
   */
  @Column({
    name: 'max_quantity_per_order',
    type: 'integer',
    nullable: true,
    comment: 'Số lượng tối đa được phép mua trong 1 lần đặt vé',
  })
  maxQuantityPerOrder: number | null;
}
