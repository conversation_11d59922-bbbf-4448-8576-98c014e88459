import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho hiệu suất chiến dịch email cụ thể
 */
export class EmailCampaignPerformanceDto {
  /**
   * ID chiến dịch
   * @example 1
   */
  @ApiProperty({
    description: 'ID chiến dịch',
    example: 1,
  })
  campaignId: number;

  /**
   * Tên chiến dịch
   * @example "Khuyến mãi Black Friday 2024"
   */
  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Khuyến mãi Black Friday 2024',
  })
  campaignName: string;

  /**
   * Tỷ lệ mở email (%)
   * @example 50.0
   */
  @ApiProperty({
    description: 'Tỷ lệ mở email (%)',
    example: 50.0,
  })
  openRate: number;

  /**
   * Tỷ lệ click email (%)
   * @example 20.0
   */
  @ApiProperty({
    description: 'Tỷ lệ click email (%)',
    example: 20.0,
  })
  clickRate: number;

  /**
   * Tỷ lệ bounce email (%)
   * @example 3.1
   */
  @ApiProperty({
    description: 'Tỷ lệ bounce email (%)',
    example: 3.1,
  })
  bounceRate: number;

  /**
   * Tỷ lệ hủy đăng ký (%)
   * @example 1.6
   */
  @ApiProperty({
    description: 'Tỷ lệ hủy đăng ký (%)',
    example: 1.6,
  })
  unsubscribeRate: number;

  /**
   * Tỷ lệ gửi thành công (%)
   * @example 93.8
   */
  @ApiProperty({
    description: 'Tỷ lệ gửi thành công (%)',
    example: 93.8,
  })
  deliveryRate: number;

  /**
   * Tỷ lệ click-to-open (%)
   * @example 40.0
   */
  @ApiProperty({
    description: 'Tỷ lệ click-to-open (%)',
    example: 40.0,
  })
  clickToOpenRate: number;

  /**
   * Trạng thái chiến dịch
   * @example "SENT"
   */
  @ApiProperty({
    description: 'Trạng thái chiến dịch',
    example: 'SENT',
  })
  status: string;

  /**
   * Thời gian tạo chiến dịch (Unix timestamp)
   * @example 1640995200
   */
  @ApiProperty({
    description: 'Thời gian tạo chiến dịch (Unix timestamp)',
    example: 1640995200,
  })
  createdAt: number;

  /**
   * Thời gian chạy chiến dịch (Unix timestamp)
   * @example 1640995200
   */
  @ApiProperty({
    description: 'Thời gian chạy chiến dịch (Unix timestamp)',
    example: 1640995200,
    required: false,
  })
  runAt?: number;
}

/**
 * DTO cho response API hiệu suất chiến dịch
 */
export class EmailCampaignPerformanceResponseDto {
  /**
   * Thông tin hiệu suất chiến dịch
   */
  @ApiProperty({
    description: 'Thông tin hiệu suất chiến dịch',
    type: EmailCampaignPerformanceDto,
  })
  performance: EmailCampaignPerformanceDto;
}
