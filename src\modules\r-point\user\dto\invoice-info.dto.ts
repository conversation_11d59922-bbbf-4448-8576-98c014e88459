import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEmail, IsOptional, IsIn, ValidateIf } from 'class-validator';

/**
 * DTO cho thông tin hóa đơn
 */
export class InvoiceInfoDto {
  @ApiProperty({
    description: 'Loại hóa đơn',
    example: 'business',
    enum: ['business', 'personal'],
  })
  @IsString()
  @IsNotEmpty()
  @IsIn(['business', 'personal'], { message: 'Loại hóa đơn phải là business hoặc personal' })
  type: 'business' | 'personal';

  @ApiProperty({
    description: 'Tên người đại diện (bắt buộc cho hóa đơn doanh nghiệp)',
    example: 'Nguyễn Văn A',
    required: false,
  })
  @ValidateIf(o => o.type === 'business')
  @IsNotEmpty({ message: 'Tên người đại diện là bắt buộc cho hóa đơn doanh nghiệp' })
  @IsString()
  representativeName?: string;

  @ApiProperty({
    description: 'Chức vụ người đại di<PERSON>n (bắt buộc cho hóa đơn doanh nghiệp)',
    example: 'Giám đốc',
    required: false,
  })
  @ValidateIf(o => o.type === 'business')
  @IsNotEmpty({ message: 'Chức vụ người đại diện là bắt buộc cho hóa đơn doanh nghiệp' })
  @IsString()
  representativePosition?: string;

  @ApiProperty({
    description: 'Tên công ty (bắt buộc cho hóa đơn doanh nghiệp)',
    example: 'Công ty TNHH ABC',
    required: false,
  })
  @ValidateIf(o => o.type === 'business')
  @IsNotEmpty({ message: 'Tên công ty là bắt buộc cho hóa đơn doanh nghiệp' })
  @IsString()
  companyName?: string;

  @ApiProperty({
    description: 'Địa chỉ công ty (bắt buộc cho hóa đơn doanh nghiệp)',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    required: false,
  })
  @ValidateIf(o => o.type === 'business')
  @IsNotEmpty({ message: 'Địa chỉ công ty là bắt buộc cho hóa đơn doanh nghiệp' })
  @IsString()
  companyAddress?: string;

  @ApiProperty({
    description: 'Mã số thuế (bắt buộc cho hóa đơn doanh nghiệp)',
    example: '0123456789',
    required: false,
  })
  @ValidateIf(o => o.type === 'business')
  @IsNotEmpty({ message: 'Mã số thuế là bắt buộc cho hóa đơn doanh nghiệp' })
  @IsString()
  taxCode?: string;

  @ApiProperty({
    description: 'Email liên hệ',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email?: string;
}
