import { Transform } from 'class-transformer';

/**
 * Transform decorator để chuyển đổi string thành boolean
 * Sử dụng cho query parameters từ URL
 * 
 * @example
 * ```typescript
 * @IsOptional()
 * @IsBoolean()
 * @TransformStringToBoolean()
 * isActive?: boolean;
 * ```
 */
export function TransformStringToBoolean() {
  return Transform(({ value }) => {
    if (typeof value === 'boolean') {
      return value;
    }
    
    if (typeof value === 'string') {
      const lowerValue = value.toLowerCase();
      if (lowerValue === 'true') return true;
      if (lowerValue === 'false') return false;
    }
    
    // Trả về giá trị gốc nếu không thể transform
    // Để validation decorator xử lý
    return value;
  });
}

/**
 * Transform function để sử dụng trực tiếp với @Transform()
 * 
 * @example
 * ```typescript
 * @IsOptional()
 * @IsBoolean()
 * @Transform(transformStringToBoolean)
 * isActive?: boolean;
 * ```
 */
export const transformStringToBoolean = ({ value }: { value: any }) => {
  if (typeof value === 'boolean') {
    return value;
  }
  
  if (typeof value === 'string') {
    const lowerValue = value.toLowerCase();
    if (lowerValue === 'true') return true;
    if (lowerValue === 'false') return false;
  }
  
  return value;
};

/**
 * Transform decorator với validation nghiêm ngặt
 * Chỉ chấp nhận 'true', 'false', true, false
 * Throw error cho các giá trị khác
 * 
 * @example
 * ```typescript
 * @IsOptional()
 * @IsBoolean()
 * @TransformStringToBooleanStrict()
 * isActive?: boolean;
 * ```
 */
export function TransformStringToBooleanStrict() {
  return Transform(({ value }) => {
    if (typeof value === 'boolean') {
      return value;
    }
    
    if (typeof value === 'string') {
      const lowerValue = value.toLowerCase();
      if (lowerValue === 'true') return true;
      if (lowerValue === 'false') return false;
      
      // Throw error cho string không hợp lệ
      throw new Error(`Invalid boolean string: "${value}". Expected "true" or "false".`);
    }
    
    if (value === undefined || value === null) {
      return value;
    }
    
    // Throw error cho type không hợp lệ
    throw new Error(`Invalid boolean value: ${value}. Expected boolean or string "true"/"false".`);
  });
}
