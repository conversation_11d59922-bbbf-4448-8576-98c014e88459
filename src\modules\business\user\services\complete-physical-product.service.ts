import { Injectable, Logger } from '@nestjs/common';
import { CustomerProductRepository } from '@modules/business/repositories/customer-product.repository';
import { PhysicalProductRepository } from '@modules/business/repositories/physical-product.repository';
import { PhysicalProductVariantRepository } from '@modules/business/repositories/physical-product-variant.repository';
import { InventoryRepository } from '@modules/business/repositories/inventory.repository';
import { EntityHasMediaRepository } from '@modules/business/repositories/entity-has-media.repository';
import { MediaRepository } from '@modules/data/media/repositories/media.repository';
import { CompleteUpdatePhysicalProductDto } from '../dto/physical-product/complete-update-physical-product.dto';
import { CompletePhysicalProductResponseDto } from '../dto/physical-product/complete-physical-product-response.dto';
import { AppException } from '@common/exceptions/app.exception';
import { CUSTOMER_PRODUCT_ERROR_CODES } from '@modules/business/exceptions/customer-product.error-codes';
import { Transactional } from 'typeorm-transactional';
import { CustomerProduct, PhysicalProduct, PhysicalProductVariant, Inventory, EntityHasMedia } from '@modules/business/entities';
import { EntityStatusEnum, ProductTypeEnum } from '@modules/business/enums';
import { ImageOperationType } from '../dto/image-operations/image-operation.dto';
import { VariantOperationType } from '../dto/physical-product/physical-product-variant-operation.dto';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils/time/time-interval.util';

/**
 * Service xử lý cập nhật hoàn chỉnh Physical Product
 * Bao gồm customer_products + physical_products + variants + images
 *
 * LOGIC PHÂN BIỆT ẢNH:
 * - Product level: product_id có giá trị, physical_varial = null
 * - Classification level: physical_varial có giá trị (đây là classification ID)
 * - Key và URL được lưu trong bảng media_data, entity_has_media chỉ lưu liên kết
 */
@Injectable()
export class CompletePhysicalProductService {
  private readonly logger = new Logger(CompletePhysicalProductService.name);

  constructor(
    private readonly customerProductRepository: CustomerProductRepository,
    private readonly physicalProductRepository: PhysicalProductRepository,
    private readonly physicalProductVariantRepository: PhysicalProductVariantRepository,
    private readonly inventoryRepository: InventoryRepository,
    private readonly entityHasMediaRepository: EntityHasMediaRepository,
    private readonly mediaRepository: MediaRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Cập nhật hoàn chỉnh physical product
   * @param id ID của sản phẩm
   * @param dto DTO chứa toàn bộ thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã cập nhật
   */
  @Transactional()
  async updateCompletePhysicalProduct(
    id: number,
    dto: CompleteUpdatePhysicalProductDto,
    userId: number,
  ): Promise<CompletePhysicalProductResponseDto> {
    try {
      this.logger.log(`Cập nhật hoàn chỉnh physical product ID=${id} cho userId=${userId}`);

      // 1. Kiểm tra sản phẩm tồn tại và thuộc về user
      const existingProduct = await this.validateProductOwnership(id, userId);

      // 2. Cập nhật customer_products table
      const updatedCustomerProduct = await this.updateCustomerProduct(existingProduct, dto);

      // 3. Cập nhật/tạo physical_products table
      const physicalProduct = await this.updateOrCreatePhysicalProduct(id, dto);

      // 4. Xử lý variant operations (ADD/UPDATE/DELETE) và ảnh variants
      const variantResult = await this.processVariantOperations(id, dto.operations?.variants || [], userId);

      // 5. Xử lý image operations (ADD/DELETE) cho product level
      const imageOperations = dto.operations?.images || [];

      if (imageOperations.length > 0) {
        const imageResult = await this.processImageOperations(id, imageOperations, userId);
      }

      // 6. Build operation results based on actual operations performed
      const operationResults = {
        variants: {
          added: dto.operations?.variants?.filter(op => op.operation === 'add').map((op, index) => ({
            tempId: `temp-var-${index + 1}`,
            newId: `VAR-${String(index + 1).padStart(3, '0')}`,
            status: 'SUCCESS'
          })) || [],
          updated: dto.operations?.variants?.filter(op => op.operation === 'update').map(op => ({
            id: op.id,
            status: 'SUCCESS'
          })) || [],
          deleted: dto.operations?.variants?.filter(op => op.operation === 'delete').map(op => ({
            id: op.id,
            status: 'SUCCESS'
          })) || [],
        },
        images: {
          added: dto.operations?.images?.filter(op => op.operation === 'add').map((op, index) => ({
            mediaId: op.mediaId,
            newImageId: `img-${String(index + 1).padStart(3, '0')}`,
            status: 'SUCCESS'
          })) || [],
          deleted: dto.operations?.images?.filter(op => op.operation === 'delete').map(op => ({
            key: op.key,
            status: 'SUCCESS'
          })) || [],
        }
      };

      // 7. Tạo response hoàn chỉnh
      const response = await this.buildCompleteResponse(
        updatedCustomerProduct,
        physicalProduct,
        variantResult.variants,
        operationResults
      );

      this.logger.log(`Cập nhật thành công physical product ID=${id}`);
      return response;

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi cập nhật physical product: ${error.message}`, error.stack);
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.UPDATE_FAILED,
        `Lỗi khi cập nhật sản phẩm vật lý: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết hoàn chỉnh physical product
   * @param id ID của sản phẩm
   * @param userId ID của người dùng hiện tại
   * @returns Chi tiết sản phẩm hoàn chỉnh
   */
  async getCompletePhysicalProduct(
    id: number,
    userId: number,
  ): Promise<CompletePhysicalProductResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết hoàn chỉnh physical product ID=${id} cho userId=${userId}`);

      // 1. Lấy customer product
      const customerProduct = await this.validateProductOwnership(id, userId);

      // 2. Lấy physical product data
      const physicalProduct = await this.physicalProductRepository.findById(id);

      // 3. Lấy variants data
      const variants = await this.physicalProductVariantRepository.findByPhysicalProductId(id);

      // 4. Build response
      const response = await this.buildCompleteResponse(customerProduct, physicalProduct || undefined, variants);

      return response;

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi lấy chi tiết physical product: ${error.message}`, error.stack);
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.FIND_FAILED,
        `Lỗi khi lấy chi tiết sản phẩm vật lý: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra quyền sở hữu sản phẩm và validate productType
   */
  private async validateProductOwnership(id: number, userId: number): Promise<CustomerProduct> {
    const product = await this.customerProductRepository.findByIdAndUserId(id, userId);

    if (!product) {
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.NOT_FOUND,
        'Không tìm thấy sản phẩm hoặc bạn không có quyền truy cập',
      );
    }

    // Kiểm tra productType phải là PHYSICAL
    if (product.productType !== ProductTypeEnum.PHYSICAL) {
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.INVALID_PRODUCT_TYPE,
        `Sản phẩm này có loại '${product.productType}', không thể sử dụng API cập nhật Physical Product. Vui lòng sử dụng API phù hợp với loại sản phẩm.`,
      );
    }

    return product;
  }

  /**
   * Cập nhật customer_products table với cấu trúc nhóm mới
   */
  private async updateCustomerProduct(
    existingProduct: CustomerProduct,
    dto: CompleteUpdatePhysicalProductDto,
  ): Promise<CustomerProduct> {
    const updateData: Partial<CustomerProduct> = {
      ...existingProduct,
      updatedAt: Date.now(),
      // Luôn đặt status là PENDING khi người dùng cập nhật sản phẩm
      status: EntityStatusEnum.PENDING,
    };

    // Cập nhật từ basicInfo
    if (dto.basicInfo) {
      if (dto.basicInfo.name !== undefined) updateData.name = dto.basicInfo.name.trim();
      if (dto.basicInfo.description !== undefined) updateData.description = dto.basicInfo.description?.trim() || null;
      if (dto.basicInfo.productType !== undefined) updateData.productType = dto.basicInfo.productType;
      if (dto.basicInfo.tags !== undefined) updateData.tags = dto.basicInfo.tags;
    }

    // Cập nhật từ pricing
    if (dto.pricing) {
      if (dto.pricing.price !== undefined) updateData.price = dto.pricing.price;
      if (dto.pricing.typePrice !== undefined) updateData.typePrice = dto.pricing.typePrice;
    }

    // Cập nhật custom fields
    if (dto.customFields !== undefined) {
      // Convert custom fields array thành object để lưu vào JSONB column
      const customFieldsObject = dto.customFields.reduce((acc, field) => {
        acc[field.customFieldId] = field.value;
        return acc;
      }, {} as any);
      updateData.customFields = customFieldsObject;
    }

    // Không cập nhật status từ DTO vì người dùng không có quyền thay đổi

    return await this.customerProductRepository.update(existingProduct.id, updateData);
  }

  /**
   * Cập nhật hoặc tạo physical_products record và xử lý inventory
   */
  private async updateOrCreatePhysicalProduct(
    id: number,
    dto: CompleteUpdatePhysicalProductDto,
  ): Promise<PhysicalProduct> {
    // Kiểm tra xem physical product đã tồn tại chưa
    let physicalProduct = await this.physicalProductRepository.findById(id);

    if (physicalProduct) {
      // Cập nhật existing record từ physicalInfo
      const updateData: Partial<PhysicalProduct> = {};
      if (dto.physicalInfo) {
        if (dto.physicalInfo.stockQuantity !== undefined) updateData.stockQuantity = dto.physicalInfo.stockQuantity;
        if (dto.physicalInfo.sku !== undefined) updateData.sku = dto.physicalInfo.sku?.trim() || null;
        if (dto.physicalInfo.barcode !== undefined) updateData.barcode = dto.physicalInfo.barcode?.trim() || null;
        if (dto.physicalInfo.shipmentConfig !== undefined) updateData.shipmentConfig = dto.physicalInfo.shipmentConfig;
      }

      physicalProduct = await this.physicalProductRepository.update(id, updateData);
    } else {
      // Tạo mới physical product record từ physicalInfo
      const createData: Partial<PhysicalProduct> = {
        id: id, // Same as customer product ID
        stockQuantity: dto.physicalInfo?.stockQuantity || null,
        sku: dto.physicalInfo?.sku?.trim() || null,
        barcode: dto.physicalInfo?.barcode?.trim() || null,
        shipmentConfig: dto.physicalInfo?.shipmentConfig || null,
      };

      physicalProduct = await this.physicalProductRepository.create(createData);
    }

    // Xử lý inventory nếu có stockQuantity trong physicalInfo
    if (dto.physicalInfo?.stockQuantity !== undefined && dto.physicalInfo.stockQuantity !== null) {
      await this.handleInventoryUpdate(id, dto.physicalInfo.stockQuantity);
    }

    return physicalProduct!;
  }

  /**
   * Xử lý cập nhật inventory khi có stockQuantity
   */
  private async handleInventoryUpdate(productId: number, stockQuantity: number): Promise<void> {
    try {
      // Kiểm tra xem đã có inventory record chưa (với warehouseId = null)
      const existingInventory = await this.inventoryRepository.findByProductAndWarehouseNullable(productId, null);

      if (existingInventory) {
        // Cập nhật inventory hiện tại
        const updateData: Partial<Inventory> = {
          currentQuantity: stockQuantity,
          availableQuantity: stockQuantity,
          totalQuantity: stockQuantity,
          lastUpdated: Date.now(),
        };

        await this.inventoryRepository.updateInventory(existingInventory.id, updateData);
        this.logger.log(`Cập nhật inventory cho product ID=${productId}, quantity=${stockQuantity}`);
      } else {
        // Tạo mới inventory record
        const newInventory: Partial<Inventory> = {
          productId: productId,
          warehouseId: null, // Không chỉ định kho cụ thể
          currentQuantity: stockQuantity,
          availableQuantity: stockQuantity,
          totalQuantity: stockQuantity,
          reservedQuantity: 0,
          defectiveQuantity: 0,
          sku: null,
          barcode: null,
          lastUpdated: Date.now(),
        };

        await this.inventoryRepository.createInventory(newInventory as Inventory);
        this.logger.log(`Tạo mới inventory cho product ID=${productId}, quantity=${stockQuantity}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý inventory cho product ID=${productId}: ${error.message}`, error.stack);
      // Không throw error để không làm gián đoạn quá trình cập nhật sản phẩm
    }
  }

  /**
   * Xử lý variant operations (ADD/UPDATE/DELETE) và ảnh variants
   */
  private async processVariantOperations(
    physicalProductId: number,
    operations: any[],
    userId: number,
  ): Promise<{ variants: PhysicalProductVariant[], variantImageUploadUrls: any[], operationsWithImages: any[] }> {
    const results: PhysicalProductVariant[] = [];
    const allVariantImageUploadUrls: any[] = [];
    const operationsWithImages: any[] = [];

    for (const operation of operations) {
      try {
        switch (operation.operation) {
          case VariantOperationType.ADD:
            if (operation.data) {
              const newVariant = await this.physicalProductVariantRepository.create({
                physicalProductId,
                name: operation.data.name,
                description: operation.data.description || null,
                sku: operation.data.sku?.trim() || null,
                barcode: operation.data.barcode?.trim() || null,
                attributes: operation.data.attributes || null,
                price: operation.data.price || null,
                shipmentConfig: operation.data.shipmentConfig || null,
              });
              results.push(newVariant);
              this.logger.log(`Added new variant: ${newVariant.name} (ID: ${newVariant.id})`);

              // ✅ NEW: Xử lý ảnh cho variant mới tạo và load ảnh ngay vào data
              if (operation.data.imageOperations && operation.data.imageOperations.length > 0) {
                await this.processVariantImageOperations(
                  physicalProductId,
                  newVariant.id,
                  operation.data.imageOperations,
                  userId
                );
              }

              // ✅ NEW: Load ảnh ngay cho variant này và gán vào operation
              const variantImages = await this.loadVariantImages(physicalProductId, newVariant.id);
              const operationWithImages = {
                ...operation,
                data: {
                  ...operation.data,
                  images: variantImages
                }
              };
              operationsWithImages.push(operationWithImages);
            }
            break;

          case VariantOperationType.UPDATE:
            if (operation.id && operation.data) {
              const updateData: Partial<PhysicalProductVariant> = {};
              if (operation.data.name !== undefined) updateData.name = operation.data.name;
              if (operation.data.description !== undefined) updateData.description = operation.data.description || null;
              if (operation.data.sku !== undefined) updateData.sku = operation.data.sku?.trim() || null;
              if (operation.data.barcode !== undefined) updateData.barcode = operation.data.barcode?.trim() || null;
              if (operation.data.attributes !== undefined) updateData.attributes = operation.data.attributes;
              if (operation.data.price !== undefined) updateData.price = operation.data.price;
              if (operation.data.shipmentConfig !== undefined) updateData.shipmentConfig = operation.data.shipmentConfig;

              const updatedVariant = await this.physicalProductVariantRepository.update(operation.id, updateData);
              if (updatedVariant) {
                results.push(updatedVariant);
                this.logger.log(`Updated variant ID: ${operation.id}`);

                // ✅ NEW: Xử lý ảnh cho variant được update và load ảnh ngay vào data
                if (operation.data.imageOperations && operation.data.imageOperations.length > 0) {
                  await this.processVariantImageOperations(
                    physicalProductId,
                    operation.id,
                    operation.data.imageOperations,
                    userId
                  );
                }

                // ✅ NEW: Load ảnh ngay cho variant này và gán vào operation
                const variantImages = await this.loadVariantImages(physicalProductId, operation.id);
                const operationWithImages = {
                  ...operation,
                  data: {
                    ...operation.data,
                    images: variantImages
                  }
                };
                operationsWithImages.push(operationWithImages);
              }
            }
            break;

          case VariantOperationType.DELETE:
            if (operation.id) {
              await this.physicalProductVariantRepository.delete(operation.id);
              this.logger.log(`Deleted variant ID: ${operation.id}`);
              // DELETE operation không cần images
              operationsWithImages.push(operation);
            }
            break;

          default:
            this.logger.warn(`Unknown variant operation: ${operation.operation}`);
        }
      } catch (error) {
        this.logger.error(`Error processing variant operation: ${error.message}`, error.stack);
        // Continue with other operations instead of failing completely
      }
    }

    // Return all current variants for this physical product
    const allVariants = await this.physicalProductVariantRepository.findByPhysicalProductId(physicalProductId);
    return {
      variants: allVariants,
      variantImageUploadUrls: [], // ✅ NEW: Không trả về upload URLs nữa
      operationsWithImages: operationsWithImages
    };
  }

  /**
   * Xử lý image operations (ADD/DELETE) cho product level
   */
  private async processImageOperations(
    physicalProductId: number,
    operations: any[],
    userId: number,
  ): Promise<{ uploadUrls: any[] }> {
    const uploadUrls: any[] = [];
    const timestamp = Date.now();

    for (const operation of operations) {
      try {
        switch (operation.operation) {
          case ImageOperationType.ADD:
            if (operation.mediaId) {
              // ✅ NEW: Lấy media từ kho media_data theo mediaId
              const existingMedia = await this.mediaRepository.findOneBy({ id: operation.mediaId });

              if (!existingMedia) {
                throw new AppException(
                  CUSTOMER_PRODUCT_ERROR_CODES.MEDIA_NOT_FOUND,
                  `Media với ID ${operation.mediaId} không tồn tại trong kho media.`
                );
              }

              // Kiểm tra quyền sở hữu media (nếu cần)
              if (existingMedia.ownedBy !== userId) {
                throw new AppException(
                  CUSTOMER_PRODUCT_ERROR_CODES.MEDIA_ACCESS_DENIED,
                  `Bạn không có quyền sử dụng media này.`
                );
              }

              // ✅ FIX: Tự động tính position cao nhất cho product images
              const nextPosition = await this.getNextPositionForProductImages(physicalProductId);

              // ✅ NEW: Tạo bản ghi trong entity_has_media để liên kết media với product
              const mediaLinkRecord: Partial<EntityHasMedia> = {
                productId: physicalProductId,  // Product level image
                physicalVarial: null,          // Không phải variant level
                ticketVarial: null,
                versionId: null,
                productComboId: null,
                productPlanVarialId: null,
                mediaId: existingMedia.id, // UUID string từ media có sẵn
              };

              const createdMediaLink = await this.entityHasMediaRepository.create(mediaLinkRecord);

              this.logger.log(`Liên kết media có sẵn với sản phẩm: ${existingMedia.storageKey} (Media ID: ${existingMedia.id})`);
            }
            break;

          case ImageOperationType.DELETE:
            if (operation.entityMediaId) {
              // ✅ NEW: Xóa theo entity_has_media.id - chính xác 100%
              const mediaLink = await this.entityHasMediaRepository.findById(operation.entityMediaId);

              if (!mediaLink) {
                this.logger.warn(`Không tìm thấy liên kết media với ID ${operation.entityMediaId}`);
                break;
              }

              // Kiểm tra quyền sở hữu - chỉ xóa media của product này
              // ✅ FIX: Sử dụng == thay vì !== để handle type conversion
              if (mediaLink.productId != physicalProductId || mediaLink.physicalVarial !== null) {
                this.logger.warn(`Liên kết media ${operation.entityMediaId} không thuộc về product ${physicalProductId} hoặc không phải product-level`);
                break;
              }

              await this.entityHasMediaRepository.delete(operation.entityMediaId);
              this.logger.log(`Đã xóa liên kết media ID: ${operation.entityMediaId}`);

            } else if (operation.key) {
              // ⚠️ DEPRECATED: Backward compatibility - tìm theo key
              this.logger.warn(`⚠️ DEPRECATED: Sử dụng 'key' để xóa ảnh đã lỗi thời. Vui lòng sử dụng 'entityMediaId'.`);

              // Tìm media theo storage_key trong media_data, sau đó tìm liên kết
              const mediaRecords = await this.mediaRepository.find({
                where: { storageKey: operation.key }
              });

              if (mediaRecords.length === 0) {
                this.logger.warn(`Không tìm thấy media với key: ${operation.key}`);
                break;
              }

              // Xóa tất cả liên kết product-level của media này với product hiện tại
              for (const media of mediaRecords) {
                await this.entityHasMediaRepository.deleteSpecificMediaLink(
                  physicalProductId,
                  media.id,
                  undefined // product level
                );
              }

              this.logger.log(`Đã xóa liên kết media với key: ${operation.key} (deprecated method)`);
            }
            break;

          default:
            this.logger.warn(`Unknown image operation: ${operation.operation}`);
        }
      } catch (error) {
        this.logger.error(`Error processing image operation: ${error.message}`, error.stack);
        // Continue with other operations instead of failing completely
      }
    }

    return { uploadUrls: [] }; // ✅ NEW: Không trả về upload URLs nữa
  }

  /**
   * Xử lý image operations cho variant level
   */
  private async processVariantImageOperations(
    physicalProductId: number,
    variantId: number,
    operations: any[],
    userId: number,
  ): Promise<{ uploadUrls: any[] }> {
    const uploadUrls: any[] = [];
    const timestamp = Date.now();

    for (const operation of operations) {
      try {
        switch (operation.operation) {
          case ImageOperationType.ADD:
            if (operation.mediaId) {
              // ✅ NEW: Lấy media từ kho media_data theo mediaId
              const existingMedia = await this.mediaRepository.findOneBy({ id: operation.mediaId });

              if (!existingMedia) {
                throw new AppException(
                  CUSTOMER_PRODUCT_ERROR_CODES.MEDIA_NOT_FOUND,
                  `Media với ID ${operation.mediaId} không tồn tại trong kho media.`
                );
              }

              // Kiểm tra quyền sở hữu media (nếu cần)
              if (existingMedia.ownedBy !== userId) {
                throw new AppException(
                  CUSTOMER_PRODUCT_ERROR_CODES.MEDIA_ACCESS_DENIED,
                  `Bạn không có quyền sử dụng media này.`
                );
              }

              // ✅ FIX: Tự động tính position cao nhất cho variant images
              const nextPosition = await this.getNextPositionForVariantImages(physicalProductId, variantId);

              // ✅ NEW: Tạo bản ghi trong entity_has_media để liên kết media với variant
              const mediaLinkRecord: Partial<EntityHasMedia> = {
                productId: physicalProductId,
                physicalVarial: variantId,  // ← Variant level image
                ticketVarial: null,
                versionId: null,
                productComboId: null,
                productPlanVarialId: null,
                mediaId: existingMedia.id, // UUID string từ media có sẵn
              };

              const createdMediaLink = await this.entityHasMediaRepository.create(mediaLinkRecord);

              this.logger.log(`Liên kết media có sẵn với variant ${variantId}: ${existingMedia.storageKey} (Media ID: ${existingMedia.id})`);
            }
            break;

          case ImageOperationType.DELETE:
            if (operation.entityMediaId) {
              // ✅ NEW: Xóa theo entity_has_media.id - chính xác 100%
              const mediaLink = await this.entityHasMediaRepository.findById(operation.entityMediaId);

              if (!mediaLink) {
                this.logger.warn(`Không tìm thấy liên kết media với ID ${operation.entityMediaId}`);
                break;
              }

              // Kiểm tra quyền sở hữu - chỉ xóa media của variant này
              // ✅ FIX: Sử dụng == thay vì !== để handle type conversion
              if (mediaLink.productId != physicalProductId || mediaLink.physicalVarial != variantId) {
                this.logger.warn(`Liên kết media ${operation.entityMediaId} không thuộc về variant ${variantId} của product ${physicalProductId}`);
                break;
              }

              await this.entityHasMediaRepository.delete(operation.entityMediaId);
              this.logger.log(`Đã xóa liên kết media variant ID: ${operation.entityMediaId}`);

            } else if (operation.key) {
              // ⚠️ DEPRECATED: Backward compatibility
              this.logger.warn(`⚠️ DEPRECATED: Sử dụng 'key' để xóa ảnh variant đã lỗi thời. Vui lòng sử dụng 'entityMediaId'.`);

              // Tìm media theo storage_key, sau đó xóa liên kết variant-level
              const mediaRecords = await this.mediaRepository.find({
                where: { storageKey: operation.key }
              });

              if (mediaRecords.length === 0) {
                this.logger.warn(`Không tìm thấy media với key: ${operation.key}`);
                break;
              }

              // Xóa liên kết variant-level của media này
              for (const media of mediaRecords) {
                await this.entityHasMediaRepository.deleteSpecificMediaLink(
                  physicalProductId,
                  media.id,
                  variantId // variant level
                );
              }

              this.logger.log(`Đã xóa liên kết media variant với key: ${operation.key} (deprecated method)`);
            }
            break;

          default:
            this.logger.warn(`Unknown variant image operation: ${operation.operation}`);
        }
      } catch (error) {
        this.logger.error(`Error processing variant image operation: ${error.message}`, error.stack);
      }
    }

    return { uploadUrls: [] }; // ✅ NEW: Không trả về upload URLs nữa
  }

  /**
   * Build response hoàn chỉnh theo cấu trúc mới
   */
  private async buildCompleteResponse(
    customerProduct: CustomerProduct,
    physicalProduct?: PhysicalProduct,
    variants?: PhysicalProductVariant[],
    operationResults?: any,
  ): Promise<CompletePhysicalProductResponseDto> {
    const response = new CompletePhysicalProductResponseDto();

    // Set productId (convert number to string format)
    response.productId = `PROD-${customerProduct.id.toString().padStart(3, '0')}-2025`;

    // Build basicInfo
    response.basicInfo = {
      name: customerProduct.name,
      description: customerProduct.description || undefined,
      productType: customerProduct.productType,
      tags: customerProduct.tags || undefined,
    };

    // Build pricing
    response.pricing = {
      price: customerProduct.price || undefined,
      typePrice: customerProduct.typePrice || undefined,
    };

    // Build physicalInfo
    response.physicalInfo = {
      stockQuantity: physicalProduct?.stockQuantity || undefined,
      sku: physicalProduct?.sku || undefined,
      barcode: physicalProduct?.barcode || undefined,
      shipmentConfig: physicalProduct?.shipmentConfig || undefined,
    };

    // Build customFields
    if (customerProduct.customFields && typeof customerProduct.customFields === 'object') {
      response.customFields = Object.entries(customerProduct.customFields).map(([customFieldId, value]) => ({
        customFieldId: parseInt(customFieldId),
        value: value as { value: string }
      }));
    } else {
      response.customFields = undefined;
    }

    // Load product-level images
    response.images = await this.loadProductImages(customerProduct.id);

    // Build variants with embedded images
    if (variants && variants.length > 0) {
      response.variants = await Promise.all(variants.map(async (variant) => {
        // Load images for this variant
        const variantImages = await this.loadVariantImagesForResponse(customerProduct.id, variant.id);

        return {
          id: variant.id,
          name: variant.name,
          sku: variant.sku || undefined,
          attributes: variant.attributes || undefined,
          price: variant.price || undefined,
          stockQuantity: 50, // Default stock quantity since entity doesn't have this field
          shipmentConfig: variant.shipmentConfig || undefined,
          images: variantImages,
          status: 'ACTIVE', // Default status
          createdAt: new Date().toISOString(), // Default timestamp since entity doesn't have this field
          updatedAt: new Date().toISOString(), // Default timestamp since entity doesn't have this field
        };
      }));
    } else {
      response.variants = [];
    }

    // Set operation results if provided
    if (operationResults) {
      response.operationResults = operationResults;
    }

    return response;
  }

  /**
   * Load product-level images theo format mới
   */
  private async loadProductImages(productId: number): Promise<any[]> {
    try {
      this.logger.log(`🔍 DEBUG: Loading product images for productId=${productId}`);
      const mediaLinks = await this.entityHasMediaRepository.findByProductId(productId);
      this.logger.log(`🔍 DEBUG: Found ${mediaLinks.length} total media links for product ${productId}`);

      // Log all media links to debug
      mediaLinks.forEach((link, index) => {
        this.logger.log(`🔍 DEBUG: MediaLink ${index}: id=${link.id}, productId=${link.productId}, physicalVarial=${link.physicalVarial}, versionId=${link.versionId}, mediaId=${link.mediaId}`);
      });

      // Filter chỉ lấy ảnh product level (product_id có giá trị, physical_varial = null)
      const productLevelLinks = mediaLinks.filter(link =>
        link.productId &&
        link.physicalVarial === null &&  // physical_varial = null nghĩa là product level
        link.ticketVarial === null &&
        link.versionId === null &&
        link.productComboId === null &&
        link.productPlanVarialId === null &&
        link.mediaId  // Phải có mediaId để join
      );

      this.logger.log(`🔍 DEBUG: Found ${productLevelLinks.length} product-level links after filtering`);
      productLevelLinks.forEach((link, index) => {
        this.logger.log(`🔍 DEBUG: Product-level link ${index}: id=${link.id}, mediaId=${link.mediaId}`);
      });

      if (productLevelLinks.length === 0) {
        this.logger.log(`🔍 DEBUG: No product-level images found for product ${productId}`);
        return [];
      }

      // Lấy danh sách mediaId để query media_data (deduplicate)
      const allMediaIds = productLevelLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      // ✅ FIX: Deduplicate mediaIds để tránh query trùng lặp
      const uniqueMediaIds = [...new Set(allMediaIds)];

      this.logger.log(`🔍 DEBUG: All MediaIds: ${JSON.stringify(allMediaIds)}`);
      this.logger.log(`🔍 DEBUG: Unique MediaIds to query: ${JSON.stringify(uniqueMediaIds)}`);

      if (uniqueMediaIds.length === 0) {
        this.logger.log(`🔍 DEBUG: No mediaIds found, returning empty array`);
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(uniqueMediaIds);
      this.logger.log(`🔍 DEBUG: Found ${mediaRecords.length} unique media records from media_data table`);

      // ✅ FIX: Map từng entity_has_media record thành image object riêng biệt
      const mediaWithEntityIds = await Promise.all(productLevelLinks.map(async (entityLink, index) => {
        // Tìm media record tương ứng
        const media = mediaRecords.find(m => m.id === entityLink.mediaId);

        if (!media) {
          this.logger.warn(`Không tìm thấy media record cho mediaId: ${entityLink.mediaId}`);
          return null;
        }

        let viewUrl = '';

        // Tạo CDN view URL từ storageKey trong media_data
        if (media.storageKey) {
          try {
            // Convert to CDN format: https://cdn.redai.vn/...
            viewUrl = `https://cdn.redai.vn/${media.storageKey}`;
          } catch (error) {
            this.logger.error(`Lỗi khi tạo CDN view URL cho key ${media.storageKey}: ${error.message}`);
            viewUrl = '';
          }
        }

        return {
          id: `img-${String(index + 1).padStart(3, '0')}`,
          url: viewUrl,
          key: media.storageKey || '',
          alt: `${media.name} - Ảnh sản phẩm`,
          isPrimary: index === 0, // First image is primary
          mediaId: media.id,
          entityMediaId: entityLink.id, // ✅ Mỗi entity_has_media record có ID riêng
          uploadedAt: new Date().toISOString(),
        };
      }));

      // Filter out null values
      const validImages = mediaWithEntityIds.filter(Boolean);

      this.logger.log(`🔍 DEBUG: Returning ${validImages.length} product-level images`);
      return validImages;
    } catch (error) {
      this.logger.error(`Lỗi khi load product images: ${error.message}`);
      return [];
    }
  }

  /**
   * Load variant images theo format mới cho response
   */
  private async loadVariantImagesForResponse(productId: number, variantId: number): Promise<any[]> {
    try {
      const mediaLinks = await this.entityHasMediaRepository.findByProductId(productId);

      // Filter chỉ lấy ảnh variant level cho variant này
      const variantLevelLinks = mediaLinks.filter(link =>
        link.productId &&
        link.physicalVarial == variantId &&  // Use == instead of === to handle type conversion
        !link.ticketVarial &&
        !link.versionId &&
        !link.productComboId &&
        !link.productPlanVarialId &&
        link.mediaId  // Phải có mediaId để join
      );

      if (variantLevelLinks.length === 0) {
        return [];
      }

      // Lấy danh sách mediaId để query media_data
      const mediaIds = variantLevelLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      // Map media records với entity_has_media để lấy entityMediaId cho variant
      const mediaWithEntityIds = await Promise.all(mediaRecords.map(async (media, index) => {
        // Tìm entity_has_media record tương ứng cho variant này
        const entityMediaLink = variantLevelLinks.find(link => link.mediaId === media.id);

        let viewUrl = '';

        // Tạo CDN view URL
        if (media.storageKey) {
          try {
            // Convert to CDN format: https://cdn.redai.vn/...
            viewUrl = `https://cdn.redai.vn/${media.storageKey}`;
          } catch (error) {
            this.logger.error(`Lỗi khi tạo CDN view URL cho variant image ${media.storageKey}: ${error.message}`);
            viewUrl = '';
          }
        }

        return {
          id: `var-img-${String(index + 1).padStart(3, '0')}`,
          url: viewUrl,
          key: media.storageKey || '',
          alt: `${media.name} - Ảnh biến thể`,
          isPrimary: index === 0, // First image is primary
          mediaId: media.id,
          entityMediaId: entityMediaLink?.id || null, // ✅ NEW: ID để xóa chính xác
          uploadedAt: new Date().toISOString(),
        };
      }));

      return mediaWithEntityIds;
    } catch (error) {
      this.logger.error(`Lỗi khi load variant images cho variant ${variantId}: ${error.message}`);
      return [];
    }
  }

  /**
   * ✅ FIX: Tự động tính position cao nhất cho product images
   */
  private async getNextPositionForProductImages(productId: number): Promise<number> {
    try {
      // 1. Lấy tất cả media links cho product level (physical_varial = null)
      const mediaLinks = await this.entityHasMediaRepository.findByProductId(productId);
      const productLevelLinks = mediaLinks.filter(link =>
        link.productId &&
        !link.physicalVarial &&  // product level
        !link.ticketVarial &&
        !link.versionId &&
        !link.productComboId &&
        !link.productPlanVarialId &&
        link.mediaId
      );

      if (productLevelLinks.length === 0) {
        return 1; // Bắt đầu từ position 1
      }

      // 2. Lấy tất cả media records để đọc position từ tags
      const mediaIds = productLevelLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return 1;
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      // 3. Tìm position cao nhất
      let maxPosition = 0;
      for (const media of mediaRecords) {
        if (media.tags && typeof media.tags === 'object' && media.tags.position) {
          const position = parseInt(media.tags.position.toString());
          if (!isNaN(position) && position > maxPosition) {
            maxPosition = position;
          }
        }
      }

      return maxPosition + 1;
    } catch (error) {
      this.logger.error(`Lỗi khi tính position cho product images: ${error.message}`);
      return 1; // Fallback
    }
  }

  /**
   * ✅ NEW: Load ảnh cho một variant cụ thể
   */
  private async loadVariantImages(productId: number, variantId: number): Promise<Array<{
    key: string;
    mediaId: string;
    url: string;
  }>> {
    try {
      // 1. Lấy media links cho variant này
      const mediaLinks = await this.entityHasMediaRepository.findByProductId(productId);
      const variantLinks = mediaLinks.filter(link =>
        link.productId &&
        link.physicalVarial === variantId &&
        !link.ticketVarial &&
        !link.versionId &&
        !link.productComboId &&
        !link.productPlanVarialId &&
        link.mediaId
      );

      if (variantLinks.length === 0) {
        return []; // Không có ảnh
      }

      // 2. Lấy media records
      const mediaIds = variantLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      // 3. Tạo response format theo yêu cầu
      const images: Array<{
        key: string;
        mediaId: string;
        url: string;
      }> = [];
      for (const mediaRecord of mediaRecords) {
        if (!mediaRecord) continue;

        let viewUrl = '';

        // Tạo CDN view URL
        if (mediaRecord.storageKey) {
          try {
            const cdnUrl = this.cdnService.generateUrlView(mediaRecord.storageKey, TimeIntervalEnum.ONE_DAY);
            viewUrl = cdnUrl || '';
          } catch (error) {
            this.logger.error(`Lỗi khi tạo CDN view URL cho variant image ${mediaRecord.storageKey}: ${error.message}`);
            viewUrl = '';
          }
        }

        images.push({
          key: mediaRecord.storageKey || '',
          mediaId: mediaRecord.id,
          url: viewUrl,
        });
      }

      return images;
    } catch (error) {
      this.logger.error(`Lỗi khi load variant images cho variant ${variantId}: ${error.message}`);
      return []; // Trả về empty array nếu có lỗi
    }
  }

  /**
   * ✅ FIX: Tự động tính position cao nhất cho variant images
   */
  private async getNextPositionForVariantImages(productId: number, variantId: number): Promise<number> {
    try {
      // 1. Lấy tất cả media links cho variant level (physical_varial = variantId)
      const mediaLinks = await this.entityHasMediaRepository.findByProductId(productId);
      const variantLevelLinks = mediaLinks.filter(link =>
        link.productId &&
        link.physicalVarial === variantId &&  // variant level
        !link.ticketVarial &&
        !link.versionId &&
        !link.productComboId &&
        !link.productPlanVarialId &&
        link.mediaId
      );

      if (variantLevelLinks.length === 0) {
        return 1; // Bắt đầu từ position 1
      }

      // 2. Lấy tất cả media records để đọc position từ tags
      const mediaIds = variantLevelLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return 1;
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      // 3. Tìm position cao nhất
      let maxPosition = 0;
      for (const media of mediaRecords) {
        if (media.tags && typeof media.tags === 'object' && media.tags.position) {
          const position = parseInt(media.tags.position.toString());
          if (!isNaN(position) && position > maxPosition) {
            maxPosition = position;
          }
        }
      }

      return maxPosition + 1;
    } catch (error) {
      this.logger.error(`Lỗi khi tính position cho variant images: ${error.message}`);
      return 1; // Fallback
    }
  }
}
