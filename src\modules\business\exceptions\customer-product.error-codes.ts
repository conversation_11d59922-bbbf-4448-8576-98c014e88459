import { ErrorCode } from '@common/exceptions/app.exception';
import { HttpStatus } from '@nestjs/common';

/**
 * Error codes cho Customer Product module
 * Range: 4000-4099
 */
export const CUSTOMER_PRODUCT_ERROR_CODES = {
  // General errors (4000-4009)
  CREATION_FAILED: new ErrorCode(4000, 'Lỗi khi tạo sản phẩm khách hàng', HttpStatus.BAD_REQUEST),
  UPDATE_FAILED: new ErrorCode(4001, 'Lỗi khi cập nhật sản phẩm khách hàng', HttpStatus.BAD_REQUEST),
  DELETE_FAILED: new ErrorCode(4002, 'Lỗi khi xóa sản phẩm khách hàng', HttpStatus.BAD_REQUEST),
  FIND_FAILED: new ErrorCode(4003, 'Lỗi khi tìm kiếm sản phẩm khách hàng', HttpStatus.BAD_REQUEST),
  NOT_FOUND: new ErrorCode(4004, 'Không tìm thấy sản phẩm khách hàng', HttpStatus.NOT_FOUND),

  // Validation errors (4010-4019)
  VALIDATION_FAILED: new ErrorCode(4010, 'Dữ liệu không hợp lệ', HttpStatus.BAD_REQUEST),
  REQUIRED_FIELD_MISSING: new ErrorCode(4011, 'Thiếu trường bắt buộc', HttpStatus.BAD_REQUEST),
  INVALID_FIELD_FORMAT: new ErrorCode(4012, 'Định dạng trường không hợp lệ', HttpStatus.BAD_REQUEST),
  INVALID_PRODUCT_TYPE: new ErrorCode(4013, 'Loại sản phẩm không hợp lệ', HttpStatus.BAD_REQUEST),
  INVALID_STATUS: new ErrorCode(4014, 'Trạng thái không hợp lệ', HttpStatus.BAD_REQUEST),

  // Permission errors (4020-4029)
  ACCESS_DENIED: new ErrorCode(4020, 'Không có quyền truy cập', HttpStatus.FORBIDDEN),
  OWNERSHIP_REQUIRED: new ErrorCode(4021, 'Yêu cầu quyền sở hữu', HttpStatus.FORBIDDEN),
  INSUFFICIENT_PERMISSIONS: new ErrorCode(4022, 'Không đủ quyền hạn', HttpStatus.FORBIDDEN),

  // Business logic errors (4030-4039)
  DUPLICATE_NAME: new ErrorCode(4030, 'Tên sản phẩm đã tồn tại', HttpStatus.CONFLICT),
  INVALID_OPERATION: new ErrorCode(4031, 'Thao tác không hợp lệ', HttpStatus.BAD_REQUEST),
  STATUS_TRANSITION_INVALID: new ErrorCode(4032, 'Chuyển đổi trạng thái không hợp lệ', HttpStatus.BAD_REQUEST),

  // Physical product specific errors (4040-4059)
  PHYSICAL_PRODUCT_INVALID_STOCK: new ErrorCode(4040, 'Số lượng tồn kho không hợp lệ', HttpStatus.BAD_REQUEST),
  PHYSICAL_PRODUCT_INVALID_SKU: new ErrorCode(4041, 'Mã SKU không hợp lệ', HttpStatus.BAD_REQUEST),
  PHYSICAL_PRODUCT_INVALID_BARCODE: new ErrorCode(4042, 'Mã vạch không hợp lệ', HttpStatus.BAD_REQUEST),
  PHYSICAL_PRODUCT_INVALID_SHIPMENT_CONFIG: new ErrorCode(4043, 'Cấu hình vận chuyển không hợp lệ', HttpStatus.BAD_REQUEST),
  PHYSICAL_PRODUCT_VARIANT_NOT_FOUND: new ErrorCode(4044, 'Không tìm thấy biến thể sản phẩm', HttpStatus.NOT_FOUND),
  PHYSICAL_PRODUCT_VARIANT_INVALID: new ErrorCode(4045, 'Biến thể sản phẩm không hợp lệ', HttpStatus.BAD_REQUEST),
  PHYSICAL_PRODUCT_SKU_DUPLICATE: new ErrorCode(4046, 'Mã SKU đã tồn tại', HttpStatus.CONFLICT),
  PHYSICAL_PRODUCT_BARCODE_DUPLICATE: new ErrorCode(4047, 'Mã vạch đã tồn tại', HttpStatus.CONFLICT),

  // Image operation errors (4060-4069)
  IMAGE_OPERATION_FAILED: new ErrorCode(4060, 'Lỗi thao tác hình ảnh', HttpStatus.BAD_REQUEST),
  IMAGE_KEY_REQUIRED: new ErrorCode(4061, 'Yêu cầu key hình ảnh', HttpStatus.BAD_REQUEST),
  IMAGE_MEDIA_ID_REQUIRED: new ErrorCode(4062, 'Yêu cầu media ID', HttpStatus.BAD_REQUEST),
  IMAGE_NOT_FOUND: new ErrorCode(4063, 'Không tìm thấy hình ảnh', HttpStatus.NOT_FOUND),
  IMAGE_LIMIT_EXCEEDED: new ErrorCode(4064, 'Vượt quá giới hạn hình ảnh', HttpStatus.BAD_REQUEST),
  MEDIA_NOT_FOUND: new ErrorCode(4065, 'Không tìm thấy media trong kho', HttpStatus.NOT_FOUND),
  MEDIA_ACCESS_DENIED: new ErrorCode(4066, 'Không có quyền truy cập media', HttpStatus.FORBIDDEN),

  // Variant operation errors (4070-4079)
  VARIANT_OPERATION_FAILED: new ErrorCode(4070, 'Lỗi thao tác biến thể', HttpStatus.BAD_REQUEST),
  VARIANT_ID_REQUIRED: new ErrorCode(4071, 'Yêu cầu ID biến thể', HttpStatus.BAD_REQUEST),
  VARIANT_DATA_REQUIRED: new ErrorCode(4072, 'Yêu cầu dữ liệu biến thể', HttpStatus.BAD_REQUEST),
  VARIANT_NAME_DUPLICATE: new ErrorCode(4073, 'Tên biến thể đã tồn tại', HttpStatus.CONFLICT),
  VARIANT_SKU_DUPLICATE: new ErrorCode(4074, 'SKU biến thể đã tồn tại', HttpStatus.CONFLICT),

  // Batch operation errors (4080-4089)
  BATCH_DELETE_FAILED: new ErrorCode(4080, 'Lỗi xóa batch', HttpStatus.BAD_REQUEST),
  BATCH_DELETE_PARTIAL_FAILURE: new ErrorCode(4081, 'Xóa batch một phần thất bại', HttpStatus.MULTI_STATUS),
  BATCH_SIZE_EXCEEDED: new ErrorCode(4082, 'Vượt quá kích thước batch', HttpStatus.BAD_REQUEST),
  BATCH_EMPTY_LIST: new ErrorCode(4083, 'Danh sách batch trống', HttpStatus.BAD_REQUEST),

  // Database errors (4090-4099)
  DATABASE_CONNECTION_FAILED: new ErrorCode(4090, 'Lỗi kết nối database', HttpStatus.INTERNAL_SERVER_ERROR),
  DATABASE_TRANSACTION_FAILED: new ErrorCode(4091, 'Lỗi transaction database', HttpStatus.INTERNAL_SERVER_ERROR),
  DATABASE_CONSTRAINT_VIOLATION: new ErrorCode(4092, 'Vi phạm ràng buộc database', HttpStatus.BAD_REQUEST),
  DATABASE_TIMEOUT: new ErrorCode(4093, 'Timeout database', HttpStatus.INTERNAL_SERVER_ERROR),
} as const;
