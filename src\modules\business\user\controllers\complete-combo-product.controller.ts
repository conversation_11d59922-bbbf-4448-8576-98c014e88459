import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Put,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto } from '@common/response';
import { CompleteComboProductService } from '@modules/business/user/services/complete-combo-product.service';
import { CompleteUpdateComboProductDto } from '@modules/business/user/dto/combo-product';
import { CompleteComboProductResponseDto } from '@modules/business/user/dto/combo-product';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý cập nhật hoàn chỉnh Combo Product
 * Bao gồm customer_products + combo_products + product_advanced_info + images
 */
@ApiTags(SWAGGER_API_TAGS.USER_BUSINESS_VARIANT_PRODUCT)
@Controller('/user/combo-products')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(CompleteUpdateComboProductDto, CompleteComboProductResponseDto)
export class CompleteComboProductController {
  constructor(
    private readonly completeComboProductService: CompleteComboProductService,
  ) {}

  /**
   * Cập nhật hoàn chỉnh combo product
   * @param id ID của combo product
   * @param updateDto DTO chứa toàn bộ thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin combo product đã cập nhật
   */
  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cập nhật hoàn chỉnh combo product',
    description: `
    Cập nhật toàn bộ thông tin của combo product bao gồm:
    - Thông tin cơ bản (tên, mô tả, tags)
    - Thông tin giá (price, typePrice)
    - Thông tin combo (maxQuantity, purchaseCount)
    - Custom fields
    - Thao tác với combo items (ADD/UPDATE/DELETE)
    - Thao tác với hình ảnh combo level (ADD/DELETE)
    
    API này sẽ cập nhật các bảng:
    - customer_products: thông tin cơ bản và giá
    - combo_products: thông tin combo specific
    - product_advanced_info: combo items và metadata
    - entity_has_media: hình ảnh combo level
    `,
  })
  @ApiParam({
    name: 'id',
    description: 'ID của combo product cần cập nhật',
    type: 'number',
    example: 123,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật combo product thành công',
    type: () => ApiResponseDto.getSchema(CompleteComboProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy combo product',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Không có quyền truy cập',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền cập nhật combo product này',
  })
  async updateComplete(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: CompleteUpdateComboProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.completeComboProductService.updateCompleteComboProduct(
      id,
      updateDto,
      userId,
    );
    return ApiResponseDto.success<CompleteComboProductResponseDto>(
      product,
      'Cập nhật combo product thành công',
    );
  }

  /**
   * Lấy chi tiết hoàn chỉnh combo product
   * @param id ID của combo product
   * @param userId ID của người dùng hiện tại
   * @returns Chi tiết combo product hoàn chỉnh
   */
  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy chi tiết hoàn chỉnh combo product',
    description: `
    Lấy toàn bộ thông tin chi tiết của combo product bao gồm:
    - Thông tin cơ bản (tên, mô tả, tags, status)
    - Thông tin giá (price, typePrice)
    - Thông tin combo (maxQuantity, purchaseCount, totalItems, isAvailable)
    - Custom fields
    - Hình ảnh combo level
    - Thông tin nâng cao với combo items chi tiết
    - Thông tin sản phẩm con trong combo (tên, loại, giá, ảnh đại diện)
    
    API này sẽ truy vấn các bảng:
    - customer_products: thông tin cơ bản
    - combo_products: thông tin combo specific
    - product_advanced_info: combo items và metadata
    - entity_has_media: hình ảnh combo level và product level
    `,
  })
  @ApiParam({
    name: 'id',
    description: 'ID của combo product cần lấy chi tiết',
    type: 'number',
    example: 123,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy chi tiết combo product thành công',
    type: () => ApiResponseDto.getSchema(CompleteComboProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy combo product',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Không có quyền truy cập',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền xem combo product này',
  })
  async getCompleteDetail(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.completeComboProductService.getCompleteComboProduct(
      id,
      userId,
    );
    return ApiResponseDto.success<CompleteComboProductResponseDto>(
      product,
      'Lấy chi tiết combo product thành công',
    );
  }
}
