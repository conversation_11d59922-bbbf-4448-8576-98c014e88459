import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsOptional,
  IsNumber,
  IsString,
  IsBoolean,
  IsObject,
  ValidateNested,
  IsArray,
  Min,
  MaxLength,
  ValidationArguments,
  registerDecorator,
  ValidationOptions,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ProductPrice } from '@modules/business/interfaces';

/**
 * Custom validator để kiểm tra salePrice <= listPrice cho ProductPrice
 */
export function IsValidProductPrice(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isValidProductPrice',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!value || typeof value !== 'object') return true;

          const { listPrice, salePrice } = value;
          if (typeof listPrice === 'number' && typeof salePrice === 'number') {
            return salePrice <= listPrice;
          }
          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return 'Gi<PERSON> bán không được lớn hơn giá niêm yết';
        },
      },
    });
  };
}

/**
 * Enum cho các loại thao tác với gói dịch vụ
 */
export enum PackageOperationType {
  ADD = 'add',
  UPDATE = 'update',
  DELETE = 'delete',
}

/**
 * DTO cho thao tác với ảnh gói dịch vụ
 */
export class PackageImageOperationDto {
  @ApiProperty({
    description: 'Loại thao tác với ảnh',
    enum: ['add', 'delete'],
    example: 'add',
  })
  @IsEnum(['add', 'delete'])
  operation: 'add' | 'delete';

  @ApiProperty({
    description: 'ID media (cho thao tác add)',
    example: 'dcc8f803-04fc-49b3-953a-e660505ac353',
    required: false,
  })
  @IsOptional()
  @IsString()
  mediaId?: string;

  @ApiProperty({
    description: 'ID của entity_has_media record cần xóa (cho thao tác delete)',
    example: 123,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  entityHasMediaId?: number;
}

/**
 * DTO cho dữ liệu gói dịch vụ
 */
export class ServicePackageDataDto {
  @ApiProperty({
    description: 'Tên gói dịch vụ',
    example: 'Gói chăm sóc da cao cấp',
    maxLength: 255,
  })
  @IsString()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Mô tả chi tiết về gói dịch vụ',
    example: 'Gói chăm sóc da toàn diện với các liệu pháp hiện đại',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Giá của gói dịch vụ',
    example: {
      listPrice: 1000000,
      salePrice: 850000,
      currency: 'VND'
    },
  })
  @IsObject()
  @IsValidProductPrice({
    message: 'Giá bán không được lớn hơn giá niêm yết',
  })
  price: ProductPrice;

  @ApiProperty({
    description: 'Thời lượng của gói dịch vụ',
    example: 90,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  duration?: number;

  @ApiProperty({
    description: 'Đơn vị tính cho thời lượng',
    example: 'phút',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  unit?: string;

  @ApiProperty({
    description: 'Danh sách tính năng hoặc lợi ích đi kèm gói dịch vụ',
    example: ['Massage thư giãn', 'Đắp mặt nạ collagen', 'Tư vấn chuyên sâu'],
    required: false,
  })
  @IsOptional()
  features?: any;

  @ApiProperty({
    description: 'Trạng thái hoạt động của gói dịch vụ',
    example: true,
    default: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: 'Gói dịch vụ có bị giới hạn số lần bán không',
    example: false,
    default: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isLimited?: boolean;

  @ApiProperty({
    description: 'Số lượng tối đa được bán nếu gói bị giới hạn',
    example: 100,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxSales?: number;

  @ApiProperty({
    description: 'Danh sách thao tác với ảnh gói dịch vụ (ADD/DELETE)',
    type: [PackageImageOperationDto],
    example: [
      {
        operation: 'add',
        mediaId: 'dcc8f803-04fc-49b3-953a-e660505ac353',
      },
      {
        operation: 'delete',
        entityHasMediaId: 456
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PackageImageOperationDto)
  imageOperations?: PackageImageOperationDto[];
}

/**
 * DTO cho thao tác với gói dịch vụ (ADD/UPDATE/DELETE)
 */
export class ServicePackageOperationDto {
  @ApiProperty({
    description: 'Loại thao tác',
    enum: PackageOperationType,
    example: PackageOperationType.ADD,
  })
  @IsEnum(PackageOperationType)
  operation: PackageOperationType;

  @ApiProperty({
    description: 'ID gói dịch vụ (bắt buộc cho UPDATE và DELETE)',
    example: 123,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  id?: number;

  @ApiProperty({
    description: 'Dữ liệu gói dịch vụ (bắt buộc cho ADD, tùy chọn cho UPDATE)',
    type: ServicePackageDataDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ServicePackageDataDto)
  data?: ServicePackageDataDto;
}
