import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsOptional,
  IsNumber,
  IsString,
  IsArray,
  ValidateNested,
  IsNotEmpty,
  Min,
  MaxLength,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Enum cho các loại thao tác với vé sự kiện
 */
export enum TicketOperationType {
  ADD = 'add',
  UPDATE = 'update',
  DELETE = 'delete',
}

/**
 * DTO cho thao tác với ảnh vé
 */
export class TicketImageOperationDto {
  @ApiProperty({
    description: 'Loại thao tác với ảnh',
    enum: ['add', 'delete'],
    example: 'add',
  })
  @IsEnum(['add', 'delete'])
  operation: 'add' | 'delete';

  @ApiProperty({
    description: 'ID media (cho thao tác add)',
    example: 'dcc8f803-04fc-49b3-953a-e660505ac353',
    required: false,
  })
  @IsOptional()
  @IsString()
  mediaId?: string;

  @ApiProperty({
    description: 'ID của entity_has_media record cần xóa (cho thao tác delete)',
    example: 456,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  entityHasMediaId?: number;
}

/**
 * DTO cho dữ liệu vé sự kiện
 */
export class TicketDataDto {
  @ApiProperty({
    description: 'Tên loại vé',
    example: 'Vé VIP',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  @ApiProperty({
    description: 'Giá vé (VND)',
    example: 500000,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  price?: number;

  @ApiProperty({
    description: 'Tổng số lượng vé được phát hành',
    example: 100,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  totalQuantity?: number;

  @ApiProperty({
    description: 'Mô tả chi tiết cho loại vé',
    example: 'Vé VIP bao gồm chỗ ngồi hạng nhất và buffet',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Thời gian bắt đầu mở bán (epoch milliseconds)',
    example: 1704067200000,
  })
  @IsOptional()
  @IsNumber()
  saleStart?: number;

  @ApiProperty({
    description: 'Thời gian kết thúc bán vé (epoch milliseconds)',
    example: 1704153600000,
  })
  @IsOptional()
  @IsNumber()
  saleEnd?: number;

  @ApiProperty({
    description: 'Múi giờ áp dụng cho thời gian mở bán',
    example: 'Asia/Ho_Chi_Minh',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  timeZone?: string;

  @ApiProperty({
    description: 'Mã định danh SKU cho vé',
    example: 'TICKET-VIP-001',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  sku?: string;

  @ApiProperty({
    description: 'Số lượng tối thiểu phải mua trong 1 lần đặt vé',
    example: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  minQuantityPerOrder?: number;

  @ApiProperty({
    description: 'Số lượng tối đa được phép mua trong 1 lần đặt vé',
    example: 10,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxQuantityPerOrder?: number;

  @ApiProperty({
    description: 'Danh sách thao tác với ảnh vé',
    type: [TicketImageOperationDto],
    example: [
      {
        operation: 'add',
        mediaId: 'dcc8f803-04fc-49b3-953a-e660505ac353',
      },
      {
        operation: 'delete',
        entityHasMediaId: 456
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TicketImageOperationDto)
  imageOperations?: TicketImageOperationDto[];
}

/**
 * DTO cho thao tác với vé sự kiện (ADD/UPDATE/DELETE)
 */
export class EventProductTicketOperationDto {
  @ApiProperty({
    description: 'Loại thao tác với vé',
    enum: TicketOperationType,
    example: TicketOperationType.ADD,
  })
  @IsEnum(TicketOperationType)
  @IsNotEmpty()
  operation: TicketOperationType;

  @ApiProperty({
    description: 'ID vé (bắt buộc cho UPDATE và DELETE)',
    example: 123,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  id?: number;

  @ApiProperty({
    description: 'Dữ liệu vé (bắt buộc cho ADD và UPDATE)',
    type: TicketDataDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => TicketDataDto)
  data?: TicketDataDto;
}
