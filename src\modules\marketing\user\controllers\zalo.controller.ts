import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, getSchemaPath } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ZaloService } from '../services/zalo.service';
import { ZaloOfficialAccount, ZaloFollower, ZaloMessage } from '../entities';
import {
  ConnectOfficialAccountDto,
  FollowerQueryDto,
  FollowerResponseDto,
  MessageQueryDto,
  MessageRequestDto,
  MessageResponseDto,
  OfficialAccountResponseDto,
  OfficialAccountQueryDto,
  TagRequestDto,
  BulkDeleteOfficialAccountsDto,
  BulkDeleteOfficialAccountsResponseDto,
  UserGetListRequestDto,
} from '../dto/zalo';
import { QuotaMessageRequestDto } from '../dto/zalo/quota-message.dto';

/**
 * Controller xử lý API liên quan đến Zalo
 */
@ApiTags(SWAGGER_API_TAGS.ZALO)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo')
export class ZaloController {
  constructor(private readonly zaloService: ZaloService) {}

  /**
   * Lấy danh sách Official Account của người dùng
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách Official Account của người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách Official Account thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'array',
              items: { $ref: getSchemaPath(OfficialAccountResponseDto) }
            }
          }
        }
      ]
    }
  })
  async getOfficialAccounts(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Partial<ZaloOfficialAccount>[]>> {
    const result = await this.zaloService.getOfficialAccounts(user.id);

    // Chỉ trả về các thông tin cần thiết
    const response = result.map(oa => ({
      id: oa.id,
      oaId: oa.oaId,
      name: oa.name,
      description: oa.description,
      avatarUrl: oa.avatarUrl,
      status: oa.status,
      createdAt: oa.createdAt,
      updatedAt: oa.updatedAt,
    }));

    return ApiResponseDto.success(response, 'Lấy danh sách Official Account thành công');
  }

  /**
   * Lấy danh sách Official Account của người dùng có phân trang
   */
  @Get('paginated')
  @ApiOperation({ summary: 'Lấy danh sách Official Account của người dùng có phân trang' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách Official Account có phân trang thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(OfficialAccountResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  async getOfficialAccountsPaginated(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: OfficialAccountQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<Partial<ZaloOfficialAccount>>>> {
    const result = await this.zaloService.getOfficialAccountsPaginated(user.id, queryDto);

    // Chỉ trả về các thông tin cần thiết
    const response = {
      items: result.items.map(oa => ({
        id: oa.id,
        oaId: oa.oaId,
        name: oa.name,
        description: oa.description,
        avatarUrl: oa.avatarUrl,
        status: oa.status,
        createdAt: oa.createdAt,
        updatedAt: oa.updatedAt,
      })),
      meta: result.meta,
    };

    return ApiResponseDto.success(response, 'Lấy danh sách Official Account có phân trang thành công');
  }

  /**
   * Lấy thông tin chi tiết Official Account
   */
  @Get(':oaId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết Official Account' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết Official Account thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(OfficialAccountResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy Official Account' })
  async getOfficialAccountDetail(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
  ): Promise<ApiResponseDto<Partial<ZaloOfficialAccount>>> {
    const result = await this.zaloService.getOfficialAccountDetail(user.id, oaId);

    // Chỉ trả về các thông tin cần thiết
    const response = {
      id: result.id,
      oaId: result.oaId,
      name: result.name,
      description: result.description,
      avatarUrl: result.avatarUrl,
      status: result.status,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    };

    return ApiResponseDto.success(response, 'Lấy thông tin chi tiết Official Account thành công');
  }

  /**
   * Lấy thông tin chi tiết Official Account từ Zalo API
   */
  @Get(':oaId/detailed-info')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết Official Account từ Zalo API',
    description: 'Lấy thông tin chi tiết Official Account bao gồm số lượng người theo dõi, thông tin liên hệ, và trạng thái ví ZCA từ Zalo API'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết Official Account từ Zalo API thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                oa_id: {
                  type: 'string',
                  description: 'ID của Official Account',
                  example: '**********'
                },
                name: {
                  type: 'string',
                  description: 'Tên Official Account',
                  example: 'Cửa hàng ABC'
                },
                description: {
                  type: 'string',
                  description: 'Mô tả Official Account',
                  example: 'Cửa hàng bán lẻ thời trang'
                },
                avatar: {
                  type: 'string',
                  description: 'URL avatar của Official Account',
                  example: 'https://example.com/avatar.jpg'
                },
                status: {
                  type: 'string',
                  description: 'Trạng thái Official Account',
                  example: 'active'
                },
                num_follower: {
                  type: 'number',
                  description: 'Số lượng người theo dõi',
                  example: 1250
                },
                oa_type: {
                  type: 'string',
                  description: 'Loại Official Account',
                  example: 'business'
                },
                is_verified: {
                  type: 'boolean',
                  description: 'Trạng thái xác thực',
                  example: true
                },
                contact_info: {
                  type: 'object',
                  description: 'Thông tin liên hệ',
                  properties: {
                    address: {
                      type: 'string',
                      description: 'Địa chỉ',
                      example: '123 Đường ABC, Quận 1, TP.HCM'
                    },
                    phone: {
                      type: 'string',
                      description: 'Số điện thoại',
                      example: '**********'
                    },
                    email: {
                      type: 'string',
                      description: 'Email',
                      example: '<EMAIL>'
                    },
                    website: {
                      type: 'string',
                      description: 'Website',
                      example: 'https://example.com'
                    }
                  }
                },
                zca_info: {
                  type: 'object',
                  description: 'Thông tin ví ZCA',
                  properties: {
                    is_linked: {
                      type: 'boolean',
                      description: 'Trạng thái liên kết ví',
                      example: true
                    },
                    balance: {
                      type: 'number',
                      description: 'Số dư ví (VND)',
                      example: 500000
                    }
                  }
                },
                created_time: {
                  type: 'number',
                  description: 'Thời gian tạo (Unix timestamp)',
                  example: **********
                }
              }
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy Official Account' })
  @ApiResponse({ status: 401, description: 'Official Account chưa có access token hoặc token đã hết hạn' })
  async getDetailedOfficialAccountInfo(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.zaloService.getDetailedOfficialAccountInfo(user.id, oaId);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết Official Account từ Zalo API thành công');
  }

  /**
   * Lấy thông tin quota message chi tiết của Official Account
   */
  @Post(':oaId/quota-message')
  @ApiOperation({
    summary: 'Lấy thông tin quota message chi tiết của Official Account',
    description: 'Lấy thông tin chi tiết về quota message bao gồm các asset, loại sản phẩm, và số lượng quota còn lại từ Zalo API'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin quota message thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      asset_id: {
                        type: 'string',
                        description: 'ID của asset',
                        example: '216991e1ea59bc03e54c'
                      },
                      product_type: {
                        type: 'string',
                        description: 'Loại sản phẩm thuộc nhánh tính năng',
                        example: 'cs'
                      },
                      quota_type: {
                        type: 'string',
                        description: 'Loại quota (nguồn quota sử dụng)',
                        example: 'sub_quota'
                      },
                      valid_through: {
                        type: 'string',
                        description: 'Ngày hết hạn của asset_id',
                        example: '10/10/2024'
                      },
                      total: {
                        type: 'number',
                        description: 'Tổng số lượng quota CHƯA hết hạn (bao gồm available + used) của asset_id',
                        example: 2000
                      },
                      remain: {
                        type: 'number',
                        description: 'Số lượng quota ở trạng thái available của asset_id',
                        example: 1990
                      }
                    }
                  }
                },
                error: {
                  type: 'number',
                  description: 'Mã lỗi (0 = thành công)',
                  example: 0
                },
                message: {
                  type: 'string',
                  description: 'Thông báo kết quả',
                  example: 'success'
                }
              }
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy Official Account' })
  @ApiResponse({ status: 401, description: 'Official Account chưa có access token hoặc token đã hết hạn' })
  async getQuotaMessage(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Body() requestDto: QuotaMessageRequestDto,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.zaloService.getQuotaMessage(
      user.id,
      oaId,
      requestDto.quotaOwner,
      requestDto.productType,
      requestDto.quotaType,
    );
    return ApiResponseDto.success(result, 'Lấy thông tin quota message thành công');
  }

  /**
   * Lấy danh sách người dùng Zalo với các tùy chọn lọc nâng cao
   */
  @Post(':oaId/users/getlist')
  @ApiOperation({
    summary: 'Lấy danh sách người dùng Zalo với các tùy chọn lọc nâng cao',
    description: 'Lấy danh sách người dùng Zalo với khả năng lọc theo nhãn, thời gian tương tác, và trạng thái quan tâm OA'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách người dùng thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                data: {
                  type: 'object',
                  properties: {
                    total: {
                      type: 'number',
                      description: 'Tổng số lượng người dùng thỏa điều kiện',
                      example: 6
                    },
                    count: {
                      type: 'number',
                      description: 'Số lượng người dùng đã request',
                      example: 15
                    },
                    offset: {
                      type: 'number',
                      description: 'Thứ tự của người dùng đầu tiên trong danh sách trả về',
                      example: 0
                    },
                    users: {
                      type: 'array',
                      description: 'Danh sách ID của tất cả người dùng thỏa điều kiện truyền vào',
                      items: {
                        type: 'object',
                        properties: {
                          user_id: {
                            type: 'string',
                            description: 'ID của người dùng Zalo',
                            example: '4572947693969771653'
                          }
                        }
                      }
                    }
                  }
                },
                error: {
                  type: 'number',
                  description: 'Mã lỗi (0 = thành công)',
                  example: 0
                },
                message: {
                  type: 'string',
                  description: 'Thông báo kết quả',
                  example: 'Success'
                }
              }
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy Official Account' })
  @ApiResponse({ status: 401, description: 'Official Account chưa có access token hoặc token đã hết hạn' })
  async getUserList(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Body() requestDto: UserGetListRequestDto,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.zaloService.getUserList(
      user.id,
      oaId,
      requestDto.offset,
      requestDto.count,
      requestDto.tagName,
      requestDto.lastInteractionPeriod,
      requestDto.isFollower,
    );
    return ApiResponseDto.success(result, 'Lấy danh sách người dùng thành công');
  }

  /**
   * Kết nối Official Account với hệ thống
   */
  @Post('connect')
  @ApiOperation({ summary: 'Kết nối Official Account với hệ thống' })
  @ApiResponse({
    status: 200,
    description: 'Kết nối thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(OfficialAccountResponseDto) }
          }
        }
      ]
    }
  })
  async connectOfficialAccount(
    @CurrentUser() user: JwtPayload,
    @Body() connectDto: ConnectOfficialAccountDto,
  ): Promise<ApiResponseDto<Partial<ZaloOfficialAccount>>> {
    const result = await this.zaloService.connectOfficialAccount(
      user.id,
      connectDto.accessToken,
      connectDto.refreshToken,
      connectDto.expiresAt,
    );

    // Chỉ trả về các thông tin cần thiết
    const response = {
      id: result.id,
      oaId: result.oaId,
      name: result.name,
      avatarUrl: result.avatarUrl,
      status: result.status,
    };

    return ApiResponseDto.success(response, 'Kết nối Official Account thành công');
  }

  /**
   * Ngắt kết nối Official Account
   */
  @Delete(':oaId/disconnect')
  @ApiOperation({ summary: 'Ngắt kết nối Official Account' })
  @ApiResponse({
    status: 200,
    description: 'Ngắt kết nối thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { type: 'boolean' }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy Official Account' })
  async disconnectOfficialAccount(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.zaloService.disconnectOfficialAccount(user.id, oaId);
    return ApiResponseDto.success(result, 'Ngắt kết nối Official Account thành công');
  }

  /**
   * Xóa nhiều Official Accounts
   */
  @Delete('bulk-delete')
  @ApiOperation({ summary: 'Xóa nhiều Official Accounts' })
  @ApiResponse({
    status: 200,
    description: 'Xóa nhiều Official Accounts thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(BulkDeleteOfficialAccountsResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ' })
  async bulkDeleteOfficialAccounts(
    @CurrentUser() user: JwtPayload,
    @Body() bulkDeleteDto: BulkDeleteOfficialAccountsDto,
  ): Promise<ApiResponseDto<BulkDeleteOfficialAccountsResponseDto>> {
    const result = await this.zaloService.bulkDeleteOfficialAccounts(user.id, bulkDeleteDto.oaIds);

    const response: BulkDeleteOfficialAccountsResponseDto = {
      totalRequested: result.totalRequested,
      successCount: result.successCount,
      failureCount: result.failureCount,
      successfulDeletes: result.successfulDeletes,
      failedDeletes: result.failedDeletes,
      message: result.message,
    };

    return ApiResponseDto.success(response, result.message);
  }

  /**
   * Lấy danh sách người theo dõi của Official Account từ Zalo API
   */
  @Get(':oaId/followers/zalo')
  @ApiOperation({ summary: 'Lấy danh sách người theo dõi của Official Account từ Zalo API' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách người theo dõi thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                followers: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      display_name: { type: 'string' },
                    }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  async getFollowersFromZalo(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Query('offset') offset: number = 0,
    @Query('limit') limit: number = 50,
  ): Promise<ApiResponseDto<any>> {
    // Kiểm tra quyền truy cập
    await this.zaloService.getOfficialAccountDetail(user.id, oaId);

    const result = await this.zaloService.getFollowers(oaId, offset, limit);
    return ApiResponseDto.success(result, 'Lấy danh sách người theo dõi từ Zalo thành công');
  }

  /**
   * Lấy danh sách người theo dõi của Official Account từ database
   */
  @Get(':oaId/followers')
  @ApiOperation({ summary: 'Lấy danh sách người theo dõi của Official Account từ database' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách người theo dõi thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(FollowerResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  async getFollowersFromDatabase(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Query() queryDto: FollowerQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloFollower>>> {
    // Kiểm tra quyền truy cập
    await this.zaloService.getOfficialAccountDetail(user.id, oaId);

    const result = await this.zaloService.getFollowersFromDatabase(oaId, queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách người theo dõi từ database thành công');
  }

  /**
   * Lấy thông tin chi tiết người theo dõi
   */
  @Get(':oaId/followers/:userId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết người theo dõi' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin người theo dõi thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(FollowerResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy người theo dõi' })
  async getFollowerProfile(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('userId') userId: string,
  ): Promise<ApiResponseDto<Partial<ZaloFollower>>> {
    // Kiểm tra quyền truy cập
    await this.zaloService.getOfficialAccountDetail(user.id, oaId);

    const result = await this.zaloService.getFollowerProfile(oaId, userId);

    // Chỉ trả về các thông tin cần thiết
    const response = {
      id: result.id,
      userId: result.userId,
      displayName: result.displayName,
      avatarUrl: result.avatarUrl,
      phone: result.phone,
      gender: result.gender,
      tags: result.tags,
      status: result.status,
      followedAt: result.followedAt,
    };

    return ApiResponseDto.success(response, 'Lấy thông tin người theo dõi thành công');
  }

  /**
   * Thêm tag cho người theo dõi
   */
  @Post(':oaId/followers/:userId/tags')
  @ApiOperation({ summary: 'Thêm tag cho người theo dõi' })
  @ApiResponse({
    status: 200,
    description: 'Thêm tag thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(FollowerResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy người theo dõi' })
  async addTagToFollower(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('userId') userId: string,
    @Body() tagRequestDto: TagRequestDto,
  ): Promise<ApiResponseDto<Partial<ZaloFollower>>> {
    // Kiểm tra quyền truy cập
    await this.zaloService.getOfficialAccountDetail(user.id, oaId);

    await this.zaloService.addTagToFollower(oaId, userId, tagRequestDto.tagName);

    // Lấy thông tin người theo dõi sau khi thêm tag
    const follower = await this.zaloService.getFollowerProfile(oaId, userId);

    // Chỉ trả về các thông tin cần thiết
    const response = {
      id: follower.id,
      userId: follower.userId,
      displayName: follower.displayName,
      tags: follower.tags,
      status: follower.status,
    };

    return ApiResponseDto.success(response, 'Thêm tag thành công');
  }

  /**
   * Xóa tag của người theo dõi
   */
  @Delete(':oaId/followers/:userId/tags/:tag')
  @ApiOperation({ summary: 'Xóa tag của người theo dõi' })
  @ApiResponse({
    status: 200,
    description: 'Xóa tag thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(FollowerResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy người theo dõi' })
  async removeTagFromFollower(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('userId') userId: string,
    @Param('tag') tag: string,
  ): Promise<ApiResponseDto<Partial<ZaloFollower>>> {
    // Kiểm tra quyền truy cập
    await this.zaloService.getOfficialAccountDetail(user.id, oaId);

    await this.zaloService.removeTagFromFollower(oaId, userId, tag);

    // Lấy thông tin người theo dõi sau khi xóa tag
    const follower = await this.zaloService.getFollowerProfile(oaId, userId);

    // Chỉ trả về các thông tin cần thiết
    const response = {
      id: follower.id,
      userId: follower.userId,
      displayName: follower.displayName,
      tags: follower.tags,
      status: follower.status,
    };

    return ApiResponseDto.success(response, 'Xóa tag thành công');
  }

  /**
   * Lấy lịch sử tin nhắn với một người dùng Zalo
   */
  @Get(':oaId/followers/:userId/messages')
  @ApiOperation({ summary: 'Lấy lịch sử tin nhắn với một người dùng Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Lấy lịch sử tin nhắn thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(MessageResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  async getMessages(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('userId') userId: string,
    @Query() queryDto: MessageQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloMessage>>> {
    // Kiểm tra quyền truy cập
    await this.zaloService.getOfficialAccountDetail(user.id, oaId);

    const result = await this.zaloService.getMessages(oaId, userId, queryDto);
    return ApiResponseDto.success(result, 'Lấy lịch sử tin nhắn thành công');
  }

  /**
   * Gửi tin nhắn đến người dùng Zalo
   */
  @Post(':oaId/messages')
  @ApiOperation({ summary: 'Gửi tin nhắn đến người dùng Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Gửi tin nhắn thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(MessageResponseDto) }
          }
        }
      ]
    }
  })
  async sendMessage(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Body() messageDto: MessageRequestDto,
  ): Promise<ApiResponseDto<Partial<ZaloMessage>>> {
    // Kiểm tra quyền truy cập
    await this.zaloService.getOfficialAccountDetail(user.id, oaId);

    const params: Record<string, any> = {};

    switch (messageDto.messageType) {
      case 'text':
        params.message = messageDto.message;
        break;
      case 'image':
        params.imageUrl = messageDto.imageUrl;
        break;
      case 'file':
        params.fileUrl = messageDto.fileUrl;
        break;
      case 'template':
        params.templateId = messageDto.templateId;
        params.templateData = messageDto.templateData;
        break;
    }

    const result = await this.zaloService.sendMessage(
      oaId,
      messageDto.userId,
      messageDto.messageType,
      params,
    );

    // Chỉ trả về các thông tin cần thiết
    const response = {
      id: result.id,
      messageId: result.messageId,
      messageType: result.messageType,
      content: result.content,
      data: result.data,
      direction: result.direction,
      timestamp: result.timestamp,
    };

    return ApiResponseDto.success(response, 'Gửi tin nhắn thành công');
  }

  /**
   * Webhook để nhận các sự kiện từ Zalo
   * Lưu ý: Endpoint này không yêu cầu xác thực JWT
   */
  @Post('webhook')
  @ApiOperation({ summary: 'Webhook để nhận các sự kiện từ Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Xử lý webhook thành công',
  })
  async handleWebhook(@Body() data: any): Promise<ApiResponseDto<any>> {
    const result = await this.zaloService.handleWebhook(data);
    return ApiResponseDto.success(result, 'Xử lý webhook thành công');
  }
}
