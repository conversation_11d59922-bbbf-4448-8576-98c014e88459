import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO response cho liên kết media với entity
 */
export class EntityHasMediaResponseDto {
  @ApiProperty({
    description: 'ID liên kết',
    example: 1,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'ID của media',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  mediaId: string;

  @ApiProperty({
    description: 'ID sản phẩm (nếu liên kết với sản phẩm)',
    example: 456,
    required: false,
  })
  @Expose()
  productId: number | null;

  @ApiProperty({
    description: 'ID biến thể vật lý (nếu liên kết với biến thể vật lý)',
    example: 789,
    required: false,
  })
  @Expose()
  physicalVarial: number | null;

  @ApiProperty({
    description: 'ID biến thể vé (nếu liên kết với biến thể vé)',
    example: 101,
    required: false,
  })
  @Expose()
  ticketVarial: number | null;

  @ApiProperty({
    description: 'ID phiên bản (nếu liên kết với phiên bản)',
    example: 202,
    required: false,
  })
  @Expose()
  versionId: number | null;

  @ApiProperty({
    description: 'ID combo sản phẩm (nếu liên kết với combo)',
    example: 303,
    required: false,
  })
  @Expose()
  productComboId: number | null;

  @ApiProperty({
    description: 'ID gói dịch vụ (nếu liên kết với gói dịch vụ)',
    example: 404,
    required: false,
  })
  @Expose()
  productPlanVarialId: number | null;
}

/**
 * DTO response cho danh sách liên kết media với phân trang
 */
export class EntityHasMediaListResponseDto {
  @ApiProperty({
    description: 'Danh sách liên kết media',
    type: [EntityHasMediaResponseDto],
  })
  @Expose()
  items: EntityHasMediaResponseDto[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    example: {
      total: 100,
      page: 1,
      limit: 10,
      totalPages: 10,
    },
  })
  @Expose()
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

/**
 * DTO response cho việc xóa nhiều liên kết media
 */
export class BulkDeleteEntityHasMediaResponseDto {
  @ApiProperty({
    description: 'Danh sách kết quả xóa',
    type: [Object],
    example: [
      {
        mediaLinkId: 1,
        status: 'success',
        message: 'Xóa liên kết media thành công'
      },
      {
        mediaLinkId: 2,
        status: 'error',
        message: 'Không tìm thấy liên kết media'
      }
    ],
  })
  @Expose()
  results: Array<{
    mediaLinkId: number;
    status: 'success' | 'error';
    message: string;
  }>;

  @ApiProperty({
    description: 'Tổng số liên kết được xử lý',
    example: 2,
  })
  @Expose()
  totalProcessed: number;

  @ApiProperty({
    description: 'Số liên kết xóa thành công',
    example: 1,
  })
  @Expose()
  successCount: number;

  @ApiProperty({
    description: 'Số liên kết xóa thất bại',
    example: 1,
  })
  @Expose()
  failureCount: number;

  @ApiProperty({
    description: 'Thông báo tổng kết',
    example: 'Xóa thành công 1/2 liên kết media',
  })
  @Expose()
  message: string;
}
