import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAgentTemplateNewFields1734567890123 implements MigrationInterface {
  name = 'AddAgentTemplateNewFields1734567890123';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Thêm field conversion (array) vào bảng agents_template
    await queryRunner.query(`
      ALTER TABLE "agents_template" 
      ADD COLUMN "conversion" jsonb DEFAULT '[]'::jsonb
    `);

    // Thêm field model_system_id vào bảng agents_template
    await queryRunner.query(`
      ALTER TABLE "agents_template" 
      ADD COLUMN "model_system_id" uuid
    `);

    // Thêm field memories (array) vào bảng agents_template
    await queryRunner.query(`
      ALTER TABLE "agents_template" 
      ADD COLUMN "memories" jsonb DEFAULT '[]'::jsonb
    `);

    // Thêm comment cho các field mới
    await queryRunner.query(`
      COMMENT ON COLUMN "agents_template"."conversion" IS 'Cấu hình conversion mới dạng JSONB array'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "agents_template"."model_system_id" IS 'UUID tham chiếu đến bảng system_models'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "agents_template"."memories" IS 'Memories của agent dạng JSONB array'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa các field đã thêm
    await queryRunner.query(`
      ALTER TABLE "agents_template" 
      DROP COLUMN "memories"
    `);

    await queryRunner.query(`
      ALTER TABLE "agents_template" 
      DROP COLUMN "model_system_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "agents_template" 
      DROP COLUMN "conversion"
    `);
  }
}
