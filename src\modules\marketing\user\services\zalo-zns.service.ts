import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ILike } from 'typeorm';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom } from 'rxjs';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';
import { ZaloZnsService as SharedZaloZnsService } from '@shared/services/zalo/zalo-zns.service';
import { ZaloZnsInfoService } from '@shared/services/zalo/zalo-zns-info.service';
import { ZaloService } from './zalo.service';
import { ZaloTokenService } from './zalo-token.service';
import { ZaloZnsTemplate, ZaloZnsMessage } from '../entities';
import {
  ZnsTemplateQueryDto,
  ZnsMessageQueryDto,
  RegisterZnsTemplateDto,
  SendZnsMessageDto
} from '../dto/zalo';

/**
 * Service xử lý các chức năng liên quan đến Zalo ZNS (Zalo Notification Service)
 * Kết hợp giữa việc gọi Zalo API và quản lý dữ liệu trong database
 */
@Injectable()
export class ZaloZnsService {
  private readonly logger = new Logger(ZaloZnsService.name);

  constructor(
    @InjectRepository(ZaloZnsTemplate)
    private readonly zaloZnsTemplateRepository: Repository<ZaloZnsTemplate>,
    @InjectRepository(ZaloZnsMessage)
    private readonly zaloZnsMessageRepository: Repository<ZaloZnsMessage>,
    private readonly sharedZaloZnsService: SharedZaloZnsService,
    private readonly zaloZnsInfoService: ZaloZnsInfoService,
    private readonly zaloService: ZaloService,
    private readonly zaloTokenService: ZaloTokenService,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Kiểm tra trạng thái ZNS của Official Account
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns Trạng thái ZNS của Official Account
   */
  async getZnsStatus(userId: number, oaId: string): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED, 'Official Account chưa có access token');
      }

      // Gọi Zalo API để lấy trạng thái ZNS
      const znsStatus = await this.zaloZnsInfoService.getZnsStatus(accessToken);

      return znsStatus;
    } catch (error) {
      this.logger.error(`Failed to get ZNS status: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_API_ERROR, 'Lỗi khi lấy trạng thái ZNS');
    }
  }

  /**
   * Lấy thông tin quota ZNS của Official Account
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns Thông tin quota ZNS của Official Account
   */
  async getZnsQuota(userId: number, oaId: string): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED, 'Official Account chưa có access token');
      }

      // Gọi Zalo API để lấy thông tin quota ZNS
      const znsQuota = await this.zaloZnsInfoService.getZnsQuota(accessToken);

      return znsQuota;
    } catch (error) {
      this.logger.error(`Failed to get ZNS quota: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_API_ERROR, 'Lỗi khi lấy thông tin quota ZNS');
    }
  }

  /**
   * Lấy danh sách template ZNS từ database với phân trang
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param queryDto Tham số truy vấn
   * @returns Danh sách template ZNS với phân trang
   */
  async getZnsTemplates(
    userId: number, 
    oaId: string, 
    queryDto: ZnsTemplateQueryDto
  ): Promise<PaginatedResult<ZaloZnsTemplate>> {
    try {
      const { page, limit, search, sortBy, sortDirection, templateName, status } = queryDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: any = { userId, oaId };

      if (status && status !== 'all') {
        where.status = status;
      }

      if (search) {
        where.templateName = ILike(`%${search}%`);
      }

      if (templateName) {
        where.templateName = ILike(`%${templateName}%`);
      }

      // Tìm kiếm template ZNS
      const [items, totalItems] = await Promise.all([
        this.zaloZnsTemplateRepository.find({
          where,
          skip,
          take: limit,
          order: {
            [sortBy || 'createdAt']: sortDirection || 'DESC',
          },
        }),
        this.zaloZnsTemplateRepository.count({ where }),
      ]);

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get ZNS templates: ${error.message}`);
      throw new AppException(MARKETING_ERROR_CODES.ZNS_GET_TEMPLATES_FAILED, 'Không thể lấy danh sách template ZNS');
    }
  }

  /**
   * Lấy thông tin chi tiết template ZNS từ database
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param templateId ID của template
   * @returns Thông tin chi tiết template ZNS
   */
  async getZnsTemplateDetail(userId: number, oaId: string, id: number): Promise<ZaloZnsTemplate> {
    try {
      const template = await this.zaloZnsTemplateRepository.findOne({
        where: { id, userId, oaId },
      });

      if (!template) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_TEMPLATE_NOT_FOUND, 'Không tìm thấy template ZNS');
      }

      return template;
    } catch (error) {
      this.logger.error(`Failed to get ZNS template detail: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_GET_TEMPLATES_FAILED, 'Không thể lấy thông tin chi tiết template ZNS');
    }
  }

  /**
   * Lấy danh sách template ZNS từ Zalo API
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param queryDto Tham số truy vấn
   * @returns Danh sách template ZNS từ Zalo API với cấu trúc phân trang
   */
  async getZnsTemplatesFromZaloApi(
    userId: number,
    oaId: string,
    queryDto: ZnsTemplateQueryDto
  ): Promise<PaginatedResult<any>> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED, 'Official Account chưa có access token');
      }

      // Gọi Zalo API để lấy danh sách template
      const { page, limit, status } = queryDto;
      const offset = (page - 1) * limit;

      // Chuyển đổi status từ enum sang số theo yêu cầu của Zalo API
      let zaloStatus: number | undefined;
      if (status) {
        switch (status) {
          case 'approved':
            zaloStatus = 1; // Enable
            break;
          case 'pending':
            zaloStatus = 2; // Pending review
            break;
          case 'rejected':
            zaloStatus = 3; // Reject
            break;
          default:
            zaloStatus = undefined; // All statuses
        }
      }

      // Gọi Zalo API để lấy danh sách template
      const zaloResult = await this.sharedZaloZnsService.getZnsTemplates(
        accessToken,
        offset,
        limit,
        zaloStatus
      );

      // Chuyển đổi sang cấu trúc PaginatedResult
      const totalPages = Math.ceil(zaloResult.metadata.total / limit);

      return {
        items: zaloResult.data,
        meta: {
          totalItems: zaloResult.metadata.total,
          itemCount: zaloResult.data.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: queryDto.page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get ZNS templates from Zalo API: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_API_ERROR, 'Không thể lấy danh sách template ZNS từ Zalo API');
    }
  }

  /**
   * Lấy thông tin chi tiết template ZNS từ Zalo API
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param templateId ID của template từ Zalo
   * @returns Thông tin chi tiết template ZNS từ Zalo API
   */
  async getZnsTemplateDetailFromZaloApi(
    userId: number,
    oaId: string,
    templateId: string
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED, 'Official Account chưa có access token');
      }

      // Gọi Zalo API để lấy chi tiết template
      const templateDetail = await this.sharedZaloZnsService.getZnsTemplateDetail(
        accessToken,
        templateId
      );

      return templateDetail;
    } catch (error) {
      this.logger.error(`Failed to get ZNS template detail from Zalo API: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_API_ERROR, 'Không thể lấy thông tin chi tiết template ZNS từ Zalo API');
    }
  }

  /**
   * Lấy dữ liệu mẫu của template ZNS từ Zalo API
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param templateId ID của template từ Zalo
   * @returns Dữ liệu mẫu của template ZNS
   */
  async getZnsTemplateSampleData(
    userId: number,
    oaId: string,
    templateId: string
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED, 'Official Account chưa có access token');
      }

      // Gọi Zalo API để lấy dữ liệu mẫu của template
      const sampleData = await this.sharedZaloZnsService.getZnsTemplateSampleData(
        accessToken,
        templateId
      );

      return sampleData;
    } catch (error) {
      this.logger.error(`Failed to get ZNS template sample data: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_API_ERROR, 'Không thể lấy dữ liệu mẫu template ZNS');
    }
  }

  /**
   * Gọi Zalo API để tạo template ZNS trực tiếp theo đúng format
   * @param accessToken Access token của Official Account
   * @param templateData Dữ liệu template
   * @returns Kết quả từ Zalo API
   */
  private async createZnsTemplateDirectly(
    accessToken: string,
    templateData: any
  ): Promise<{ template_id: string; status: string; template_name: string; price: string }> {
    try {
      const url = 'https://business.openapi.zalo.me/template/create';
      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      // Transform data theo đúng format Zalo API
      const requestBody = {
        template_name: templateData.template_name,
        template_type: templateData.template_type,
        tag: templateData.tag, // Tag đã là string từ DTO
        layout: templateData.layout,
        tracking_id: templateData.tracking_id,
        ...(templateData.params && { params: templateData.params }),
        ...(templateData.note && { note: templateData.note }),
      };

      // Log request body để debug
      this.logger.debug('Zalo ZNS Template Create Request:', JSON.stringify(requestBody, null, 2));

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: {
            template_id: string;
            template_name: string;
            template_type: number;
            status: string;
            tag: number;
            app_id: string;
            oa_id: string;
            price: string;
            timeout: number;
            preview_url: string;
          };
        }>(url, requestBody, { headers }),
      );

      // Log response để debug
      this.logger.debug('Zalo ZNS Template Create Response:', JSON.stringify(response.data, null, 2));

      if (response.data.error !== 0) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_API_ERROR,
          `Lỗi khi tạo template ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_API_ERROR,
          'Lỗi khi tạo template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(`Error calling Zalo create template APIeate template API: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_API_ERROR, 'Lỗi khi gọi Zalo API tạo template');
    }
  }

  /**
   * Đăng ký template ZNS mới
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param registerDto Dữ liệu đăng ký template
   * @returns Template ZNS đã đăng ký
   */
  async registerZnsTemplate(
    userId: number,
    oaId: string,
    registerDto: RegisterZnsTemplateDto
  ): Promise<ZaloZnsTemplate> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED, 'Official Account chưa có access token');
      }

      // Gọi Zalo API để tạo template theo đúng format
      const createResult = await this.createZnsTemplateDirectly(accessToken, {
        template_name: registerDto.template_name,
        template_type: registerDto.template_type,
        tag: registerDto.tag,
        layout: registerDto.layout,
        tracking_id: registerDto.tracking_id,
        params: registerDto.params,
        note: registerDto.note,
      });

      // Lưu template vào database
      const now = Date.now();
      const template = this.zaloZnsTemplateRepository.create({
        userId,
        oaId,
        templateId: createResult.template_id,
        templateName: registerDto.template_name,
        templateContent: JSON.stringify(registerDto.layout), // Lưu layout dưới dạng JSON string
        templateType: registerDto.template_type.toString(),
        status: createResult.status,
        createdAt: now,
        updatedAt: now,
      });

      return await this.zaloZnsTemplateRepository.save(template);
    } catch (error) {
      this.logger.error(`Failed to register ZNS template: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_REGISTER_TEMPLATE_FAILED, 'Không thể đăng ký template ZNS');
    }
  }

  /**
   * Cập nhật trạng thái template ZNS
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param templateId ID của template
   * @param status Trạng thái mới
   * @returns Template ZNS đã cập nhật
   */
  async updateZnsTemplateStatus(
    userId: number,
    oaId: string,
    id: number,
    status: string
  ): Promise<ZaloZnsTemplate> {
    try {
      // Kiểm tra template tồn tại trước khi cập nhật
      await this.getZnsTemplateDetail(userId, oaId, id);

      // Cập nhật trạng thái
      await this.zaloZnsTemplateRepository.update(id, {
        status,
        updatedAt: Date.now(),
      });

      return await this.getZnsTemplateDetail(userId, oaId, id);
    } catch (error) {
      this.logger.error(`Failed to update ZNS template status: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_UPDATE_TEMPLATE_STATUS_FAILED, 'Không thể cập nhật trạng thái template ZNS');
    }
  }

  /**
   * Gửi tin nhắn ZNS
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param sendDto Dữ liệu gửi tin nhắn
   * @returns Kết quả gửi tin nhắn
   */
  async sendZnsMessage(
    userId: number,
    oaId: string,
    sendDto: SendZnsMessageDto
  ): Promise<ZaloZnsMessage> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED, 'Official Account chưa có access token');
      }

      // Kiểm tra template tồn tại
      const template = await this.zaloZnsTemplateRepository.findOne({
        where: { templateId: sendDto.templateId, oaId },
      });

      if (!template) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_TEMPLATE_NOT_FOUND, 'Không tìm thấy template ZNS');
      }

      if (template.status !== 'approved') {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_TEMPLATE_NOT_APPROVED, 'Template ZNS chưa được phê duyệt');
      }

      // Tạo tracking ID
      const trackingId = `zns_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

      // Gửi tin nhắn ZNS qua Zalo API
      const sendResult = await this.sharedZaloZnsService.sendZnsMessage(accessToken, {
        phone: sendDto.phone,
        template_id: sendDto.templateId,
        template_data: sendDto.templateData,
        tracking_id: trackingId,
      });

      // Lưu tin nhắn vào database
      const now = Date.now();
      const message = this.zaloZnsMessageRepository.create({
        userId,
        oaId,
        templateId: sendDto.templateId,
        phone: sendDto.phone,
        messageId: sendResult.message_id,
        trackingId,
        templateData: sendDto.templateData,
        status: 'pending',
        createdAt: now,
        updatedAt: now,
      });

      return await this.zaloZnsMessageRepository.save(message);
    } catch (error) {
      this.logger.error(`Failed to send ZNS message: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_SEND_MESSAGE_FAILED, 'Không thể gửi tin nhắn ZNS');
    }
  }

  /**
   * Lấy lịch sử tin nhắn ZNS
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param queryDto Tham số truy vấn
   * @returns Lịch sử tin nhắn ZNS với phân trang
   */
  async getZnsMessages(
    userId: number,
    oaId: string,
    queryDto: ZnsMessageQueryDto
  ): Promise<PaginatedResult<ZaloZnsMessage>> {
    try {
      const { page, limit, search, sortBy, sortDirection, phone, templateId, status } = queryDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: any = { userId, oaId };

      if (status && status !== 'all') {
        where.status = status;
      }

      if (templateId) {
        where.templateId = templateId;
      }

      if (phone) {
        where.phone = phone;
      }

      if (search) {
        where.phone = ILike(`%${search}%`);
      }

      // Tìm kiếm tin nhắn ZNS
      const [items, totalItems] = await Promise.all([
        this.zaloZnsMessageRepository.find({
          where,
          skip,
          take: limit,
          order: {
            [sortBy || 'createdAt']: sortDirection || 'DESC',
          },
        }),
        this.zaloZnsMessageRepository.count({ where }),
      ]);

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get ZNS messages: ${error.message}`);
      throw new AppException(MARKETING_ERROR_CODES.ZNS_GET_MESSAGES_FAILED, 'Không thể lấy lịch sử tin nhắn ZNS');
    }
  }

  /**
   * Lấy thông tin chi tiết tin nhắn ZNS
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của tin nhắn
   * @returns Thông tin chi tiết tin nhắn ZNS
   */
  async getZnsMessageDetail(userId: number, oaId: string, id: number): Promise<ZaloZnsMessage> {
    try {
      const message = await this.zaloZnsMessageRepository.findOne({
        where: { id, userId, oaId },
      });

      if (!message) {
        throw new AppException(MARKETING_ERROR_CODES.ZNS_MESSAGE_NOT_FOUND, 'Không tìm thấy tin nhắn ZNS');
      }

      return message;
    } catch (error) {
      this.logger.error(`Failed to get ZNS message detail: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MARKETING_ERROR_CODES.ZNS_GET_MESSAGES_FAILED, 'Không thể lấy thông tin chi tiết tin nhắn ZNS');
    }
  }
}
