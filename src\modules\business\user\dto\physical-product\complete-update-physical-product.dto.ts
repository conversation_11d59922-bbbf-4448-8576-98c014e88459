import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsNumber,
  IsArray,
  ValidateNested,
  IsObject,
  Min,
  MaxLength,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  ShipmentConfigType,
} from '@modules/business/interfaces';
import {
  PriceTypeEnum,
  EntityStatusEnum,
  ProductTypeEnum
} from '@modules/business/enums';
import { UpdateCustomerProductDto } from '../customer-product/update-customer-product.dto';
import { CustomFieldInputDto } from '../custom-field-metadata.dto';
import { HasPriceDto, StringPriceDto } from '../price.dto';
import { PhysicalProductVariantOperationDto } from './physical-product-variant-operation.dto';
import { ProductImageOperationDto } from '../image-operations/image-operation.dto';

/**
 * DTO cho thông tin cơ bản sản phẩm
 */
export class BasicInfoDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Á<PERSON> thun nam cao cấp - Phiên bản mới',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  name?: string;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Áo thun nam chất liệu cotton 100%, thiết kế hiện đại với logo mới',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductTypeEnum)
  productType?: ProductTypeEnum;

  @ApiProperty({
    description: 'Danh sách tags',
    type: [String],
    example: ['thời trang', 'nam', 'cotton', 'premium'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * DTO cho thông tin giá
 */
export class PricingInfoDto {
  @ApiProperty({
    description: 'Giá sản phẩm (HasPriceDto hoặc StringPriceDto)',
    example: {
      listPrice: 500000,
      salePrice: 450000,
      currency: 'VND'
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  price?: HasPriceDto | StringPriceDto;

  @ApiProperty({
    description: 'Loại giá sản phẩm',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
  })
  @IsOptional()
  @IsEnum(PriceTypeEnum)
  typePrice?: PriceTypeEnum;
}

/**
 * DTO cho thông tin vật lý sản phẩm
 */
export class PhysicalInfoDto {
  @ApiProperty({
    description: 'Số lượng tồn kho',
    example: 150,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  stockQuantity?: number;

  @ApiProperty({
    description: 'Mã SKU sản phẩm',
    example: 'SHIRT-001-V2',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  sku?: string;

  @ApiProperty({
    description: 'Mã vạch sản phẩm',
    example: '1234567890123',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  barcode?: string;

  @ApiProperty({
    description: 'Cấu hình vận chuyển',
    example: {
      widthCm: 25,
      heightCm: 5,
      lengthCm: 30,
      weightGram: 200
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  shipmentConfig?: ShipmentConfigType;
}

/**
 * DTO cho các thao tác
 */
export class OperationsDto {
  @ApiProperty({
    description: 'Danh sách thao tác với biến thể sản phẩm (ADD/UPDATE/DELETE)',
    type: [PhysicalProductVariantOperationDto],
    example: [
      {
        operation: 'add',
        data: {
          name: 'Áo thun đỏ size M',
          sku: 'SHIRT-RED-M-001',
          attributes: { color: 'Đỏ', size: 'M' },
          price: { listPrice: 300000, salePrice: 250000, currency: 'VND' },
          shipmentConfig: {
            widthCm: 20,
            heightCm: 3,
            lengthCm: 25,
            weightGram: 150
          },
          imageOperations: [
            {
              operation: 'add',
              mediaId: 'dcc8f803-04fc-49b3-953a-e660505ac353',
            },
            {
              operation: 'add',
              mediaId: 'e5e8c212-9c02-4677-b399-bb58d7c5077b',
            }
          ]
        }
      },
      {
        operation: 'update',
        id: 123,
        data: {
          name: 'Áo thun đỏ size L - Cập nhật',
          attributes: { color: 'Đỏ', size: 'L' },
          shipmentConfig: {
            widthCm: 22,
            heightCm: 3,
            lengthCm: 27,
            weightGram: 160
          },
          imageOperations: [
            {
              operation: 'add',
              mediaId: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2',
            },
            {
              operation: 'delete',
              entityMediaId: 789
            }
          ]
        }
      },
      {
        operation: 'delete',
        id: 124
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PhysicalProductVariantOperationDto)
  variants?: PhysicalProductVariantOperationDto[];

  @ApiProperty({
    description: 'Danh sách thao tác với hình ảnh sản phẩm (ADD/DELETE)',
    type: [ProductImageOperationDto],
    example: [
      {
        operation: 'add',
        mediaId: '789a0123-b45c-67d8-e901-234567890123',
      },
      {
        operation: 'delete',
        entityMediaId: 456
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductImageOperationDto)
  images?: ProductImageOperationDto[];
}

// Union type cho price (tương thích với entity)
export type ProductPriceType = HasPriceDto | StringPriceDto;

/**
 * DTO hoàn chỉnh cho việc cập nhật sản phẩm vật lý
 * Sử dụng cấu trúc nhóm theo chức năng để dễ đọc và maintain
 * Bao gồm tất cả thuộc tính từ:
 * - customer_products table (thông qua basicInfo và pricing)
 * - physical_products table (thông qua physicalInfo)
 * - physical_product_variants table (thông qua operations.variants)
 * - entity_has_media table (thông qua operations.images)
 */
export class CompleteUpdatePhysicalProductDto {
  // ========== THÔNG TIN CƠ BẢN ==========
  @ApiProperty({
    description: 'Thông tin cơ bản sản phẩm (tên, mô tả, loại, tags)',
    type: BasicInfoDto,
    example: {
      name: 'Áo thun nam cao cấp - Phiên bản mới',
      description: 'Áo thun nam chất liệu cotton 100%, thiết kế hiện đại với logo mới',
      productType: 'PHYSICAL',
      tags: ['thời trang', 'nam', 'cotton', 'premium']
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => BasicInfoDto)
  basicInfo?: BasicInfoDto;

  // ========== THÔNG TIN GIÁ ==========
  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    type: PricingInfoDto,
    example: {
      price: {
        listPrice: 500000,
        salePrice: 450000,
        currency: 'VND'
      },
      typePrice: 'HAS_PRICE'
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PricingInfoDto)
  pricing?: PricingInfoDto;

  // ========== THÔNG TIN VẬT LÝ ==========
  @ApiProperty({
    description: 'Thông tin vật lý sản phẩm (tồn kho, SKU, barcode, vận chuyển)',
    type: PhysicalInfoDto,
    example: {
      stockQuantity: 150,
      sku: 'SHIRT-001-V2',
      barcode: '1234567890123',
      shipmentConfig: {
        widthCm: 25,
        heightCm: 5,
        lengthCm: 30,
        weightGram: 200
      }
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PhysicalInfoDto)
  physicalInfo?: PhysicalInfoDto;

  // ========== CUSTOM FIELDS ==========
  @ApiProperty({
    description: 'Danh sách custom fields',
    type: [CustomFieldInputDto],
    example: [
      {
        customFieldId: 1,
        value: { value: 'XL' }
      },
      {
        customFieldId: 2,
        value: { value: 'Đỏ' }
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];

  // Trường status đã được loại bỏ vì người dùng không có quyền thay đổi
  // Status mặc định sẽ là PENDING khi tạo/sửa sản phẩm

  // ========== THAO TÁC ==========
  @ApiProperty({
    description: 'Các thao tác với biến thể và hình ảnh sản phẩm',
    type: OperationsDto,
    example: {
      variants: [
        {
          operation: 'add',
          data: {
            name: 'Áo thun đỏ size M',
            sku: 'SHIRT-RED-M-001',
            attributes: { color: 'Đỏ', size: 'M' },
            price: { listPrice: 300000, salePrice: 250000, currency: 'VND' },
            shipmentConfig: {
              widthCm: 20,
              heightCm: 3,
              lengthCm: 25,
              weightGram: 150
            },
            imageOperations: [
              {
                operation: 'add',
                mediaId: 'e5e8c212-9c02-4677-b399-bb58d7c5077b',
              }
            ]
          }
        },
        {
          operation: 'update',
          id: 123,
          data: {
            name: 'Áo thun đỏ size L - Cập nhật',
            attributes: { color: 'Đỏ', size: 'L' },
            shipmentConfig: {
              widthCm: 22,
              heightCm: 3,
              lengthCm: 27,
              weightGram: 160
            },
            imageOperations: [
              {
                operation: 'add',
                mediaId: 'dcc8f803-04fc-49b3-953a-e660505ac353',
              },
              {
                operation: 'delete',
                entityMediaId: 789
              }
            ]
          }
        },
        {
          operation: 'delete',
          id: 124
        }
      ],
      images: [
        {
          operation: 'add',
          mediaId: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2',
        },
        {
          operation: 'delete',
          entityMediaId: 456
        }
      ]
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => OperationsDto)
  operations?: OperationsDto;
}
