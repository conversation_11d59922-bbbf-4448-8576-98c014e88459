/**
 * Interface định nghĩa cấu trúc phản hồi chuẩn từ Zalo API
 */
export interface ZaloResponse<T> {
  /**
   * Mã trạng thái phản hồi
   */
  error: number;

  /**
   * Thông báo lỗi (nếu có)
   */
  message: string;

  /**
   * Dữ liệu phản hồi
   */
  data?: T;
}

/**
 * Interface định nghĩa thông tin access token
 */
export interface ZaloAccessToken {
  /**
   * Access token
   */
  access_token: string;

  /**
   * Thời gian hết hạn (tính bằng giây)
   */
  expires_in: number;

  /**
   * Refresh token (nếu có)
   */
  refresh_token?: string;
}

/**
 * Interface định nghĩa phản hồi khi làm mới access token từ refresh token
 */
export interface ZaloRefreshTokenResponse {
  /**
   * Access token mới
   */
  access_token: string;

  /**
   * Thời gian hết hạn của access token (tính bằng giây)
   */
  expires_in: number;

  /**
   * Refresh token mới (nếu có)
   */
  refresh_token?: string;
}

/**
 * Interface định nghĩa thông tin chi tiết người dùng từ Social API
 */
export interface ZaloSocialUserInfo {
  /**
   * ID của người dùng Zalo
   */
  id: string;

  /**
   * Tên hiển thị của người dùng
   */
  name: string;

  /**
   * URL avatar của người dùng
   */
  picture: {
    data: {
      url: string;
    };
  };

  /**
   * Giới tính của người dùng
   */
  gender?: string;

  /**
   * Ngày sinh của người dùng
   */
  birthday?: string;

  /**
   * Địa chỉ của người dùng (nếu được cấp quyền)
   */
  location?: {
    name: string;
  };
}

/**
 * Interface định nghĩa thông tin Official Account
 */
export interface ZaloOaInfo {
  /**
   * ID của Official Account
   */
  oa_id: string;

  /**
   * Tên của Official Account
   */
  name: string;

  /**
   * Mô tả của Official Account
   */
  description: string;

  /**
   * URL avatar của Official Account
   */
  avatar: string;

  /**
   * Trạng thái của Official Account
   */
  status: string;
}

/**
 * Interface định nghĩa thông tin hạn mức tin nhắn OA
 * Endpoint: GET https://openapi.zalo.me/v2.0/oa/quota
 * Điều kiện: Access token hợp lệ của Official Account
 */
export interface ZaloMessageQuota {
  /**
   * Hạn mức tin nhắn hàng ngày
   */
  daily_quota: number;

  /**
   * Số tin nhắn còn lại trong ngày
   */
  remaining_quota: number;

  /**
   * Loại quota (ví dụ: "free", "paid")
   */
  quota_type: string;

  /**
   * Thời gian reset quota (Unix timestamp)
   */
  reset_time: number;
}

/**
 * Interface định nghĩa yêu cầu lấy quota message chi tiết
 * Endpoint: POST https://openapi.zalo.me/v3.0/oa/quota/message
 * Điều kiện: Access token hợp lệ của Official Account
 */
export interface ZaloQuotaMessageRequest {
  /**
   * Thực thể sở hữu Quota muốn truy xuất OA / APP
   */
  quota_owner: string;

  /**
   * Loại sản phẩm thuộc nhánh tính năng muốn truy xuất
   * cs: tin Tư vấn
   * transaction: tin Giao dịch
   */
  product_type?: 'cs' | 'transaction';

  /**
   * Loại quota (nguồn quota sử dụng) muốn truy xuất
   * sub_quota: quota tin theo gói dịch vụ OA
   * purchase_quota: quota tin mua lẻ
   * reward_quota: quota tin được tặng từ các chương trình khuyến mãi
   */
  quota_type?: 'sub_quota' | 'purchase_quota' | 'reward_quota';
}

/**
 * Interface định nghĩa thông tin chi tiết quota message
 */
export interface ZaloQuotaMessageAsset {
  /**
   * ID của asset
   */
  asset_id: string;

  /**
   * Loại sản phẩm thuộc nhánh tính năng
   */
  product_type: string;

  /**
   * Loại quota (nguồn quota sử dụng)
   */
  quota_type: string;

  /**
   * Ngày hết hạn của asset_id
   */
  valid_through: string;

  /**
   * Tổng số lượng quota CHƯA hết hạn (bao gồm available + used) của asset_id
   */
  total: number;

  /**
   * Số lượng quota ở trạng thái available của asset_id
   */
  remain: number;
}

/**
 * Interface định nghĩa phản hồi quota message chi tiết
 * Endpoint: POST https://openapi.zalo.me/v3.0/oa/quota/message
 */
export interface ZaloQuotaMessageResponse {
  /**
   * Danh sách thông tin quota message
   */
  data: ZaloQuotaMessageAsset[];

  /**
   * Mã lỗi (0 = thành công)
   */
  error: number;

  /**
   * Thông báo kết quả
   */
  message: string;
}

/**
 * Interface định nghĩa thông tin chi tiết Official Account
 * Endpoint: GET https://openapi.zalo.me/v2.0/oa/getoa
 * Điều kiện: Access token hợp lệ của Official Account
 */
export interface ZaloDetailedOaInfo extends ZaloOaInfo {
  /**
   * Số lượng người theo dõi
   */
  num_follower: number;

  /**
   * Loại Official Account
   */
  oa_type: string;

  /**
   * Trạng thái xác thực
   */
  is_verified: boolean;

  /**
   * Thông tin liên hệ
   */
  contact_info?: {
    /**
     * Địa ch�i
     */
    address?: string;

    /**
     * Số điện thoại
     */
    phone?: string;

    /**
     * Email
     */
    email?: string;

    /**
     * Website
     */
    website?: string;
  };

  /**
   * Thông tin ví ZCA (nếu có)
   */
  zca_info?: {
    /**
     * Trạng thái liên kết ví
     */
    is_linked: boolean;

    /**
     * Số dư ví (VND)
     */
    balance?: number;
  };

  /**
   * Thời gian tạo OA (Unix timestamp)
   */
  created_time?: number;
}

/**
 * Interface định nghĩa thông tin người dùng Zalo
 */
export interface ZaloUserInfo {
  /**
   * ID của người dùng Zalo
   */
  id: string;

  /**
   * Tên hiển thị của người dùng
   */
  display_name: string;

  /**
   * URL avatar của người dùng
   */
  avatar: string;

  /**
   * Giới tính của người dùng (1: Nam, 2: Nữ)
   */
  gender?: number;

  /**
   * Ngày sinh của người dùng (định dạng dd/mm/yyyy)
   */
  birth_date?: string;

  /**
   * Số điện thoại của người dùng (nếu được cấp quyền)
   */
  phone?: string;
}

/**
 * Interface định nghĩa nhãn người dùng
 */
export interface ZaloUserLabel {
  /**
   * ID của nhãn
   */
  label_id: string;

  /**
   * Tên nhãn
   */
  label_name: string;

  /**
   * Mô tả nhãn
   */
  description?: string;

  /**
   * Màu sắc nhãn (hex color)
   */
  color?: string;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  created_time?: number;

  /**
   * Số lượng người dùng có nhãn này
   */
  user_count?: number;
}

/**
 * Interface định nghĩa danh sách nhãn
 */
export interface ZaloUserLabelList {
  /**
   * Danh sách nhãn
   */
  labels: ZaloUserLabel[];

  /**
   * Tổng số nhãn
   */
  total: number;
}

/**
 * Interface định nghĩa yêu cầu tạo nhãn mới
 */
export interface ZaloCreateLabelRequest {
  /**
   * Tên nhãn
   */
  label_name: string;

  /**
   * Mô tả nhãn (tùy chọn)
   */
  description?: string;

  /**
   * Màu sắc nhãn (hex color, tùy chọn)
   */
  color?: string;
}

/**
 * Interface định nghĩa yêu cầu gắn/gỡ nhãn cho người dùng
 */
export interface ZaloUserLabelRequest {
  /**
   * ID của người dùng
   */
  user_id: string;

  /**
   * ID của nhãn
   */
  label_id: string;
}

/**
 * Interface định nghĩa danh sách người dùng
 */
export interface ZaloUserList {
  /**
   * Danh sách người dùng
   */
  users: ZaloUserInfo[];

  /**
   * Tổng số người dùng
   */
  total: number;

  /**
   * Có còn dữ liệu tiếp theo không
   */
  has_more: boolean;

  /**
   * Offset tiếp theo
   */
  next_offset?: number;
}

/**
 * Interface định nghĩa yêu cầu lấy danh sách người dùng theo API v3.0
 * Endpoint: GET https://openapi.zalo.me/v3.0/oa/user/getlist
 */
export interface ZaloUserGetListRequest {
  /**
   * Thứ tự của người dùng đầu tiên trong danh sách trả về
   * Hỗ trợ tối đa 9951 (tương ứng 10000 người dùng, 50 người dùng/request)
   */
  offset: number;

  /**
   * Số lượng người dùng muốn lấy (tối đa 50 người dùng 1 request)
   */
  count: number;

  /**
   * Tên của nhãn được gắn cho người dùng (tùy chọn)
   * Khi sử dụng tham số này, API sẽ trả về danh sách người dùng theo nhãn tương ứng
   */
  tag_name?: string;

  /**
   * Khoảng thời gian tương tác gần nhất của người dùng (tùy chọn)
   * TODAY: trong ngày hôm nay
   * YESTERDAY: trong ngày hôm qua
   * L7D: trong 7 ngày vừa qua, không bao gồm ngày hiện tại
   * L30D: trong 30 ngày vừa qua, không bao gồm ngày hiện tại
   * <YYYY_MM_DD:YYYY_MM_DD>: trong khoảng thời gian tùy biến
   */
  last_interaction_period?: 'TODAY' | 'YESTERDAY' | 'L7D' | 'L30D' | string;

  /**
   * Trạng thái quan tâm OA của người dùng (tùy chọn)
   * true: Người dùng đang quan tâm OA
   * false: Người dùng chưa quan tâm OA
   */
  is_follower?: boolean;
}

/**
 * Interface định nghĩa thông tin người dùng trong response getlist
 */
export interface ZaloUserGetListItem {
  /**
   * ID của người dùng Zalo
   */
  user_id: string;
}

/**
 * Interface định nghĩa phản hồi API getlist
 */
export interface ZaloUserGetListResponse {
  /**
   * Dữ liệu danh sách người dùng
   */
  data: {
    /**
     * Tổng số lượng người dùng thỏa điều kiện
     */
    total: number;

    /**
     * Số lượng người dùng đã request
     */
    count: number;

    /**
     * Thứ tự của người dùng đầu tiên trong danh sách trả về
     */
    offset: number;

    /**
     * Danh sách ID của tất cả người dùng thỏa điều kiện truyền vào
     */
    users: ZaloUserGetListItem[];
  };

  /**
   * Mã lỗi (0 = thành công)
   */
  error: number;

  /**
   * Thông báo kết quả
   */
  message: string;
}

/**
 * Interface định nghĩa yêu cầu cập nhật thông tin người dùng
 */
export interface ZaloUpdateUserRequest {
  /**
   * ID của người dùng
   */
  user_id: string;

  /**
   * Tên hiển thị mới (tùy chọn)
   */
  display_name?: string;

  /**
   * Ghi chú về người dùng (tùy chọn)
   */
  note?: string;

  /**
   * Thông tin bổ sung (tùy chọn)
   */
  metadata?: Record<string, any>;
}

/**
 * Interface định nghĩa thông tin tùy biến của người dùng
 */
export interface ZaloUserCustomInfo {
  /**
   * ID của người dùng
   */
  user_id: string;

  /**
   * Thông tin tùy biến (key-value pairs)
   */
  custom_fields: Record<string, any>;

  /**
   * Thời gian cập nhật cuối cùng (Unix timestamp)
   */
  last_updated?: number;
}

/**
 * Interface định nghĩa yêu cầu cập nhật thông tin tùy biến
 */
export interface ZaloUpdateCustomInfoRequest {
  /**
   * ID của người dùng
   */
  user_id: string;

  /**
   * Thông tin tùy biến cần cập nhật
   */
  custom_fields: Record<string, any>;
}

/**
 * Enum cho các kiểu dữ liệu trường thông tin
 */
export enum ZaloUserInfoFieldType {
  TEXT = 'text',
  NUMBER = 'number',
  DATE = 'date',
  SELECT = 'select',
}

/**
 * Interface định nghĩa tùy chọn của trường select
 */
export interface ZaloFieldOption {
  /**
   * Giá trị của tùy chọn
   */
  value: string;

  /**
   * Nhãn hiển thị của tùy chọn
   */
  label: string;
}

/**
 * Interface định nghĩa trường thông tin người dùng
 */
export interface ZaloUserInfoField {
  /**
   * ID của trường thông tin
   */
  field_id: string;

  /**
   * Tên trường thông tin
   */
  field_name: string;

  /**
   * Kiểu dữ liệu của trường
   */
  field_type: ZaloUserInfoFieldType;

  /**
   * Mô tả trường thông tin
   */
  description?: string;

  /**
   * Trường có bắt buộc không
   */
  required?: boolean;

  /**
   * Danh sách tùy chọn (chỉ áp dụng cho kiểu select)
   */
  options?: ZaloFieldOption[];

  /**
   * Giá trị mặc định
   */
  default_value?: string;

  /**
   * Thứ tự hiển thị
   */
  display_order?: number;

  /**
   * Trường có phải là trường hệ thống không
   */
  is_system_field?: boolean;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  created_time?: number;

  /**
   * Thời gian cập nhật cuối (Unix timestamp)
   */
  updated_time?: number;
}

/**
 * Interface định nghĩa danh sách trường thông tin
 */
export interface ZaloUserInfoFieldList {
  /**
   * Danh sách trường thông tin
   */
  fields: ZaloUserInfoField[];

  /**
   * Tổng số trường thông tin
   */
  total: number;

  /**
   * Có còn dữ liệu tiếp theo không
   */
  has_more: boolean;

  /**
   * Offset tiếp theo
   */
  next_offset?: number;
}

/**
 * Interface định nghĩa yêu cầu tạo trường thông tin tùy biến
 */
export interface ZaloCreateUserInfoFieldRequest {
  /**
   * Tên trường thông tin
   */
  field_name: string;

  /**
   * Kiểu dữ liệu của trường
   */
  field_type: ZaloUserInfoFieldType;

  /**
   * Mô tả trường thông tin
   */
  description?: string;

  /**
   * Trường có bắt buộc không
   */
  required?: boolean;

  /**
   * Danh sách tùy chọn (chỉ áp dụng cho kiểu select)
   */
  options?: ZaloFieldOption[];

  /**
   * Giá trị mặc định
   */
  default_value?: string;

  /**
   * Thứ tự hiển thị
   */
  display_order?: number;
}

/**
 * Interface định nghĩa yêu cầu cập nhật trường thông tin
 */
export interface ZaloUpdateUserInfoFieldRequest {
  /**
   * Tên trường thông tin
   */
  field_name?: string;

  /**
   * Mô tả trường thông tin
   */
  description?: string;

  /**
   * Trường có bắt buộc không
   */
  required?: boolean;

  /**
   * Danh sách tùy chọn (chỉ áp dụng cho kiểu select)
   */
  options?: ZaloFieldOption[];

  /**
   * Giá trị mặc định
   */
  default_value?: string;

  /**
   * Thứ tự hiển thị
   */
  display_order?: number;
}

/**
 * Interface định nghĩa tin nhắn văn bản
 */
export interface ZaloTextMessage {
  /**
   * Loại tin nhắn (text)
   */
  type: 'text';

  /**
   * Nội dung tin nhắn
   */
  text: string;
}

/**
 * Interface định nghĩa tin nhắn hình ảnh
 */
export interface ZaloImageMessage {
  /**
   * Loại tin nhắn (image)
   */
  type: 'image';

  /**
   * URL của hình ảnh
   */
  url: string;

  /**
   * Chú thích cho hình ảnh (nếu có)
   */
  caption?: string;
}

/**
 * Interface định nghĩa tin nhắn tệp đính kèm
 */
export interface ZaloFileMessage {
  /**
   * Loại tin nhắn (file)
   */
  type: 'file';

  /**
   * URL của tệp đính kèm
   */
  url: string;

  /**
   * Tên của tệp đính kèm
   */
  name: string;
}

/**
 * Interface định nghĩa tin nhắn template
 */
export interface ZaloTemplateMessage {
  /**
   * Loại tin nhắn (template)
   */
  type: 'template';

  /**
   * ID của template
   */
  template_id: string;

  /**
   * Dữ liệu cho template
   */
  template_data: Record<string, any>;
}

/**
 * Interface định nghĩa tin nhắn sticker
 */
export interface ZaloStickerMessage {
  /**
   * Loại tin nhắn (sticker)
   */
  type: 'sticker';

  /**
   * ID của sticker
   */
  sticker_id: string;
}

/**
 * Interface định nghĩa tin nhắn tư vấn văn bản
 */
export interface ZaloConsultationTextMessage {
  /**
   * Loại tin nhắn (consultation_text)
   */
  type: 'consultation_text';

  /**
   * Nội dung tin nhắn
   */
  text: string;
}

/**
 * Interface định nghĩa tin nhắn tư vấn đính kèm ảnh
 */
export interface ZaloConsultationImageMessage {
  /**
   * Loại tin nhắn (consultation_image)
   */
  type: 'consultation_image';

  /**
   * URL của hình ảnh
   */
  url: string;

  /**
   * Nội dung tin nhắn kèm theo
   */
  message?: string;
}

/**
 * Interface định nghĩa tin nhắn tư vấn trích dẫn
 */
export interface ZaloConsultationQuoteMessage {
  /**
   * Loại tin nhắn (consultation_quote)
   */
  type: 'consultation_quote';

  /**
   * Nội dung tin nhắn
   */
  text: string;

  /**
   * Thông tin tin nhắn được trích dẫn
   */
  quote: {
    /**
     * ID của tin nhắn được trích dẫn
     */
    message_id: string;

    /**
     * Nội dung tin nhắn được trích dẫn
     */
    content: string;
  };
}

/**
 * Interface định nghĩa tin nhắn tư vấn kèm sticker
 */
export interface ZaloConsultationStickerMessage {
  /**
   * Loại tin nhắn (consultation_sticker)
   */
  type: 'consultation_sticker';

  /**
   * ID của sticker
   */
  sticker_id: string;

  /**
   * Nội dung tin nhắn kèm theo (nếu có)
   */
  message?: string;
}

/**
 * Interface định nghĩa tin nhắn tư vấn đính kèm file
 */
export interface ZaloConsultationFileMessage {
  /**
   * Loại tin nhắn (consultation_file)
   */
  type: 'consultation_file';

  /**
   * URL của file đính kèm
   */
  url: string;

  /**
   * Tên file
   */
  filename: string;

  /**
   * Nội dung tin nhắn kèm theo (nếu có)
   */
  message?: string;
}

/**
 * Interface định nghĩa tin nhắn tư vấn yêu cầu thông tin người dùng
 */
export interface ZaloConsultationRequestInfoMessage {
  /**
   * Loại tin nhắn (consultation_request_info)
   */
  type: 'consultation_request_info';

  /**
   * Tiêu đề của form yêu cầu thông tin
   */
  title: string;

  /**
   * Mô tả ngắn gọn về form
   */
  subtitle?: string;

  /**
   * URL hình ảnh đại diện (nếu có)
   */
  image_url?: string;

  /**
   * Danh sách các trường thông tin cần thu thập
   */
  elements: Array<{
    /**
     * Tiêu đề của trường
     */
    title: string;

    /**
     * Loại input (text, phone, email, date)
     */
    type: 'text' | 'phone' | 'email' | 'date';

    /**
     * Có bắt buộc nhập không
     */
    required?: boolean;

    /**
     * Placeholder text
     */
    placeholder?: string;
  }>;
}

/**
 * Interface định nghĩa tin nhắn văn bản đến người dùng ẩn danh
 */
export interface ZaloAnonymousTextMessage {
  /**
   * Loại tin nhắn (anonymous_text)
   */
  type: 'anonymous_text';

  /**
   * Nội dung tin nhắn
   */
  text: string;
}

/**
 * Interface định nghĩa tin nhắn ảnh đến người dùng ẩn danh
 */
export interface ZaloAnonymousImageMessage {
  /**
   * Loại tin nhắn (anonymous_image)
   */
  type: 'anonymous_image';

  /**
   * URL của hình ảnh
   */
  url: string;

  /**
   * Nội dung tin nhắn kèm theo (nếu có)
   */
  message?: string;
}

/**
 * Interface định nghĩa tin nhắn file đến người dùng ẩn danh
 */
export interface ZaloAnonymousFileMessage {
  /**
   * Loại tin nhắn (anonymous_file)
   */
  type: 'anonymous_file';

  /**
   * URL của file đính kèm
   */
  url: string;

  /**
   * Tên file
   */
  filename: string;

  /**
   * Nội dung tin nhắn kèm theo (nếu có)
   */
  message?: string;
}

/**
 * Interface định nghĩa tin nhắn sticker đến người dùng ẩn danh
 */
export interface ZaloAnonymousStickerMessage {
  /**
   * Loại tin nhắn (anonymous_sticker)
   */
  type: 'anonymous_sticker';

  /**
   * ID của sticker
   */
  sticker_id: string;

  /**
   * Nội dung tin nhắn kèm theo (nếu có)
   */
  message?: string;
}

/**
 * Interface định nghĩa thả biểu tượng cảm xúc vào tin nhắn
 */
export interface ZaloReactionMessage {
  /**
   * Loại tin nhắn (reaction)
   */
  type: 'reaction';

  /**
   * ID của tin nhắn cần thả cảm xúc
   */
  message_id: string;

  /**
   * Loại biểu tượng cảm xúc (heart, like, haha, wow, sad, angry)
   */
  reaction_type: 'heart' | 'like' | 'haha' | 'wow' | 'sad' | 'angry';
}

/**
 * Interface định nghĩa tin nhắn miniapp
 */
export interface ZaloMiniAppMessage {
  /**
   * Loại tin nhắn (miniapp)
   */
  type: 'miniapp';

  /**
   * ID của miniapp
   */
  app_id: string;

  /**
   * Tiêu đề tin nhắn
   */
  title: string;

  /**
   * Mô tả ngắn gọn
   */
  subtitle?: string;

  /**
   * URL hình ảnh đại diện
   */
  image_url?: string;

  /**
   * Dữ liệu truyền vào miniapp
   */
  data?: Record<string, any>;
}

/**
 * Union type cho các loại tin nhắn
 */
export type ZaloMessage = ZaloTextMessage | ZaloImageMessage | ZaloFileMessage | ZaloTemplateMessage | ZaloStickerMessage;

/**
 * Union type cho các loại tin nhắn tư vấn
 */
export type ZaloConsultationMessage =
  | ZaloConsultationTextMessage
  | ZaloConsultationImageMessage
  | ZaloConsultationQuoteMessage
  | ZaloConsultationStickerMessage
  | ZaloConsultationFileMessage
  | ZaloConsultationRequestInfoMessage;

/**
 * Union type cho các loại tin nhắn đến người dùng ẩn danh
 */
export type ZaloAnonymousMessage =
  | ZaloAnonymousTextMessage
  | ZaloAnonymousImageMessage
  | ZaloAnonymousFileMessage
  | ZaloAnonymousStickerMessage;

/**
 * Union type cho các loại tin nhắn khác
 */
export type ZaloOtherMessage =
  | ZaloReactionMessage
  | ZaloMiniAppMessage;

/**
 * Interface định nghĩa thông tin hạn mức gửi tin nhắn
 */
export interface ZaloQuotaInfo {
  /**
   * Hạn mức gửi tin nhắn trong ngày
   */
  daily_quota: number;

  /**
   * Số tin nhắn đã gửi trong ngày
   */
  daily_sent: number;

  /**
   * Số tin nhắn còn lại có thể gửi trong ngày
   */
  daily_remaining: number;

  /**
   * Hạn mức gửi tin nhắn trong tháng
   */
  monthly_quota: number;

  /**
   * Số tin nhắn đã gửi trong tháng
   */
  monthly_sent: number;

  /**
   * Số tin nhắn còn lại có thể gửi trong tháng
   */
  monthly_remaining: number;
}

/**
 * Interface định nghĩa thông tin tin nhắn trong hội thoại
 */
export interface ZaloConversationMessage {
  /**
   * ID của tin nhắn
   */
  message_id: string;

  /**
   * ID của người gửi
   */
  from_id: string;

  /**
   * ID của người nhận
   */
  to_id: string;

  /**
   * Thời gian gửi tin nhắn (Unix timestamp)
   */
  timestamp: number;

  /**
   * Loại tin nhắn
   */
  type: 'text' | 'image' | 'file' | 'sticker' | 'template' | 'location' | 'audio';

  /**
   * Nội dung tin nhắn
   */
  message: any;

  /**
   * Trạng thái tin nhắn
   */
  status: 'sent' | 'delivered' | 'read' | 'failed';
}

/**
 * Interface định nghĩa thông tin hội thoại
 */
export interface ZaloConversation {
  /**
   * ID của hội thoại
   */
  conversation_id: string;

  /**
   * ID của người dùng
   */
  user_id: string;

  /**
   * Tin nhắn cuối cùng
   */
  last_message: ZaloConversationMessage;

  /**
   * Số tin nhắn chưa đọc
   */
  unread_count: number;

  /**
   * Thời gian cập nhật cuối cùng
   */
  last_updated: number;
}

/**
 * Interface định nghĩa kết quả upload file
 */
export interface ZaloUploadResult {
  /**
   * URL của file đã upload
   */
  url: string;

  /**
   * ID của attachment (nếu có)
   */
  attachment_id?: string;

  /**
   * Kích thước file (bytes)
   */
  size?: number;

  /**
   * Loại file
   */
  type?: string;

  /**
   * Thời gian hết hạn của URL (Unix timestamp)
   */
  expires_at?: number;
}

/**
 * Interface định nghĩa thông tin upload file
 */
export interface ZaloUploadFileInfo {
  /**
   * Tên file
   */
  filename: string;

  /**
   * Dữ liệu file (Buffer hoặc base64)
   */
  data: Buffer | string;

  /**
   * MIME type của file
   */
  mimetype: string;

  /**
   * Kích thước file (bytes)
   */
  size: number;
}

/**
 * Interface định nghĩa tham số phân trang cho tin nhắn
 */
export interface ZaloMessagePagination {
  /**
   * Số lượng tin nhắn tối đa trả về (1-100)
   */
  count?: number;

  /**
   * Offset để phân trang
   */
  offset?: number;

  /**
   * ID tin nhắn để lấy tin nhắn trước đó
   */
  before?: string;

  /**
   * ID tin nhắn để lấy tin nhắn sau đó
   */
  after?: string;
}

/**
 * Interface định nghĩa tin nhắn giao dịch
 */
export interface ZaloTransactionMessage {
  /**
   * Loại tin nhắn (transaction)
   */
  type: 'transaction';

  /**
   * ID của template giao dịch
   */
  template_id: string;

  /**
   * Dữ liệu cho template
   */
  template_data: Record<string, string>;

  /**
   * Chế độ gửi tin nhắn
   */
  mode?: 'development' | 'production';
}

/**
 * Interface định nghĩa thông tin đơn hàng
 */
export interface ZaloOrderData {
  /**
   * ID đơn hàng
   */
  orderId: string;

  /**
   * Ngày đặt hàng
   */
  orderDate: string;

  /**
   * Tên khách hàng
   */
  customerName: string;

  /**
   * Tổng tiền
   */
  totalAmount: string;

  /**
   * Phương thức thanh toán
   */
  paymentMethod: string;

  /**
   * Địa chỉ giao hàng (tùy chọn)
   */
  deliveryAddress?: string;

  /**
   * Thời gian giao hàng dự kiến (tùy chọn)
   */
  estimatedDelivery?: string;
}

/**
 * Interface định nghĩa thông tin giao hàng
 */
export interface ZaloDeliveryData {
  /**
   * ID đơn hàng
   */
  orderId: string;

  /**
   * Trạng thái giao hàng
   */
  status: string;

  /**
   * Mã vận đơn (tùy chọn)
   */
  trackingNumber?: string;

  /**
   * Thời gian giao hàng dự kiến (tùy chọn)
   */
  estimatedDelivery?: string;

  /**
   * Ghi chú giao hàng (tùy chọn)
   */
  deliveryNote?: string;
}

/**
 * Interface định nghĩa thông tin thanh toán
 */
export interface ZaloPaymentData {
  /**
   * ID đơn hàng
   */
  orderId: string;

  /**
   * Số tiền
   */
  amount: string;

  /**
   * Phương thức thanh toán
   */
  paymentMethod: string;

  /**
   * Ngày thanh toán
   */
  paymentDate: string;

  /**
   * Trạng thái thanh toán
   */
  status: 'success' | 'failed' | 'pending';

  /**
   * ID giao dịch (tùy chọn)
   */
  transactionId?: string;
}

/**
 * Interface định nghĩa tin nhắn truyền thông
 */
export interface ZaloPromotionMessage {
  /**
   * Loại tin nhắn (promotion)
   */
  type: 'promotion';

  /**
   * ID của template truyền thông
   */
  template_id: string;

  /**
   * Dữ liệu cho template
   */
  template_data: Record<string, string>;

  /**
   * Chế độ gửi tin nhắn
   */
  mode?: 'development' | 'production';
}

/**
 * Interface định nghĩa thông tin khuyến mãi
 */
export interface ZaloPromotionData {
  /**
   * Tiêu đề khuyến mãi
   */
  title: string;

  /**
   * Mô tả khuyến mãi
   */
  description: string;

  /**
   * Phần trăm giảm giá (tùy chọn)
   */
  discountPercent?: string;

  /**
   * Giá gốc (tùy chọn)
   */
  originalPrice?: string;

  /**
   * Giá khuyến mãi (tùy chọn)
   */
  salePrice?: string;

  /**
   * Thời hạn khuyến mãi (tùy chọn)
   */
  validUntil?: string;

  /**
   * Mã khuyến mãi (tùy chọn)
   */
  promoCode?: string;

  /**
   * URL hình ảnh (tùy chọn)
   */
  imageUrl?: string;

  /**
   * URL hành động (tùy chọn)
   */
  actionUrl?: string;
}

/**
 * Interface định nghĩa thông tin sự kiện
 */
export interface ZaloEventData {
  /**
   * Tên sự kiện
   */
  eventName: string;

  /**
   * Ngày sự kiện
   */
  eventDate: string;

  /**
   * Thời gian sự kiện (tùy chọn)
   */
  eventTime?: string;

  /**
   * Địa điểm (tùy chọn)
   */
  location?: string;

  /**
   * Mô tả sự kiện (tùy chọn)
   */
  description?: string;

  /**
   * URL hình ảnh (tùy chọn)
   */
  imageUrl?: string;
}

// ===== ZALO GROUP MESSAGE FRAMEWORK (GMF) INTERFACES =====

/**
 * Interface định nghĩa tin nhắn nhóm văn bản
 */
export interface ZaloGroupTextMessage {
  /**
   * Loại tin nhắn (group_text)
   */
  type: 'group_text';

  /**
   * Nội dung tin nhắn
   */
  text: string;
}

/**
 * Interface định nghĩa tin nhắn nhóm hình ảnh
 */
export interface ZaloGroupImageMessage {
  /**
   * Loại tin nhắn (group_image)
   */
  type: 'group_image';

  /**
   * URL của hình ảnh đã upload
   */
  url: string;

  /**
   * Chú thích cho hình ảnh (tùy chọn)
   */
  caption?: string;
}

/**
 * Interface định nghĩa tin nhắn nhóm file
 */
export interface ZaloGroupFileMessage {
  /**
   * Loại tin nhắn (group_file)
   */
  type: 'group_file';

  /**
   * URL của file đã upload
   */
  url: string;

  /**
   * Tên file
   */
  filename: string;
}

/**
 * Interface định nghĩa tin nhắn nhóm sticker
 */
export interface ZaloGroupStickerMessage {
  /**
   * Loại tin nhắn (group_sticker)
   */
  type: 'group_sticker';

  /**
   * ID của sticker
   */
  sticker_id: string;
}

/**
 * Interface định nghĩa tin nhắn nhóm mention
 */
export interface ZaloGroupMentionMessage {
  /**
   * Loại tin nhắn (group_mention)
   */
  type: 'group_mention';

  /**
   * Nội dung tin nhắn
   */
  text: string;

  /**
   * Danh sách user ID được mention
   */
  mention_uids: string[];
}

/**
 * Union type cho các loại tin nhắn nhóm GMF
 */
export type ZaloGroupMessage =
  | ZaloGroupTextMessage
  | ZaloGroupImageMessage
  | ZaloGroupFileMessage
  | ZaloGroupStickerMessage
  | ZaloGroupMentionMessage;

/**
 * Interface định nghĩa thông tin nhóm chat
 */
export interface ZaloGroupInfo {
  /**
   * ID của nhóm chat
   */
  group_id: string;

  /**
   * Tên nhóm
   */
  group_name: string;

  /**
   * Mô tả nhóm (nếu có)
   */
  description?: string;

  /**
   * URL avatar nhóm (nếu có)
   */
  avatar_url?: string;

  /**
   * Số lượng thành viên
   */
  member_count: number;

  /**
   * Trạng thái nhóm
   */
  status: 'active' | 'inactive' | 'archived';

  /**
   * Thời gian tạo nhóm
   */
  created_time: number;
}

/**
 * Interface định nghĩa thành viên nhóm
 */
export interface ZaloGroupMember {
  /**
   * ID người dùng
   */
  user_id: string;

  /**
   * Tên hiển thị
   */
  display_name: string;

  /**
   * URL avatar (nếu có)
   */
  avatar_url?: string;

  /**
   * Vai trò trong nhóm
   */
  role: 'admin' | 'member';

  /**
   * Thời gian tham gia nhóm
   */
  joined_time: number;
}

/**
 * Interface định nghĩa kết quả gửi tin nhắn nhóm
 */
export interface ZaloGroupMessageResult {
  /**
   * ID tin nhắn đã gửi
   */
  message_id: string;

  /**
   * Thời gian gửi tin nhắn
   */
  timestamp: number;

  /**
   * Trạng thái gửi
   */
  status: 'sent' | 'failed';

  /**
   * Thông báo lỗi (nếu có)
   */
  error_message?: string;
}

/**
 * Interface định nghĩa thông tin sự kiện webhook (deprecated - use ZaloWebhookEvent from dto)
 * @deprecated Use ZaloWebhookEvent from './dto/zalo-webhook.dto' instead
 */
export interface ZaloLegacyWebhookEvent {
  /**
   * ID của sự kiện
   */
  event_id: string;

  /**
   * Tên của sự kiện
   */
  event_name: string;

  /**
   * Thời gian xảy ra sự kiện (Unix timestamp)
   */
  timestamp: number;

  /**
   * Dữ liệu của sự kiện
   */
  data: any;
}

/**
 * Interface định nghĩa thông tin tin nhắn ZNS
 */
export interface ZaloZnsMessage {
  /**
   * ID của template
   */
  template_id: string;

  /**
   * Số điện thoại người nhận
   */
  phone: string;

  /**
   * Dữ liệu cho template
   */
  template_data: Record<string, string>;

  /**
   * ID giao dịch (nếu có)
   */
  tracking_id?: string;
}

/**
 * Interface định nghĩa kết quả gửi tin nhắn ZNS
 */
export interface ZaloZnsSendResult {
  /**
   * ID của tin nhắn
   */
  message_id: string;

  /**
   * ID giao dịch
   */
  tracking_id: string;
}

// ===== ZALO NOTIFICATION SERVICE (ZNS) INTERFACES =====

/**
 * Interface định nghĩa template ZNS
 * Endpoint: POST https://business.openapi.zalo.me/template/create
 * Điều kiện: Access token hợp lệ của Official Account
 */
export interface ZaloZnsTemplate {
  /**
   * ID template
   */
  templateId: string;

  /**
   * Tên template
   */
  templateName: string;

  /**
   * Trạng thái template
   */
  status: 'ENABLE' | 'PENDING_REVIEW' | 'DELETE' | 'REJECT' | 'DISABLE';

  /**
   * Lý do template có trạng thái hiện tại
   */
  reason?: string;

  /**
   * Danh sách các thuộc tính của template
   */
  listParams?: ZaloZnsTemplateParam[];

  /**
   * Danh sách các buttons/CTAs của template
   */
  listButtons?: ZaloZnsTemplateButton[];

  /**
   * Thời gian timeout của template (milliseconds)
   */
  timeout?: number;

  /**
   * Đường dẫn đến bản xem trước của template
   */
  previewUrl?: string;

  /**
   * Chất lượng template
   */
  templateQuality?: 'UNDEFINED' | 'HIGH' | 'MEDIUM' | 'LOW' | null;

  /**
   * Loại nội dung của template
   */
  templateTag?: 'TRANSACTION' | 'CUSTOMER_CARE' | 'PROMOTION';

  /**
   * Đơn giá của template
   */
  price?: string;

  // ===== LEGACY FIELDS (for backward compatibility) =====

  /**
   * Loại template (text, media, list, button)
   * @deprecated Use templateTag instead
   */
  template_type?: 'text' | 'media' | 'list' | 'button';

  /**
   * Nội dung template
   * @deprecated
   */
  template_content?: string;

  /**
   * Ngôn ngữ template
   * @deprecated
   */
  lang?: string;

  /**
   * Thời gian tạo (Unix timestamp)
   * @deprecated
   */
  createdTime?: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @deprecated
   */
  updated_time?: number;

  /**
   * Danh sách element cho template list (nếu có)
   * @deprecated
   */
  list_element?: ZaloZnsTemplateListElement[];
}

/**
 * Interface định nghĩa tham số template ZNS
 */
export interface ZaloZnsTemplateParam {
  /**
   * Tên thuộc tính
   */
  name: string;

  /**
   * Tính bắt buộc của thuộc tính
   */
  require: boolean;

  /**
   * Định dạng validate của thuộc tính
   */
  type: string;

  /**
   * Số ký tự tối đa được truyền vào thuộc tính
   */
  maxLength: number;

  /**
   * Số ký tự tối thiểu được truyền vào thuộc tính
   */
  minLength: number;

  /**
   * Thông tin cho biết thuộc tính có thể nhận giá trị rỗng hay không
   */
  acceptNull: boolean;
}

/**
 * Interface định nghĩa button trong template ZNS
 */
export interface ZaloZnsTemplateButton {
  /**
   * Button type
   * 1: Đến trang của doanh nghiệp
   * 2: Gọi điện
   * 3: Đến trang thông tin OA
   * 4: Đến ứng dụng Zalo Mini App của doanh nghiệp
   * 5: Đến trang tải ứng dụng
   * 6: Đến trang phân phối sản phẩm
   * 7: Đến trang web/Zalo Mini App khác
   * 8: Đến ứng dụng khác
   */
  type: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8;

  /**
   * Nội dung Button
   */
  title: string;

  /**
   * Đường dẫn liên kết/số điện thoại
   */
  content: string;
}

/**
 * Interface định nghĩa element trong template list ZNS
 */
export interface ZaloZnsTemplateListElement {
  /**
   * Tiêu đề element
   */
  title: string;

  /**
   * Mô tả element
   */
  subtitle?: string;

  /**
   * URL hình ảnh element
   */
  image_url?: string;

  /**
   * Button mặc định của element
   */
  default_action?: ZaloZnsTemplateButton;
}

/**
 * Interface định nghĩa yêu cầu tạo template ZNS
 */
export interface ZaloCreateZnsTemplateRequest {
  /**
   * Tên template
   */
  templateName: string;

  /**
   * Loại template
   */
  templateType: 'text' | 'media' | 'list' | 'button';

  /**
   * Ngôn ngữ template (mặc định: vi)
   */
  lang?: string;

  /**
   * Timeout template (giây, mặc định: 86400)
   */
  timeout?: number;

  /**
   * URL hình ảnh preview
   */
  previewUrl?: string;

  /**
   * Nội dung template
   */
  templateContent: string;

  /**
   * Danh sách button
   */
  listButton?: ZaloZnsTemplateButton[];

  /**
   * Danh sách element cho template list
   */
  listElement?: ZaloZnsTemplateListElement[];
}

/**
 * Interface định nghĩa yêu cầu cập nhật template ZNS
 */
export interface ZaloUpdateZnsTemplateRequest {
  /**
   * ID template
   */
  templateId: string;

  /**
   * Tên template mới
   */
  templateName?: string;

  /**
   * Timeout template (giây)
   */
  timeout?: number;

  /**
   * URL hình ảnh preview mới
   */
  previewUrl?: string;

  /**
   * Nội dung template mới
   */
  templateContent?: string;

  /**
   * Danh sách button mới
   */
  listButton?: ZaloZnsTemplateButton[];

  /**
   * Danh sách element mới cho template list
   */
  listElement?: ZaloZnsTemplateListElement[];
}

/**
 * Interface định nghĩa kết quả upload ảnh ZNS
 * Endpoint: POST https://business.openapi.zalo.me/template/upload_image
 * Điều kiện: Access token hợp lệ của Official Account
 */
export interface ZaloZnsUploadImageResult {
  /**
   * URL ảnh đã upload
   */
  url: string;

  /**
   * ID attachment
   */
  attachment_id: string;

  /**
   * Kích thước file (bytes)
   */
  size?: number;

  /**
   * Loại file
   */
  type?: string;
}

/**
 * Interface định nghĩa tin nhắn ZNS với hash phone
 * Endpoint: POST https://business.openapi.zalo.me/message/template/hash_phone
 * Điều kiện: Access token hợp lệ của Official Account
 */
export interface ZaloZnsHashPhoneMessage {
  /**
   * Hash của số điện thoại
   */
  phone_hash: string;

  /**
   * ID của template
   */
  template_id: string;

  /**
   * Dữ liệu cho template
   */
  template_data: Record<string, string>;

  /**
   * ID giao dịch (nếu có)
   */
  tracking_id?: string;
}

/**
 * Interface định nghĩa tin nhắn ZNS development mode
 * Endpoint: POST https://business.openapi.zalo.me/message/template
 * Điều kiện: Access token hợp lệ của Official Account, mode development
 */
export interface ZaloZnsDevModeMessage {
  /**
   * Số điện thoại người nhận
   */
  phone: string;

  /**
   * ID của template
   */
  template_id: string;

  /**
   * Dữ liệu cho template
   */
  template_data: Record<string, string>;

  /**
   * Chế độ development
   */
  mode: boolean;

  /**
   * ID giao dịch (nếu có)
   */
  tracking_id?: string;
}

/**
 * Interface định nghĩa tin nhắn ZNS với mã hóa RSA
 * Endpoint: POST https://business.openapi.zalo.me/message/template
 * Điều kiện: Access token hợp lệ của Official Account, số điện thoại đã mã hóa RSA
 */
export interface ZaloZnsRsaMessage {
  /**
   * Số điện thoại người nhận (đã mã hóa RSA)
   */
  phone: string;

  /**
   * ID của template
   */
  template_id: string;

  /**
   * Dữ liệu cho template
   */
  template_data: Record<string, string>;

  /**
   * ID giao dịch (nếu có)
   */
  tracking_id?: string;
}

/**
 * Interface định nghĩa tin nhắn ZNS Journey
 * Endpoint: POST https://business.openapi.zalo.me/message/template/journey
 * Điều kiện: Access token hợp lệ của Official Account
 */
export interface ZaloZnsJourneyMessage {
  /**
   * Số điện thoại người nhận
   */
  phone: string;

  /**
   * ID của template
   */
  template_id: string;

  /**
   * Dữ liệu cho template
   */
  template_data: Record<string, string>;

  /**
   * ID journey
   */
  journey_id: string;

  /**
   * ID giao dịch (nếu có)
   */
  tracking_id?: string;
}

/**
 * Interface định nghĩa danh sách template ZNS
 */
export interface ZaloZnsTemplateList {
  /**
   * Danh sách template
   */
  data: ZaloZnsTemplate[];

  /**
   * Metadata chứa thông tin phân trang
   */
  metadata: {
    /**
     * Tổng số template
     */
    total: number;
  };
}

// ===== ENUMS CHO ZNS =====

/**
 * Enum cho trạng thái template ZNS theo API
 */
export enum ZaloZnsTemplateStatus {
  ENABLE = 1,
  PENDING_REVIEW = 2,
  REJECT = 3,
  DISABLE = 4,
  DELETE = 5, // Thêm trạng thái DELETE
}

/**
 * Enum cho chất lượng template ZNS
 */
export enum ZaloZnsTemplateQuality {
  UNDEFINED = 'UNDEFINED',
  HIGH = 'HIGH',
  MEDIUM = 'MEDIUM',
  LOW = 'LOW',
}

// ===== INTERFACES CHO CÁC API TRUY XUẤT THÔNG TIN ZNS =====

/**
 * Interface định nghĩa thông tin trạng thái ZNS
 * Endpoint: GET https://business.openapi.zalo.me/message/status
 * Điều kiện: Access token hợp lệ của Official Account
 */
export interface ZaloZnsStatusInfo {
  /**
   * Trạng thái ZNS (ACTIVE, INACTIVE, SUSPENDED)
   */
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';

  /**
   * Thời gian cập nhật trạng thái (Unix timestamp)
   */
  updated_time: number;

  /**
   * Lý do thay đổi trạng thái
   */
  reason?: string;

  /**
   * Thông tin bổ sung
   */
  additional_info?: string;
}

/**
 * Interface định nghĩa thông tin quota ZNS
 * Endpoint: GET https://business.openapi.zalo.me/message/quota
 * Điều kiện: Access token hợp lệ của Official Account
 */
export interface ZaloZnsQuotaInfo {
  /**
   * Số thông báo ZNS OA được gửi trong 1 ngày
   * Lưu ý: Hạn mức gửi ZNS mỗi ngày của OA sẽ tự động được điều chỉnh dựa theo chất lượng và nhu cầu gửi
   */
  dailyQuota: number;

  /**
   * Số thông báo ZNS OA được gửi trong ngày còn lại
   */
  remainingQuota: number;

  /**
   * Số tin ZNS hậu mãi OA được gửi trong ngày
   * Ghi chú: Từ ngày 1/11, thuộc tính này sẽ trả về giá trị null
   */
  dailyQuotaPromotion: number | null;

  /**
   * Số tin ZNS hậu mãi còn lại OA được gửi trong ngày
   * Ghi chú: Từ ngày 1/11, thuộc tính này sẽ trả về giá trị null
   */
  remainingQuotaPromotion: number | null;

  /**
   * Số tin ZNS hậu mãi OA được gửi trong tháng
   */
  monthlyPromotionQuota: number;

  /**
   * Số tin ZNS hậu mãi còn lại OA được gửi trong tháng
   */
  remainingMonthlyPromotionQuota: number;

  /**
   * Số tin ZNS hậu mãi dự kiến mà OA có thể gửi trong tháng tiếp theo
   */
  estimatedNextMonthPromotionQuota: number;
}

/**
 * Interface định nghĩa thông tin loại nội dung ZNS được phép gửi
 * Endpoint: GET https://business.openapi.zalo.me/message/content_types
 * Điều kiện: Access token hợp lệ của Official Account
 */
export interface ZaloZnsAllowedContent {
  /**
   * Danh sách loại nội dung được phép
   */
  allowed_content_types: ('TRANSACTION' | 'PROMOTION' | 'CUSTOMER_CARE' | 'NOTIFICATION')[];

  /**
   * Có được phép gửi tin nhắn giao dịch không
   */
  can_send_transaction: boolean;

  /**
   * Có được phép gửi tin nhắn khuyến mãi không
   */
  can_send_promotion: boolean;

  /**
   * Có được phép gửi tin nhắn chăm sóc khách hàng không
   */
  can_send_customer_care: boolean;

  /**
   * Có được phép gửi tin nhắn thông báo không
   */
  can_send_notification: boolean;

  /**
   * Giới hạn đặc biệt cho từng loại nội dung
   */
  content_limits?: Record<string, number>;
}

/**
 * Interface định nghĩa dữ liệu mẫu của template
 * Endpoint: GET https://business.openapi.zalo.me/template/sample_data
 * Điều kiện: Access token hợp lệ của Official Account
 */
export interface ZaloZnsTemplateSampleData {
  /**
   * ID template
   */
  template_id: string;

  /**
   * Dữ liệu mẫu cho template
   */
  sample_data: Record<string, string>;

  /**
   * Nội dung template sau khi thay thế dữ liệu mẫu
   */
  preview_content: string;

  /**
   * URL preview (nếu có hình ảnh)
   */
  preview_url?: string;
}

/**
 * Interface định nghĩa thông tin đánh giá của khách hàng
 * Endpoint: GET https://business.openapi.zalo.me/message/rating
 * Điều kiện: Access token hợp lệ của Official Account
 */
export interface ZaloZnsCustomerRating {
  /**
   * Tổng số đánh giá
   */
  total_ratings: number;

  /**
   * Điểm đánh giá trung bình (1-5)
   */
  average_rating: number;

  /**
   * Số đánh giá 5 sao
   */
  five_star_count: number;

  /**
   * Số đánh giá 4 sao
   */
  four_star_count: number;

  /**
   * Số đánh giá 3 sao
   */
  three_star_count: number;

  /**
   * Số đánh giá 2 sao
   */
  two_star_count: number;

  /**
   * Số đánh giá 1 sao
   */
  one_star_count: number;

  /**
   * Phần trăm đánh giá tích cực (4-5 sao)
   */
  positive_rating_percentage: number;

  /**
   * Thời gian cập nhật cuối cùng
   */
  last_updated: number;

  /**
   * Xu hướng đánh giá (tăng/giảm so với tháng trước)
   */
  rating_trend?: 'up' | 'down' | 'stable';
}

/**
 * Interface định nghĩa chi tiết đánh giá khách hàng
 */
export interface ZaloZnsCustomerRatingDetail {
  /**
   * ID đánh giá
   */
  rating_id: string;

  /**
   * ID tin nhắn được đánh giá
   */
  message_id: string;

  /**
   * ID template
   */
  template_id: string;

  /**
   * Điểm đánh giá (1-5)
   */
  rating: number;

  /**
   * Nhận xét của khách hàng
   */
  comment?: string;

  /**
   * Thời gian đánh giá
   */
  rating_time: number;

  /**
   * ID khách hàng (nếu có)
   */
  customer_id?: string;

  /**
   * Thông tin bổ sung
   */
  metadata?: Record<string, any>;
}

/**
 * Interface định nghĩa danh sách đánh giá khách hàng
 */
export interface ZaloZnsCustomerRatingList {
  /**
   * Danh sách đánh giá
   */
  ratings: ZaloZnsCustomerRatingDetail[];

  /**
   * Tổng số đánh giá
   */
  total: number;

  /**
   * Có còn dữ liệu tiếp theo không
   */
  has_more?: boolean;

  /**
   * Offset tiếp theo
   */
  next_offset?: number;
}

/**
 * Interface định nghĩa thông tin chất lượng gửi ZNS
 * Endpoint: GET https://business.openapi.zalo.me/message/quality
 * Điều kiện: Access token hợp lệ của Official Account
 */
export interface ZaloZnsQualityInfo {
  /**
   * Mức độ chất lượng hiện tại (HIGH, MEDIUM, LOW)
   */
  current_quality_level: 'HIGH' | 'MEDIUM' | 'LOW';

  /**
   * Điểm chất lượng (0-100)
   */
  quality_score: number;

  /**
   * Tỷ lệ gửi thành công (%)
   */
  delivery_rate: number;

  /**
   * Tỷ lệ khách hàng đọc tin nhắn (%)
   */
  read_rate: number;

  /**
   * Tỷ lệ khách hàng tương tác (%)
   */
  engagement_rate: number;

  /**
   * Tỷ lệ khiếu nại (%)
   */
  complaint_rate: number;

  /**
   * Điểm đánh giá trung bình từ khách hàng
   */
  average_customer_rating: number;

  /**
   * Thời gian đánh giá
   */
  evaluation_time: number;

  /**
   * Xu hướng chất lượng so với tháng trước
   */
  quality_trend?: 'improving' | 'declining' | 'stable';

  /**
   * Gợi ý cải thiện chất lượng
   */
  improvement_suggestions?: string[];
}

/**
 * Interface định nghĩa chi tiết chất lượng theo thời gian
 */
export interface ZaloZnsQualityHistory {
  /**
   * Thời gian đánh giá
   */
  evaluation_time: number;

  /**
   * Điểm chất lượng tại thời điểm đó
   */
  quality_score: number;

  /**
   * Mức độ chất lượng tại thời điểm đó
   */
  quality_level: 'HIGH' | 'MEDIUM' | 'LOW';

  /**
   * Tỷ lệ gửi thành công (%)
   */
  delivery_rate: number;

  /**
   * Tỷ lệ đọc tin nhắn (%)
   */
  read_rate: number;

  /**
   * Tỷ lệ khiếu nại (%)
   */
  complaint_rate: number;
}

/**
 * Interface định nghĩa danh sách lịch sử chất lượng
 */
export interface ZaloZnsQualityHistoryList {
  /**
   * Danh sách lịch sử chất lượng
   */
  history: ZaloZnsQualityHistory[];

  /**
   * Tổng số bản ghi
   */
  total: number;

  /**
   * Khoảng thời gian đánh giá
   */
  interval: 'daily' | 'weekly' | 'monthly';
}

// ===== ZALO GROUP MANAGEMENT FRAMEWORK (GMF) INTERFACES =====

/**
 * Interface định nghĩa thông tin tạo nhóm mới
 */
export interface ZaloCreateGroupRequest {
  /**
   * Tên nhóm (bắt buộc, tối đa 100 ký tự)
   */
  group_name: string;

  /**
   * Mô tả nhóm (tùy chọn, tối đa 500 ký tự)
   */
  description?: string;

  /**
   * URL avatar nhóm (tùy chọn)
   */
  avatar_url?: string;

  /**
   * Danh sách user ID được mời vào nhóm (tối đa 200 người)
   */
  member_uids: string[];
}

/**
 * Interface định nghĩa kết quả tạo nhóm
 */
export interface ZaloCreateGroupResult {
  /**
   * ID của nhóm vừa tạo
   */
  group_id: string;

  /**
   * Thời gian tạo nhóm
   */
  created_time: number;
}

/**
 * Interface định nghĩa thông tin cập nhật nhóm
 */
export interface ZaloUpdateGroupRequest {
  /**
   * Tên nhóm mới (tùy chọn, tối đa 100 ký tự)
   */
  group_name?: string;

  /**
   * Mô tả nhóm mới (tùy chọn, tối đa 500 ký tự)
   */
  description?: string;
}

/**
 * Interface định nghĩa thông tin cập nhật avatar nhóm
 */
export interface ZaloUpdateGroupAvatarRequest {
  /**
   * URL avatar mới
   */
  avatar_url: string;
}

/**
 * Interface định nghĩa thông tin mời thành viên
 */
export interface ZaloInviteMemberRequest {
  /**
   * Danh sách user ID được mời (tối đa 50 người mỗi lần)
   */
  member_uids: string[];
}

/**
 * Interface định nghĩa thành viên chờ duyệt
 */
export interface ZaloPendingMember {
  /**
   * ID người dùng
   */
  user_id: string;

  /**
   * Tên hiển thị
   */
  display_name: string;

  /**
   * URL avatar (nếu có)
   */
  avatar_url?: string;

  /**
   * Thời gian yêu cầu tham gia
   */
  request_time: number;
}

/**
 * Interface định nghĩa thông tin duyệt/từ chối thành viên
 */
export interface ZaloMemberActionRequest {
  /**
   * Danh sách user ID cần xử lý
   */
  member_uids: string[];
}

/**
 * Interface định nghĩa thông tin thêm/xóa admin
 */
export interface ZaloAdminActionRequest {
  /**
   * Danh sách user ID cần thêm/xóa quyền admin
   */
  admin_uids: string[];
}

/**
 * Interface định nghĩa thông tin xóa thành viên
 */
export interface ZaloRemoveMemberRequest {
  /**
   * Danh sách user ID cần xóa khỏi nhóm
   */
  member_uids: string[];
}

/**
 * Interface định nghĩa thông tin hạn mức nhóm
 */
export interface ZaloGroupQuota {
  /**
   * Số nhóm tối đa có thể tạo
   */
  max_groups: number;

  /**
   * Số nhóm đã tạo
   */
  created_groups: number;

  /**
   * Số nhóm còn lại có thể tạo
   */
  remaining_groups: number;

  /**
   * Số thành viên tối đa mỗi nhóm
   */
  max_members_per_group: number;

  /**
   * Số tin nhắn tối đa mỗi ngày
   */
  daily_message_limit: number;

  /**
   * Số tin nhắn đã gửi hôm nay
   */
  daily_messages_sent: number;
}

/**
 * Interface định nghĩa cuộc trò chuyện gần đây
 */
export interface ZaloRecentChat {
  /**
   * ID nhóm
   */
  group_id: string;

  /**
   * Tên nhóm
   */
  group_name: string;

  /**
   * URL avatar nhóm
   */
  avatar_url?: string;

  /**
   * Tin nhắn cuối cùng
   */
  last_message: {
    /**
     * ID tin nhắn
     */
    message_id: string;

    /**
     * ID người gửi
     */
    from_id: string;

    /**
     * Tên người gửi
     */
    from_name: string;

    /**
     * Nội dung tin nhắn
     */
    content: string;

    /**
     * Loại tin nhắn
     */
    type: string;

    /**
     * Thời gian gửi
     */
    timestamp: number;
  };

  /**
   * Số tin nhắn chưa đọc
   */
  unread_count: number;

  /**
   * Thời gian cập nhật cuối
   */
  last_updated: number;
}

/**
 * Interface định nghĩa tin nhắn trong cuộc trò chuyện nhóm
 */
export interface ZaloGroupConversationMessage {
  /**
   * ID tin nhắn
   */
  message_id: string;

  /**
   * ID người gửi
   */
  from_id: string;

  /**
   * Tên người gửi
   */
  from_name: string;

  /**
   * URL avatar người gửi
   */
  from_avatar?: string;

  /**
   * Nội dung tin nhắn
   */
  content: any;

  /**
   * Loại tin nhắn
   */
  type: 'text' | 'image' | 'file' | 'sticker' | 'mention';

  /**
   * Thời gian gửi
   */
  timestamp: number;

  /**
   * Trạng thái tin nhắn
   */
  status: 'sent' | 'delivered' | 'read';

  /**
   * Danh sách user được mention (nếu có)
   */
  mentioned_users?: string[];
}

/**
 * Interface định nghĩa yêu cầu cấp quyền gọi thoại
 */
export interface ZaloCallPermissionRequest {
  /**
   * ID của người dùng Zalo
   */
  user_id: string;

  /**
   * Lý do yêu cầu cấp quyền gọi
   */
  reason?: string;

  /**
   * Thông tin bổ sung
   */
  metadata?: Record<string, any>;
}

/**
 * Interface định nghĩa phản hồi yêu cầu cấp quyền gọi
 */
export interface ZaloCallPermissionResponse {
  /**
   * Trạng thái yêu cầu
   */
  status: 'success' | 'failed';

  /**
   * ID yêu cầu
   */
  request_id?: string;

  /**
   * Thông báo
   */
  message?: string;
}

/**
 * Interface định nghĩa trạng thái cấp quyền gọi
 */
export interface ZaloCallPermissionStatus {
  /**
   * ID của người dùng Zalo
   */
  user_id: string;

  /**
   * Trạng thái cấp quyền (granted, denied, pending)
   */
  permission_status: 'granted' | 'denied' | 'pending';

  /**
   * Thời gian cấp quyền (Unix timestamp)
   */
  granted_time?: number;

  /**
   * Thời gian hết hạn quyền (Unix timestamp)
   */
  expires_time?: number;

  /**
   * Lý do từ chối (nếu có)
   */
  deny_reason?: string;
}

/**
 * Interface định nghĩa yêu cầu tạo link gọi thoại
 */
export interface ZaloCallLinkRequest {
  /**
   * ID của người dùng Zalo
   */
  user_id: string;

  /**
   * Loại cuộc gọi (audio, video)
   */
  call_type: 'audio' | 'video';

  /**
   * ID agent thực hiện cuộc gọi
   */
  agent_id?: string;

  /**
   * ID branch
   */
  branch_id?: string;

  /**
   * Thời gian hết hạn link (giây)
   */
  expires_in?: number;

  /**
   * Thông tin bổ sung
   */
  metadata?: Record<string, any>;
}

/**
 * Interface định nghĩa phản hồi tạo link gọi thoại
 */
export interface ZaloCallLinkResponse {
  /**
   * Link gọi thoại
   */
  call_link: string;

  /**
   * ID cuộc gọi
   */
  call_id: string;

  /**
   * Thời gian hết hạn link (Unix timestamp)
   */
  expires_time: number;

  /**
   * Trạng thái tạo link
   */
  status: 'success' | 'failed';

  /**
   * Thông báo
   */
  message?: string;
}

/**
 * Interface định nghĩa thông tin Agent
 */
export interface ZaloAgentInfo {
  /**
   * ID của agent
   */
  agent_id: string;

  /**
   * Tên agent
   */
  agent_name: string;

  /**
   * Email agent
   */
  email?: string;

  /**
   * Số điện thoại agent
   */
  phone?: string;

  /**
   * Trạng thái agent (online, offline, busy)
   */
  status: 'online' | 'offline' | 'busy';

  /**
   * ID branch mà agent thuộc về
   */
  branch_id?: string;

  /**
   * Thông tin bổ sung
   */
  metadata?: Record<string, any>;
}

/**
 * Interface định nghĩa thông tin Branch
 */
export interface ZaloBranchInfo {
  /**
   * ID của branch
   */
  branch_id: string;

  /**
   * Tên branch
   */
  branch_name: string;

  /**
   * Mô tả branch
   */
  description?: string;

  /**
   * Địa chỉ branch
   */
  address?: string;

  /**
   * Số điện thoại branch
   */
  phone?: string;

  /**
   * Email branch
   */
  email?: string;

  /**
   * Trạng thái branch (active, inactive)
   */
  status: 'active' | 'inactive';

  /**
   * Danh sách agent trong branch
   */
  agents?: ZaloAgentInfo[];

  /**
   * Thông tin bổ sung
   */
  metadata?: Record<string, any>;
}

/**
 * Interface định nghĩa thông tin cuộc gọi
 */
export interface ZaloCallInfo {
  /**
   * ID cuộc gọi
   */
  call_id: string;

  /**
   * ID người gọi
   */
  caller_id: string;

  /**
   * ID người nhận
   */
  callee_id: string;

  /**
   * Loại cuộc gọi (audio, video)
   */
  call_type: 'audio' | 'video';

  /**
   * Trạng thái cuộc gọi (initiated, ringing, answered, ended, failed)
   */
  status: 'initiated' | 'ringing' | 'answered' | 'ended' | 'failed';

  /**
   * Thời gian bắt đầu cuộc gọi (Unix timestamp)
   */
  start_time: number;

  /**
   * Thời gian kết thúc cuộc gọi (Unix timestamp)
   */
  end_time?: number;

  /**
   * Thời lượng cuộc gọi (giây)
   */
  duration?: number;

  /**
   * Lý do kết thúc cuộc gọi
   */
  end_reason?: string;

  /**
   * ID agent xử lý cuộc gọi
   */
  agent_id?: string;

  /**
   * ID branch
   */
  branch_id?: string;

  /**
   * Thông tin bổ sung
   */
  metadata?: Record<string, any>;
}

/**
 * Interface định nghĩa mã lỗi Zalo Call API
 */
export interface ZaloCallErrorCode {
  /**
   * Mã lỗi
   */
  error_code: number;

  /**
   * Tên lỗi
   */
  error_name: string;

  /**
   * Mô tả lỗi
   */
  error_description: string;
}

/**
 * Interface định nghĩa yêu cầu tạo nội dung bài viết
 */
export interface ZaloContentRequest {
  /**
   * Tiêu đề bài viết
   */
  title: string;

  /**
   * Mô tả ngắn gọn
   */
  description?: string;

  /**
   * Nội dung bài viết (HTML)
   */
  content: string;

  /**
   * URL ảnh đại diện
   */
  cover_photo_url?: string;

  /**
   * URL video (nếu có)
   */
  video_url?: string;

  /**
   * Trạng thái xuất bản (draft, published)
   */
  status?: 'draft' | 'published';

  /**
   * Thời gian xuất bản (Unix timestamp)
   */
  publish_time?: number;

  /**
   * Tags của bài viết
   */
  tags?: string[];

  /**
   * Thông tin bổ sung
   */
  metadata?: Record<string, any>;
}

/**
 * Interface định nghĩa phản hồi tạo nội dung
 */
export interface ZaloContentResponse {
  /**
   * ID của nội dung
   */
  content_id: string;

  /**
   * Trạng thái tạo nội dung
   */
  status: 'success' | 'processing' | 'failed';

  /**
   * ID tiến trình (nếu đang xử lý)
   */
  process_id?: string;

  /**
   * Thông báo
   */
  message?: string;

  /**
   * URL xem trước (nếu có)
   */
  preview_url?: string;
}

/**
 * Interface định nghĩa chi tiết nội dung
 */
export interface ZaloContentDetail {
  /**
   * ID của nội dung
   */
  content_id: string;

  /**
   * Tiêu đề bài viết
   */
  title: string;

  /**
   * Mô tả ngắn gọn
   */
  description?: string;

  /**
   * Nội dung bài viết
   */
  content: string;

  /**
   * URL ảnh đại diện
   */
  cover_photo_url?: string;

  /**
   * URL video (nếu có)
   */
  video_url?: string;

  /**
   * Trạng thái (draft, published, deleted)
   */
  status: 'draft' | 'published' | 'deleted';

  /**
   * Thời gian tạo (Unix timestamp)
   */
  created_time: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  updated_time: number;

  /**
   * Thời gian xuất bản (Unix timestamp)
   */
  publish_time?: number;

  /**
   * Số lượt xem
   */
  view_count?: number;

  /**
   * Số lượt thích
   */
  like_count?: number;

  /**
   * Số lượt chia sẻ
   */
  share_count?: number;

  /**
   * Tags của bài viết
   */
  tags?: string[];

  /**
   * URL xem trước
   */
  preview_url?: string;

  /**
   * Thông tin bổ sung
   */
  metadata?: Record<string, any>;
}

/**
 * Interface định nghĩa danh sách nội dung
 */
export interface ZaloContentList {
  /**
   * Danh sách nội dung
   */
  contents: ZaloContentDetail[];

  /**
   * Tổng số nội dung
   */
  total: number;

  /**
   * Có còn dữ liệu tiếp theo không
   */
  has_more: boolean;

  /**
   * Offset tiếp theo
   */
  next_offset?: number;
}

/**
 * Interface định nghĩa yêu cầu upload video
 */
export interface ZaloVideoUploadRequest {
  /**
   * Tên file video
   */
  filename: string;

  /**
   * Kích thước file (bytes)
   */
  file_size: number;

  /**
   * Loại MIME của video
   */
  mime_type: string;

  /**
   * Mô tả video
   */
  description?: string;
}

/**
 * Interface định nghĩa phản hồi upload video
 */
export interface ZaloVideoUploadResponse {
  /**
   * ID của video
   */
  video_id: string;

  /**
   * URL upload
   */
  upload_url: string;

  /**
   * Token upload
   */
  upload_token: string;

  /**
   * Thời gian hết hạn upload (Unix timestamp)
   */
  expires_time: number;

  /**
   * Trạng thái
   */
  status: 'ready' | 'processing' | 'completed' | 'failed';
}

/**
 * Interface định nghĩa tiến trình xử lý
 */
export interface ZaloContentProcess {
  /**
   * ID tiến trình
   */
  process_id: string;

  /**
   * Trạng thái tiến trình
   */
  status: 'processing' | 'completed' | 'failed';

  /**
   * Phần trăm hoàn thành (0-100)
   */
  progress: number;

  /**
   * Thông báo
   */
  message?: string;

  /**
   * ID nội dung (nếu hoàn thành)
   */
  content_id?: string;

  /**
   * Lỗi (nếu có)
   */
  error?: {
    code: number;
    message: string;
  };

  /**
   * Thời gian bắt đầu (Unix timestamp)
   */
  start_time: number;

  /**
   * Thời gian hoàn thành (Unix timestamp)
   */
  end_time?: number;
}

/**
 * Interface định nghĩa yêu cầu tạo nội dung video
 */
export interface ZaloVideoContentRequest {
  /**
   * Tiêu đề video
   */
  title: string;

  /**
   * Mô tả video
   */
  description?: string;

  /**
   * URL video đã upload
   */
  video_url: string;

  /**
   * URL ảnh thumbnail
   */
  thumbnail_url?: string;

  /**
   * Trạng thái xuất bản (draft, published)
   */
  status?: 'draft' | 'published';

  /**
   * Thời gian xuất bản (Unix timestamp)
   */
  publish_time?: number;

  /**
   * Tags của video
   */
  tags?: string[];

  /**
   * Thông tin bổ sung
   */
  metadata?: Record<string, any>;
}

/**
 * Interface định nghĩa phản hồi tạo nội dung video
 */
export interface ZaloVideoContentResponse {
  /**
   * ID của nội dung video
   */
  video_content_id: string;

  /**
   * Trạng thái tạo nội dung
   */
  status: 'success' | 'processing' | 'failed';

  /**
   * ID tiến trình (nếu đang xử lý)
   */
  process_id?: string;

  /**
   * Thông báo
   */
  message?: string;

  /**
   * URL xem trước (nếu có)
   */
  preview_url?: string;
}

/**
 * Interface định nghĩa chi tiết nội dung video
 */
export interface ZaloVideoContentDetail {
  /**
   * ID của nội dung video
   */
  video_content_id: string;

  /**
   * Tiêu đề video
   */
  title: string;

  /**
   * Mô tả video
   */
  description?: string;

  /**
   * URL video
   */
  video_url: string;

  /**
   * URL ảnh thumbnail
   */
  thumbnail_url?: string;

  /**
   * Thời lượng video (giây)
   */
  duration?: number;

  /**
   * Kích thước file (bytes)
   */
  file_size?: number;

  /**
   * Độ phân giải video
   */
  resolution?: {
    width: number;
    height: number;
  };

  /**
   * Trạng thái (draft, published, deleted)
   */
  status: 'draft' | 'published' | 'deleted';

  /**
   * Thời gian tạo (Unix timestamp)
   */
  created_time: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  updated_time: number;

  /**
   * Thời gian xuất bản (Unix timestamp)
   */
  publish_time?: number;

  /**
   * Số lượt xem
   */
  view_count?: number;

  /**
   * Số lượt thích
   */
  like_count?: number;

  /**
   * Số lượt chia sẻ
   */
  share_count?: number;

  /**
   * Số lượt bình luận
   */
  comment_count?: number;

  /**
   * Tags của video
   */
  tags?: string[];

  /**
   * URL xem trước
   */
  preview_url?: string;

  /**
   * Thông tin bổ sung
   */
  metadata?: Record<string, any>;
}

/**
 * Interface định nghĩa danh sách nội dung video
 */
export interface ZaloVideoContentList {
  /**
   * Danh sách nội dung video
   */
  video_contents: ZaloVideoContentDetail[];

  /**
   * Tổng số nội dung video
   */
  total: number;

  /**
   * Có còn dữ liệu tiếp theo không
   */
  has_more: boolean;

  /**
   * Offset tiếp theo
   */
  next_offset?: number;
}