import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Put,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto } from '@common/response';
import { CompleteEventProductService } from '@modules/business/user/services';
import { CompleteUpdateEventProductDto } from '@modules/business/user/dto/event-product';
import { CompleteEventProductResponseDto } from '@modules/business/user/dto/event-product';
import { ParticipationTypeEnum } from '@modules/business/enums';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý cập nhật hoàn chỉnh Event Product
 * Bao gồm customer_products + event_products + tickets + images
 */
@ApiTags(SWAGGER_API_TAGS.USER_BUSINESS_VARIANT_PRODUCT)
@Controller('user/products/event')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto,
  CompleteUpdateEventProductDto,
  CompleteEventProductResponseDto,
)
export class CompleteEventProductController {
  constructor(
    private readonly completeEventProductService: CompleteEventProductService,
  ) {}

  /**
   * Cập nhật hoàn chỉnh sản phẩm sự kiện
   * @param id ID của sản phẩm sự kiện
   * @param updateDto DTO chứa toàn bộ thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm sự kiện đã cập nhật
   */
  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cập nhật hoàn chỉnh sản phẩm sự kiện',
    description: `Cập nhật toàn bộ thông tin sản phẩm sự kiện bao gồm:
    - Thông tin cơ bản từ customer_products (name, description, price, tags, etc.)
    - Thông tin sự kiện từ event_products (participationType, location, participationUrl, startDate, endDate, timeZone)
    - Quản lý vé thông qua operations (ADD/UPDATE/DELETE) với đầy đủ fields: name, price, totalQuantity, description, saleStart, saleEnd, timeZone, sku, minQuantityPerOrder, maxQuantityPerOrder
    - Quản lý images thông qua operations (ADD/DELETE) cho cả product level và ticket level

    **Ví dụ cho participationType:**
    - **online**: Sự kiện trực tuyến (cần participationUrl, không cần location)
    - **offline**: Sự kiện trực tiếp (cần location, không cần participationUrl)`,
  })
  @ApiBody({
    type: CompleteUpdateEventProductDto,
    description: 'Dữ liệu cập nhật hoàn chỉnh sản phẩm sự kiện',
    examples: {
      'online-event': {
        summary: 'Sự kiện trực tuyến (Online)',
        description: 'Ví dụ cho sự kiện trực tuyến với participationType = "online"',
        value: {
          basicInfo: {
            name: 'Hội thảo Marketing Digital Online 2024',
            description: 'Hội thảo trực tuyến về xu hướng marketing digital mới nhất',
            productType: 'EVENT',
            tags: ['hội thảo', 'marketing', 'online', '2024']
          },
          pricing: {
            price: {
              listPrice: 300000,
              salePrice: 250000,
              currency: 'VND'
            },
            typePrice: 'HAS_PRICE'
          },
          eventInfo: {
            participationType: 'online',
            participationUrl: 'https://zoom.us/j/123456789',
            startDate: 1704067200000,
            endDate: '2024-01-15T18:00:00.000Z',
            timeZone: 'Asia/Ho_Chi_Minh'
          },
          operations: {
            tickets: [
              {
                operation: 'add',
                data: {
                  name: 'Vé tham gia online',
                  price: 250000,
                  totalQuantity: 100,
                  description: 'Vé tham gia hội thảo trực tuyến',
                  saleStart: 1704067200000,
                  saleEnd: 1704153600000,
                  timeZone: 'Asia/Ho_Chi_Minh',
                  sku: 'ONLINE-TICKET-001',
                  minQuantityPerOrder: 1,
                  maxQuantityPerOrder: 5
                }
              }
            ]
          }
        }
      },
      'offline-event': {
        summary: 'Sự kiện trực tiếp (Offline)',
        description: 'Ví dụ cho sự kiện trực tiếp với participationType = "offline"',
        value: {
          basicInfo: {
            name: 'Hội thảo Marketing Digital Offline 2024',
            description: 'Hội thảo trực tiếp về xu hướng marketing digital tại Hà Nội',
            productType: 'EVENT',
            tags: ['hội thảo', 'marketing', 'offline', 'hanoi', '2024']
          },
          pricing: {
            price: {
              listPrice: 500000,
              salePrice: 450000,
              currency: 'VND'
            },
            typePrice: 'HAS_PRICE'
          },
          eventInfo: {
            participationType: 'offline',
            location: 'Trung tâm Hội nghị Quốc gia, Hà Nội',
            startDate: 1704067200000,
            endDate: '2024-01-15T18:00:00.000Z',
            timeZone: 'Asia/Ho_Chi_Minh'
          },
          customFields: [
            {
              customFieldId: 1,
              value: { value: 'Hội thảo chuyên sâu' }
            },
            {
              customFieldId: 2,
              value: { value: 'Cấp độ nâng cao' }
            }
          ],
          operations: {
            tickets: [
              {
                operation: 'add',
                data: {
                  name: 'Vé VIP',
                  price: 500000,
                  totalQuantity: 50,
                  description: 'Vé VIP bao gồm chỗ ngồi hạng nhất và buffet',
                  saleStart: 1704067200000,
                  saleEnd: 1704153600000,
                  timeZone: 'Asia/Ho_Chi_Minh',
                  sku: 'VIP-TICKET-001',
                  minQuantityPerOrder: 1,
                  maxQuantityPerOrder: 10,
                  imageOperations: [
                    {
                      operation: 'add',
                      mediaId: 'dcc8f803-04fc-49b3-953a-e660505ac353'
                    }
                  ]
                }
              },
              {
                operation: 'add',
                data: {
                  name: 'Vé thường',
                  price: 300000,
                  totalQuantity: 200,
                  description: 'Vé tham gia hội thảo với chỗ ngồi thường',
                  saleStart: 1704067200000,
                  saleEnd: 1704153600000,
                  timeZone: 'Asia/Ho_Chi_Minh',
                  sku: 'REGULAR-TICKET-001',
                  minQuantityPerOrder: 1,
                  maxQuantityPerOrder: 5
                }
              },
              {
                operation: 'update',
                id: 123,
                data: {
                  name: 'Vé VIP - Cập nhật',
                  price: 450000,
                  imageOperations: [
                    {
                      operation: 'delete',
                      entityHasMediaId: 456
                    }
                  ]
                }
              },
              {
                operation: 'delete',
                id: 124
              }
            ],
            images: [
              {
                operation: 'add',
                mediaId: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2'
              },
              {
                operation: 'delete',
                entityHasMediaId: 789
              }
            ]
          }
        }
      },
      'complete-example': {
        summary: 'Ví dụ hoàn chỉnh với tất cả fields',
        description: 'Ví dụ bao gồm tất cả các trường có thể có trong request body',
        value: {
          basicInfo: {
            name: 'Hội thảo Marketing Digital 2024 - Phiên bản hoàn chỉnh',
            description: 'Hội thảo về xu hướng marketing digital mới nhất năm 2024 với các chuyên gia hàng đầu trong ngành',
            productType: 'EVENT',
            tags: ['hội thảo', 'marketing', 'digital', '2024', 'chuyên gia', 'xu hướng']
          },
          pricing: {
            price: {
              listPrice: 800000,
              salePrice: 650000,
              currency: 'VND'
            },
            typePrice: 'HAS_PRICE'
          },
          eventInfo: {
            participationType: 'offline',
            location: 'Trung tâm Hội nghị Quốc gia, 1 Thăng Long, Ba Đình, Hà Nội',
            startDate: 1704067200000,
            endDate: '2024-01-15T18:00:00.000Z',
            timeZone: 'Asia/Ho_Chi_Minh'
          },
          customFields: [
            {
              customFieldId: 1,
              value: { value: 'Hội thảo chuyên sâu' }
            },
            {
              customFieldId: 2,
              value: { value: 'Cấp độ nâng cao' }
            },
            {
              customFieldId: 3,
              value: { value: 'Có chứng chỉ' }
            }
          ],
          operations: {
            tickets: [
              {
                operation: 'add',
                data: {
                  name: 'Vé VIP Premium',
                  price: 800000,
                  totalQuantity: 30,
                  description: 'Vé VIP Premium bao gồm chỗ ngồi hạng nhất, buffet cao cấp và tài liệu độc quyền',
                  saleStart: 1704067200000,
                  saleEnd: 1704153600000,
                  timeZone: 'Asia/Ho_Chi_Minh',
                  sku: 'VIP-PREMIUM-001',
                  minQuantityPerOrder: 1,
                  maxQuantityPerOrder: 5,
                  imageOperations: [
                    {
                      operation: 'add',
                      mediaId: 'dcc8f803-04fc-49b3-953a-e660505ac353'
                    },
                    {
                      operation: 'add',
                      mediaId: 'abc123-def456-ghi789-jkl012'
                    }
                  ]
                }
              },
              {
                operation: 'add',
                data: {
                  name: 'Vé VIP',
                  price: 650000,
                  totalQuantity: 50,
                  description: 'Vé VIP bao gồm chỗ ngồi hạng nhất và buffet',
                  saleStart: 1704067200000,
                  saleEnd: 1704153600000,
                  timeZone: 'Asia/Ho_Chi_Minh',
                  sku: 'VIP-TICKET-001',
                  minQuantityPerOrder: 1,
                  maxQuantityPerOrder: 10
                }
              },
              {
                operation: 'add',
                data: {
                  name: 'Vé thường',
                  price: 400000,
                  totalQuantity: 200,
                  description: 'Vé tham gia hội thảo với chỗ ngồi thường và tài liệu cơ bản',
                  saleStart: 1704067200000,
                  saleEnd: 1704153600000,
                  timeZone: 'Asia/Ho_Chi_Minh',
                  sku: 'REGULAR-TICKET-001',
                  minQuantityPerOrder: 1,
                  maxQuantityPerOrder: 8
                }
              },
              {
                operation: 'update',
                id: 123,
                data: {
                  name: 'Vé VIP - Cập nhật giá',
                  price: 600000,
                  description: 'Vé VIP với giá ưu đãi đặc biệt',
                  maxQuantityPerOrder: 15,
                  imageOperations: [
                    {
                      operation: 'add',
                      mediaId: 'new-image-123-456'
                    },
                    {
                      operation: 'delete',
                      entityHasMediaId: 456
                    }
                  ]
                }
              },
              {
                operation: 'delete',
                id: 124
              }
            ],
            images: [
              {
                operation: 'add',
                mediaId: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2'
              },
              {
                operation: 'add',
                mediaId: 'event-banner-main-image-uuid'
              },
              {
                operation: 'delete',
                entityHasMediaId: 789
              },
              {
                operation: 'delete',
                entityHasMediaId: 790
              }
            ]
          }
        }
      }
    }
  })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm sự kiện cần cập nhật',
    example: 123,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật sản phẩm sự kiện thành công',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Cập nhật sản phẩm sự kiện thành công' },
        result: { $ref: '#/components/schemas/CompleteEventProductResponseDto' }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Dữ liệu đầu vào không hợp lệ' },
        result: { type: 'null', example: null }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Không có quyền truy cập',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Không có quyền truy cập' },
        result: { type: 'null', example: null }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền cập nhật sản phẩm này',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 403 },
        message: { type: 'string', example: 'Không có quyền cập nhật sản phẩm này' },
        result: { type: 'null', example: null }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm sự kiện',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Không tìm thấy sản phẩm sự kiện' },
        result: { type: 'null', example: null }
      }
    }
  })
  async updateComplete(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: CompleteUpdateEventProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.completeEventProductService.updateCompleteEventProduct(
      id,
      updateDto,
      userId,
    );
    return ApiResponseDto.success<CompleteEventProductResponseDto>(
      product,
      'Cập nhật sản phẩm sự kiện thành công',
    );
  }

  /**
   * Lấy chi tiết hoàn chỉnh sản phẩm sự kiện
   * @param id ID của sản phẩm sự kiện
   * @param userId ID của người dùng hiện tại
   * @returns Chi tiết hoàn chỉnh sản phẩm sự kiện
   */
  @Get(':id/complete')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy chi tiết hoàn chỉnh sản phẩm sự kiện',
    description: `Lấy thông tin chi tiết hoàn chỉnh sản phẩm sự kiện bao gồm:
    - Thông tin cơ bản từ customer_products
    - Thông tin sự kiện từ event_products
    - Danh sách vé từ event_product_tickets
    - Danh sách images từ entity_has_media`,
  })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm sự kiện',
    example: 123,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy chi tiết sản phẩm sự kiện thành công',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Lấy chi tiết sản phẩm sự kiện thành công' },
        result: { $ref: '#/components/schemas/CompleteEventProductResponseDto' }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Không có quyền truy cập',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Không có quyền truy cập' },
        result: { type: 'null', example: null }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền xem sản phẩm này',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 403 },
        message: { type: 'string', example: 'Không có quyền xem sản phẩm này' },
        result: { type: 'null', example: null }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm sự kiện',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Không tìm thấy sản phẩm sự kiện' },
        result: { type: 'null', example: null }
      }
    }
  })
  async getCompleteDetail(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.completeEventProductService.getCompleteEventProduct(
      id,
      userId,
    );
    return ApiResponseDto.success<CompleteEventProductResponseDto>(
      product,
      'Lấy chi tiết sản phẩm sự kiện thành công',
    );
  }
}
