import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { KnowledgeFileUserController, VectorStoreUserController, TrackingUserController } from './controllers';
import { KnowledgeFileUserService, VectorStoreUserService, TrackingUserService } from './services';
import { KnowledgeFile, VectorStore, VectorStoreFile } from '../entities';
import { KnowledgeFileRepository, VectorStoreRepository, VectorStoreFileRepository } from '../repositories';
import { S3Service } from '@shared/services/s3.service';
import { KnowledgeFileUserValidationHelper } from './helpers';
import { RagFileProcessingService } from '@shared/services/ai/rag-file-processing.service';
import { UserRagApiKey } from '@modules/user/entities';
import { UserRagApiKeyRepository } from '@modules/user/repositories';

@Module({
  imports: [
    TypeOrmModule.forFeature([KnowledgeFile, VectorStore, VectorStoreFile, UserRagApiKey]),
    HttpModule,
  ],
  controllers: [KnowledgeFileUserController, VectorStoreUserController, TrackingUserController],
  providers: [
    KnowledgeFileUserService,
    VectorStoreUserService,
    TrackingUserService,
    KnowledgeFileRepository,
    VectorStoreRepository,
    VectorStoreFileRepository,
    UserRagApiKeyRepository,
    KnowledgeFileUserValidationHelper,
    S3Service,
    RagFileProcessingService
  ],
  exports: [KnowledgeFileUserService, VectorStoreUserService],
})
export class KnowledgeFilesUserModule {}
