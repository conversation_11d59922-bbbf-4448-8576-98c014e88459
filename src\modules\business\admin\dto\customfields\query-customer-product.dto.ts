import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@dto/query.dto';
import { PriceTypeEnum } from '@modules/business/enums';
import { EntityStatusEnum } from '@modules/business/enums/entity-status.enum';

/**
 * DTO cho các tham số truy vấn danh sách sản phẩm khách hàng
 */
export class QueryCustomerProductDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Thời gian tạo từ (timestamp)',
    example: 1625097600000,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createdAtFrom?: number;

  @ApiPropertyOptional({
    description: 'Thời gian tạo đến (timestamp)',
    example: 1625184000000,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createdAtTo?: number;

  @ApiPropertyOptional({
    description: 'Gi<PERSON> tối thiểu',
    example: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  minPrice?: number;

  @ApiPropertyOptional({
    description: 'Giá tối đa',
    example: 1000,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  maxPrice?: number;

  @ApiPropertyOptional({
    description: 'Loại giá',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
  })
  @IsOptional()
  @IsEnum(PriceTypeEnum)
  @Type(() => String)
  typePrice?: PriceTypeEnum;

  @ApiPropertyOptional({
    description: 'Lọc theo tags (phân cách bằng dấu phẩy)',
    example: 'tag1,tag2',
  })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiPropertyOptional({
    description: 'Lọc theo ID người dùng',
    example: 123,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userId?: number;

  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái sản phẩm',
    enum: EntityStatusEnum,
    example: EntityStatusEnum.PENDING,
  })
  @IsOptional()
  @IsEnum(EntityStatusEnum)
  @Type(() => String)
  status?: EntityStatusEnum;

  @ApiPropertyOptional({
    description: 'Lọc theo loại sản phẩm',
    example: 'PHYSICAL',
  })
  @IsOptional()
  @IsString()
  productType?: string;


}
