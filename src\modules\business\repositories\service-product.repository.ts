import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ServiceProduct } from '@modules/business/entities';

/**
 * Repository cho ServiceProduct entity
 * Xử lý các thao tác database cho sản phẩm dịch vụ
 */
@Injectable()
export class ServiceProductRepository {
  private readonly logger = new Logger(ServiceProductRepository.name);

  constructor(
    @InjectRepository(ServiceProduct)
    private readonly repository: Repository<ServiceProduct>,
  ) {}

  /**
   * Tạo sản phẩm dịch vụ mới
   * @param data Dữ liệu sản phẩm dịch vụ
   * @returns Sản phẩm dịch vụ đã tạo
   */
  async create(data: Partial<ServiceProduct>): Promise<ServiceProduct> {
    const serviceProduct = this.repository.create({
      ...data,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    return this.repository.save(serviceProduct);
  }

  /**
   * Tìm sản phẩm dịch vụ theo ID
   * @param id ID sản phẩm dịch vụ (cũng là customer product ID)
   * @returns Sản phẩm dịch vụ hoặc null
   */
  async findById(id: number): Promise<ServiceProduct | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Cập nhật sản phẩm dịch vụ
   * @param id ID sản phẩm dịch vụ
   * @param data Dữ liệu cập nhật
   * @returns Sản phẩm dịch vụ đã cập nhật
   */
  async update(id: number, data: Partial<ServiceProduct>): Promise<ServiceProduct | null> {
    await this.repository.update(id, {
      ...data,
      updatedAt: Date.now(),
    });
    return this.findById(id);
  }

  /**
   * Xóa sản phẩm dịch vụ
   * @param id ID sản phẩm dịch vụ
   */
  async delete(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Kiểm tra sản phẩm dịch vụ có tồn tại không
   * @param id ID sản phẩm dịch vụ
   * @returns true nếu tồn tại
   */
  async exists(id: number): Promise<boolean> {
    const count = await this.repository.count({
      where: { id },
    });
    return count > 0;
  }

  /**
   * Lưu sản phẩm dịch vụ
   * @param serviceProduct Sản phẩm dịch vụ cần lưu
   * @returns Sản phẩm dịch vụ đã lưu
   */
  async save(serviceProduct: ServiceProduct): Promise<ServiceProduct> {
    return this.repository.save(serviceProduct);
  }

  /**
   * Đếm số lượng sản phẩm dịch vụ
   * @returns Số lượng sản phẩm dịch vụ
   */
  async count(): Promise<number> {
    return this.repository.count();
  }
}
