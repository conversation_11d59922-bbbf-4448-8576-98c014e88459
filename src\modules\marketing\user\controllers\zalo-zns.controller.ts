import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, getSchemaPath, ApiBody } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ZaloZnsService } from '../services/zalo-zns.service';
import { ZaloZnsTemplate, ZaloZnsMessage } from '../entities';
import {
  RegisterZnsTemplateDto,
  SendZnsMessageDto,
  ZnsMessageQueryDto,
  ZnsMessageResponseDto,
  ZnsTemplateQueryDto,
  ZnsTemplateResponseDto,

} from '../dto/zalo';

/**
 * Controller xử lý API liên quan đến Zalo ZNS
 */
@ApiTags(SWAGGER_API_TAGS.ZALO_ZNS)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo/:oaId/zns')
export class ZaloZnsController {
  constructor(private readonly zaloZnsService: ZaloZnsService) {}

  /**
   * Lấy danh sách template ZNS từ database
   */
  @Get('templates')
  @ApiOperation({ summary: 'Lấy danh sách template ZNS từ database' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách template ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(ZnsTemplateResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  async getTemplates(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Query() queryDto: ZnsTemplateQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloZnsTemplate>>> {
    const result = await this.zaloZnsService.getZnsTemplates(user.id, oaId, queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách template ZNS thành công');
  }

  /**
   * Lấy danh sách template ZNS từ Zalo API
   */
  @Get('templates/zalo-api')
  @ApiOperation({ summary: 'Lấy danh sách template ZNS từ Zalo API' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách template ZNS từ Zalo API thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                items: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      templateId: { type: 'string', description: 'ID template' },
                      templateName: { type: 'string', description: 'Tên template' },
                      status: { type: 'string', description: 'Trạng thái template' },
                      createdTime: { type: 'number', description: 'Thời gian tạo' },
                      templateQuality: { type: 'string', description: 'Chất lượng template' },
                      reason: { type: 'string', description: 'Lý do trạng thái' },
                      listParams: { type: 'array', description: 'Danh sách tham số' },
                      listButtons: { type: 'array', description: 'Danh sách button' },
                      templateTag: { type: 'string', description: 'Tag template' },
                      price: { type: 'number', description: 'Giá template' }
                    }
                  }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number', description: 'Tổng số template' },
                    itemCount: { type: 'number', description: 'Số lượng template trên trang hiện tại' },
                    itemsPerPage: { type: 'number', description: 'Số lượng template trên mỗi trang' },
                    totalPages: { type: 'number', description: 'Tổng số trang' },
                    currentPage: { type: 'number', description: 'Trang hiện tại' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  async getTemplatesFromZaloApi(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Query() queryDto: ZnsTemplateQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<any>>> {
    const result = await this.zaloZnsService.getZnsTemplatesFromZaloApi(user.id, oaId, queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách template ZNS từ Zalo API thành công');
  }

  /**
   * Lấy thông tin chi tiết template ZNS từ database
   */
  @Get('templates/:id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết template ZNS từ database' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết template ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsTemplateResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template ZNS' })
  async getTemplateDetail(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<ZaloZnsTemplate>> {
    const result = await this.zaloZnsService.getZnsTemplateDetail(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết template ZNS thành công');
  }

  /**
   * Lấy thông tin chi tiết template ZNS từ Zalo API
   */
  @Get('templates/zalo-api/:templateId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết template ZNS từ Zalo API' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết template ZNS từ Zalo API thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                templateId: { type: 'string', description: 'ID template' },
                templateName: { type: 'string', description: 'Tên template' },
                status: { type: 'string', description: 'Trạng thái template' },
                reason: { type: 'string', description: 'Lý do trạng thái' },
                listParams: {
                  type: 'array',
                  description: 'Danh sách tham số',
                  items: {
                    type: 'object',
                    properties: {
                      name: { type: 'string', description: 'Tên thuộc tính' },
                      require: { type: 'boolean', description: 'Tính bắt buộc' },
                      type: { type: 'string', description: 'Định dạng validate' },
                      maxLength: { type: 'number', description: 'Số ký tự tối đa' },
                      minLength: { type: 'number', description: 'Số ký tự tối thiểu' },
                      acceptNull: { type: 'boolean', description: 'Có thể nhận giá trị rỗng' }
                    }
                  }
                },
                listButtons: {
                  type: 'array',
                  description: 'Danh sách button',
                  items: {
                    type: 'object',
                    properties: {
                      type: { type: 'string', description: 'Loại button' },
                      title: { type: 'string', description: 'Tiêu đề button' },
                      payload: { type: 'string', description: 'Payload của button' },
                      image_icon: { type: 'string', description: 'URL hình ảnh icon' }
                    }
                  }
                },
                timeout: { type: 'number', description: 'Thời gian timeout (milliseconds)' },
                previewUrl: { type: 'string', description: 'URL hình ảnh preview' },
                createdTime: { type: 'number', description: 'Thời gian tạo (Unix timestamp)' },
                updatedTime: { type: 'number', description: 'Thời gian cập nhật (Unix timestamp)' },
                templateQuality: { type: 'string', description: 'Chất lượng template' },
                templateTag: { type: 'string', description: 'Tag template' },
                price: { type: 'number', description: 'Giá template' }
              }
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template ZNS' })
  @ApiResponse({ status: 401, description: 'Official Account chưa có access token' })
  async getTemplateDetailFromZaloApi(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('templateId') templateId: string,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.zaloZnsService.getZnsTemplateDetailFromZaloApi(user.id, oaId, templateId);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết template ZNS từ Zalo API thành công');
  }

  /**
   * Lấy dữ liệu mẫu của template ZNS từ Zalo API
   */
  @Get('templates/zalo-api/:templateId/sample-data')
  @ApiOperation({ summary: 'Lấy dữ liệu mẫu của template ZNS từ Zalo API' })
  @ApiResponse({
    status: 200,
    description: 'Lấy dữ liệu mẫu template ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                template_id: { type: 'string', description: 'ID template' },
                sample_data: {
                  type: 'object',
                  description: 'Dữ liệu mẫu cho template',
                  additionalProperties: { type: 'string' }
                },
                preview_content: { type: 'string', description: 'Nội dung template sau khi thay thế dữ liệu mẫu' },
                preview_url: { type: 'string', description: 'URL preview (nếu có hình ảnh)' }
              }
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template ZNS' })
  @ApiResponse({ status: 401, description: 'Official Account chưa có access token' })
  async getTemplateSampleData(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('templateId') templateId: string,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.zaloZnsService.getZnsTemplateSampleData(user.id, oaId, templateId);
    return ApiResponseDto.success(result, 'Lấy dữ liệu mẫu template ZNS thành công');
  }

  /**
   * Kiểm tra trạng thái ZNS của Official Account
   */
  @Get('status')
  @ApiOperation({
    summary: 'Kiểm tra trạng thái ZNS của Official Account',
    description: 'Kiểm tra xem Official Account có quyền sử dụng ZNS không'
  })
  @ApiResponse({ status: 200, description: 'Lấy trạng thái ZNS thành công' })
  @ApiResponse({ status: 401, description: 'Official Account chưa có access token' })
  async getZnsStatus(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.zaloZnsService.getZnsStatus(user.id, oaId);
    return ApiResponseDto.success(result, 'Lấy trạng thái ZNS thành công');
  }

  /**
   * Lấy thông tin quota ZNS của Official Account
   */
  @Get('quota')
  @ApiOperation({
    summary: 'Lấy thông tin quota ZNS của Official Account',
    description: 'Lấy thông tin hạn mức gửi ZNS hàng ngày và hàng tháng của Official Account'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin quota ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                dailyQuota: {
                  type: 'number',
                  description: 'Số thông báo ZNS OA được gửi trong 1 ngày',
                  example: 1000
                },
                remainingQuota: {
                  type: 'number',
                  description: 'Số thông báo ZNS OA được gửi trong ngày còn lại',
                  example: 750
                },
                dailyQuotaPromotion: {
                  type: 'number',
                  nullable: true,
                  description: 'Số tin ZNS hậu mãi OA được gửi trong ngày (từ 1/11 sẽ trả về null)',
                  example: null
                },
                remainingQuotaPromotion: {
                  type: 'number',
                  nullable: true,
                  description: 'Số tin ZNS hậu mãi còn lại OA được gửi trong ngày (từ 1/11 sẽ trả về null)',
                  example: null
                },
                monthlyPromotionQuota: {
                  type: 'number',
                  description: 'Số tin ZNS hậu mãi OA được gửi trong tháng',
                  example: 5000
                },
                remainingMonthlyPromotionQuota: {
                  type: 'number',
                  description: 'Số tin ZNS hậu mãi còn lại OA được gửi trong tháng',
                  example: 3200
                },
                estimatedNextMonthPromotionQuota: {
                  type: 'number',
                  description: 'Số tin ZNS hậu mãi dự kiến mà OA có thể gửi trong tháng tiếp theo',
                  example: 5500
                }
              }
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 401, description: 'Official Account chưa có access token' })
  async getZnsQuota(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.zaloZnsService.getZnsQuota(user.id, oaId);
    return ApiResponseDto.success(result, 'Lấy thông tin quota ZNS thành công');
  }

  /**
   * Đăng ký template ZNS
   */
  @Post('templates')
  @UsePipes(new ValidationPipe({
    transform: true,
    whitelist: false,  // Tắt whitelist validation cho endpoint này
    forbidNonWhitelisted: false
  }))
  @ApiOperation({
    summary: 'Đăng ký template ZNS',
    description: `
Đăng ký template ZNS mới theo chuẩn Zalo API.

**Lưu ý quan trọng:**
- template_name: 10-60 ký tự
- template_type: 1=ZNS tùy chỉnh, 2=ZNS xác thực, 3=ZNS yêu cầu thanh toán, 4=ZNS voucher, 5=ZNS đánh giá dịch vụ
- tag: 1=Transaction, 2=Customer care, 3=Promotion
- layout: Bắt buộc có body, tùy chọn header và footer
- tracking_id: Mã tracking do đối tác tự định nghĩa
- note: Ghi chú kiểm duyệt (1-400 ký tự)

**Param Types:**
- 1: Tên khách hàng (30 ký tự)
- 2: Số điện thoại (15 ký tự)
- 3: Địa chỉ (200 ký tự)
- 4: Mã số (30 ký tự)
- 5: Nhãn tùy chỉnh (30 ký tự)
- 6: Trạng thái giao dịch (30 ký tự)
- 7: Thông tin liên hệ (50 ký tự)
- 8: Giới tính/Danh xưng (5 ký tự)
- 9: Tên sản phẩm/Thương hiệu (200 ký tự)
- 10: Số lượng/Số tiền (20 ký tự)
- 11: Thời gian (20 ký tự)
- 12: OTP (10 ký tự)
- 13: URL (200 ký tự)
- 14: Tiền tệ VNĐ (12 ký tự)
- 15: Bank transfer note (90 ký tự)
    `
  })
  @ApiBody({
    description: 'Dữ liệu đăng ký template ZNS',
    examples: {
      'Thông báo đơn hàng cơ bản': {
        summary: 'Template thông báo đơn hàng đơn giản',
        description: 'Template cơ bản với title và paragraph',
        value: {
          "template_name": "Thông báo đơn hàng cơ bản",
          "template_type": 1,
          "tag": "1",
          "layout": {
            "body": {
              "components": [
                {
                  "TITLE": {
                    "value": "Xác nhận đơn hàng"
                  }
                },
                {
                  "PARAGRAPH": {
                    "value": "Cảm ơn <customer_name> đã mua hàng tại cửa hàng. Đơn hàng <order_code> đã được xác nhận với tổng tiền <total_amount>."
                  }
                }
              ]
            }
          },
          "params": [
            {
              "name": "customer_name",
              "type": 1,
              "sample_value": "Nguyễn Văn A"
            },
            {
              "name": "order_code",
              "type": 4,
              "sample_value": "DH001"
            },
            {
              "name": "total_amount",
              "type": 14,
              "sample_value": "500000"
            }
          ],
          "tracking_id": "order_basic_001",
          "note": "Template thông báo đơn hàng cơ bản"
        }
      },
      'Template xác thực OTP': {
        summary: 'Template xác thực với mã OTP',
        description: 'Template ZNS xác thực đơn giản chỉ với OTP và paragraph',
        value: {
          "template_name": "Mã xác thực đăng nhập",
          "template_type": 2,
          "tag": "1",
          "layout": {
            "header": {
              "components": [
                {
                  "LOGO": {
                    "value": "https://example.com/logo.png"
                  }
                }
              ]
            },
            "body": {
              "components": [
                {
                  "OTP": {
                    "value": "<otp_code>"
                  }
                },
                {
                  "PARAGRAPH": {
                    "value": "Tuyệt đối KHÔNG chia sẻ mã xác thực cho bất kỳ ai dưới bất kỳ hình thức nào. Mã xác thực có hiệu lực trong 5 phút."
                  }
                }
              ]
            }
          },
          "params": [
            {
              "name": "otp_code",
              "type": 12,
              "sample_value": "123456"
            }
          ],
          "tracking_id": "otp_auth_001",
          "note": "Template xác thực OTP đăng nhập"
        }
      },
      'Template đơn giản nhất': {
        summary: 'Template chỉ có paragraph',
        description: 'Template ZNS tùy chỉnh đơn giản nhất chỉ có 1 paragraph',
        value: {
          "template_name": "Thông báo đơn giản",
          "template_type": 1,
          "tag": "2",
          "layout": {
            "body": {
              "components": [
                {
                  "PARAGRAPH": {
                    "value": "Xin chào <customer_name>, cảm ơn bạn đã sử dụng dịch vụ của chúng tôi."
                  }
                }
              ]
            }
          },
          "params": [
            {
              "name": "customer_name",
              "type": 1,
              "sample_value": "Nguyễn Văn A"
            }
          ],
          "tracking_id": "simple_001"
        }
      },
      'Thông báo đơn hàng với button': {
        summary: 'Template thông báo đơn hàng có nút bấm',
        description: 'Template với title, paragraph và button',
        value: {
          "template_name": "Thông báo đơn hàng với button",
          "template_type": 1,
          "tag": 1,
          "layout": {
            "body": {
              "components": [
                {
                  "TITLE": {
                    "value": "Đơn hàng đã được xác nhận"
                  }
                },
                {
                  "PARAGRAPH": {
                    "value": "Xin chào <customer_name>, đơn hàng <order_code> của bạn đã được xác nhận. Tổng tiền: <total_amount> VNĐ. Thời gian giao hàng dự kiến: <delivery_time>."
                  }
                }
              ]
            },
            "footer": {
              "components": [
                {
                  "BUTTONS": {
                    "items": [
                      {
                        "content": "https://shop.example.com/order/<order_code>",
                        "type": 1,
                        "title": "Xem chi tiết đơn hàng"
                      }
                    ]
                  }
                }
              ]
            }
          },
          "params": [
            {
              "name": "customer_name",
              "type": 1,
              "sample_value": "Trần Thị B"
            },
            {
              "name": "order_code",
              "type": 4,
              "sample_value": "DH002"
            },
            {
              "name": "total_amount",
              "type": 14,
              "sample_value": "750000"
            },
            {
              "name": "delivery_time",
              "type": 11,
              "sample_value": "2-3 ngày"
            }
          ],
          "tracking_id": "order_with_button_001",
          "note": "Template thông báo đơn hàng có button xem chi tiết"
        }
      },
      'Thông báo đơn hàng với bảng': {
        summary: 'Template thông báo đơn hàng có bảng thông tin',
        description: 'Template với title, paragraph và table',
        value: {
          "template_name": "Thông báo đơn hàng với bảng",
          "template_type": 1,
          "tag": 1,
          "layout": {
            "body": {
              "components": [
                {
                  "TITLE": {
                    "value": "Chi tiết đơn hàng"
                  }
                },
                {
                  "PARAGRAPH": {
                    "value": "Cảm ơn <customer_name> đã đặt hàng tại cửa hàng chúng tôi."
                  }
                },
                {
                  "TABLE": {
                    "rows": [
                      {
                        "value": "<order_code>",
                        "title": "Mã đơn hàng",
                        "row_type": 1
                      },
                      {
                        "value": "<customer_phone>",
                        "title": "Số điện thoại"
                      },
                      {
                        "value": "<total_amount>",
                        "title": "Tổng tiền"
                      },
                      {
                        "value": "<order_status>",
                        "title": "Trạng thái"
                      },
                      {
                        "value": "<order_date>",
                        "title": "Ngày đặt hàng"
                      }
                    ]
                  }
                }
              ]
            }
          },
          "params": [
            {
              "name": "customer_name",
              "type": 1,
              "sample_value": "Lê Văn C"
            },
            {
              "name": "order_code",
              "type": 4,
              "sample_value": "DH003"
            },
            {
              "name": "customer_phone",
              "type": 2,
              "sample_value": "**********"
            },
            {
              "name": "total_amount",
              "type": 14,
              "sample_value": "1200000"
            },
            {
              "name": "order_status",
              "type": 6,
              "sample_value": "Đã xác nhận"
            },
            {
              "name": "order_date",
              "type": 11,
              "sample_value": "17/06/2025"
            }
          ],
          "tracking_id": "order_with_table_001",
          "note": "Template thông báo đơn hàng có bảng chi tiết"
        }
      },
      'Template OTP xác thực': {
        summary: 'Template gửi mã OTP xác thực',
        description: 'Template ZNS xác thực với mã OTP',
        value: {
          "template_name": "Mã OTP xác thực tài khoản",
          "template_type": 2,
          "tag": 1,
          "layout": {
            "body": {
              "components": [
                {
                  "TITLE": {
                    "value": "Mã xác thực OTP"
                  }
                },
                {
                  "PARAGRAPH": {
                    "value": "Xin chào <customer_name>, mã OTP để xác thực tài khoản của bạn là:"
                  }
                },
                {
                  "OTP": {
                    "value": "<otp_code>"
                  }
                },
                {
                  "PARAGRAPH": {
                    "value": "Mã OTP có hiệu lực trong 5 phút. Vui lòng không chia sẻ mã này với bất kỳ ai."
                  }
                }
              ]
            }
          },
          "params": [
            {
              "name": "customer_name",
              "type": 1,
              "sample_value": "Phạm Thị D"
            },
            {
              "name": "otp_code",
              "type": 12,
              "sample_value": "123456"
            }
          ],
          "tracking_id": "otp_auth_001",
          "note": "Template gửi mã OTP xác thực"
        }
      },
      'Template voucher khuyến mãi': {
        summary: 'Template gửi voucher khuyến mãi',
        description: 'Template ZNS voucher với thông tin khuyến mãi',
        value: {
          "template_name": "Voucher khuyến mãi đặc biệt",
          "template_type": 4,
          "tag": 3,
          "layout": {
            "body": {
              "components": [
                {
                  "TITLE": {
                    "value": "🎉 Voucher khuyến mãi đặc biệt"
                  }
                },
                {
                  "PARAGRAPH": {
                    "value": "Chúc mừng <customer_name>! Bạn đã nhận được voucher khuyến mãi đặc biệt từ cửa hàng."
                  }
                },
                {
                  "VOUCHER": {
                    "name": "Giảm <discount_amount>",
                    "condition": "Cho đơn hàng trên <min_order_amount>",
                    "voucher_code": "<voucher_code>",
                    "start_date": "<start_date>",
                    "end_date": "<end_date>",
                    "display_code": 1
                  }
                }
              ]
            },
            "footer": {
              "components": [
                {
                  "BUTTONS": {
                    "items": [
                      {
                        "content": "https://shop.example.com/voucher",
                        "type": 1,
                        "title": "Sử dụng ngay"
                      }
                    ]
                  }
                }
              ]
            }
          },
          "params": [
            {
              "name": "customer_name",
              "type": 1,
              "sample_value": "Hoàng Văn E"
            },
            {
              "name": "discount_amount",
              "type": 14,
              "sample_value": "50000"
            },
            {
              "name": "min_order_amount",
              "type": 14,
              "sample_value": "200000"
            },
            {
              "name": "voucher_code",
              "type": 4,
              "sample_value": "SALE50K"
            },
            {
              "name": "start_date",
              "type": 11,
              "sample_value": "17/06/2025"
            },
            {
              "name": "end_date",
              "type": 11,
              "sample_value": "30/06/2025"
            }
          ],
          "tracking_id": "voucher_promo_001",
          "note": "Template voucher khuyến mãi"
        }
      },
      'Template thanh toán': {
        summary: 'Template yêu cầu thanh toán',
        description: 'Template ZNS yêu cầu thanh toán với thông tin chuyển khoản',
        value: {
          "template_name": "Yêu cầu thanh toán đơn hàng",
          "template_type": 3,
          "tag": 1,
          "layout": {
            "body": {
              "components": [
                {
                  "TITLE": {
                    "value": "Thông tin thanh toán"
                  }
                },
                {
                  "PARAGRAPH": {
                    "value": "Xin chào <customer_name>, vui lòng thanh toán cho đơn hàng <order_code> theo thông tin bên dưới:"
                  }
                },
                {
                  "PAYMENT": {
                    "bank_code": "970416",
                    "account_name": "CÔNG TY TNHH ABC",
                    "account_number": "**********",
                    "amount": "<payment_amount>",
                    "note": "<transfer_note>"
                  }
                }
              ]
            },
            "footer": {
              "components": [
                {
                  "BUTTONS": {
                    "items": [
                      {
                        "content": "https://shop.example.com/payment/<order_code>",
                        "type": 1,
                        "title": "Xác nhận thanh toán"
                      }
                    ]
                  }
                }
              ]
            }
          },
          "params": [
            {
              "name": "customer_name",
              "type": 1,
              "sample_value": "Ngô Thị F"
            },
            {
              "name": "order_code",
              "type": 4,
              "sample_value": "DH004"
            },
            {
              "name": "payment_amount",
              "type": 14,
              "sample_value": "850000"
            },
            {
              "name": "transfer_note",
              "type": 15,
              "sample_value": "Thanh toan don hang DH004"
            }
          ],
          "tracking_id": "payment_request_001",
          "note": "Template yêu cầu thanh toán"
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Đăng ký template ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsTemplateResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ',
    schema: {
      example: {
        "code": 9999,
        "message": "Lỗi khi tạo template ZNS: Data is invalid | Template tracking id must not be null;Template tag must not be null;Template note must not be null;Template name must not be null;Template type must not be null;Layout must not be null"
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Official Account chưa có access token' })
  async registerTemplate(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Body() registerDto: RegisterZnsTemplateDto,
  ): Promise<ApiResponseDto<ZaloZnsTemplate>> {
    const result = await this.zaloZnsService.registerZnsTemplate(user.id, oaId, registerDto);
    return ApiResponseDto.success(result, 'Đăng ký template ZNS thành công');
  }

  /**
   * Cập nhật trạng thái template ZNS
   */
  @Put('templates/:id/status/:status')
  @ApiOperation({ summary: 'Cập nhật trạng thái template ZNS' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái template ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsTemplateResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template ZNS' })
  async updateTemplateStatus(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
    @Param('status') status: string,
  ): Promise<ApiResponseDto<ZaloZnsTemplate>> {
    const result = await this.zaloZnsService.updateZnsTemplateStatus(user.id, oaId, id, status);
    return ApiResponseDto.success(result, 'Cập nhật trạng thái template ZNS thành công');
  }

  /**
   * Gửi tin nhắn ZNS
   */
  @Post('messages')
  @ApiOperation({ summary: 'Gửi tin nhắn ZNS' })
  @ApiResponse({
    status: 201,
    description: 'Gửi tin nhắn ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsMessageResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template ZNS' })
  @ApiResponse({ status: 400, description: 'Template ZNS chưa được phê duyệt' })
  async sendMessage(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Body() sendDto: SendZnsMessageDto,
  ): Promise<ApiResponseDto<ZaloZnsMessage>> {
    const result = await this.zaloZnsService.sendZnsMessage(user.id, oaId, sendDto);
    return ApiResponseDto.success(result, 'Gửi tin nhắn ZNS thành công');
  }

  /**
   * Lấy lịch sử tin nhắn ZNS
   */
  @Get('messages')
  @ApiOperation({ summary: 'Lấy lịch sử tin nhắn ZNS' })
  @ApiResponse({
    status: 200,
    description: 'Lấy lịch sử tin nhắn ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(ZnsMessageResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  async getMessages(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Query() queryDto: ZnsMessageQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloZnsMessage>>> {
    const result = await this.zaloZnsService.getZnsMessages(user.id, oaId, queryDto);
    return ApiResponseDto.success(result, 'Lấy lịch sử tin nhắn ZNS thành công');
  }

  /**
   * Lấy thông tin chi tiết tin nhắn ZNS
   */
  @Get('messages/:id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết tin nhắn ZNS' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết tin nhắn ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsMessageResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy tin nhắn ZNS' })
  async getMessageDetail(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<ZaloZnsMessage>> {
    const result = await this.zaloZnsService.getZnsMessageDetail(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết tin nhắn ZNS thành công');
  }
}
