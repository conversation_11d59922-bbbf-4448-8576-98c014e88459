import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsIn, Min, Max } from 'class-validator';

/**
 * DTO cho việc cập nhật cấu hình công ty
 */
export class UpdateCompanyConfigDto {
  @ApiProperty({
    description: 'Cấu hình nhận diện mã thanh toán',
    example: 'on',
    enum: ['on', 'off'],
    required: false
  })
  @IsOptional()
  @IsIn(['on', 'off'], { message: 'Payment code phải là "on" hoặc "off"' })
  paymentCode?: 'on' | 'off';

  @ApiProperty({
    description: 'Cấu hình tiền tố mã thanh toán',
    example: 'PAY',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Payment code prefix phải là chuỗi' })
  paymentCodePrefix?: string;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> hình độ dài tối thiểu hậu tố mã thanh toán',
    example: 4,
    minimum: 1,
    maximum: 20,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: 'Payment code suffix from phải là số' })
  @Min(1, { message: 'Payment code suffix from phải lớn hơn hoặc bằng 1' })
  @Max(20, { message: 'Payment code suffix from phải nhỏ hơn hoặc bằng 20' })
  paymentCodeSuffixFrom?: number;

  @ApiProperty({
    description: 'Cấu hình độ dài tối đa hậu tố mã thanh toán',
    example: 8,
    minimum: 1,
    maximum: 20,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: 'Payment code suffix to phải là số' })
  @Min(1, { message: 'Payment code suffix to phải lớn hơn hoặc bằng 1' })
  @Max(20, { message: 'Payment code suffix to phải nhỏ hơn hoặc bằng 20' })
  paymentCodeSuffixTo?: number;

  @ApiProperty({
    description: 'Cấu hình kiểu ký tự hậu tố mã thanh toán',
    example: 'NumberAndLetter',
    enum: ['NumberAndLetter', 'NumberOnly'],
    required: false
  })
  @IsOptional()
  @IsIn(['NumberAndLetter', 'NumberOnly'], { 
    message: 'Payment code suffix character type phải là "NumberAndLetter" hoặc "NumberOnly"' 
  })
  paymentCodeSuffixCharacterType?: 'NumberAndLetter' | 'NumberOnly';

  @ApiProperty({
    description: 'Cấu hình số lượng giao dịch (số nguyên dương hoặc "Unlimited")',
    example: 1000,
    required: false
  })
  @IsOptional()
  transactionAmount?: number | 'Unlimited';
}
