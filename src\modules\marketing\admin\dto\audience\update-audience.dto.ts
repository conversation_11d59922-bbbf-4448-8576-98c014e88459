import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsOptional, IsString, IsPhoneNumber, IsEnum } from 'class-validator';
import { CreateCustomFieldDto } from './create-custom-field.dto';
import { Type } from 'class-transformer';
import { ImageTypeEnum } from '@shared/utils/file/image-media_type.util';

/**
 * DTO cho cập nhật audience
 */
export class UpdateAudienceDto {
  /**
   * Tên của khách hàng
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({
    description: 'Tên của khách hàng',
    example: 'Nguyễn Văn A',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên phải là chuỗi' })
  name?: string;

  /**
   * Email của khách hàng
   * @example "<EMAIL>"
   */
  @ApiProperty({
    description: '<PERSON>ail của khách hàng',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { message: '<PERSON><PERSON> không hợp lệ' })
  email?: string;

  /**
   * <PERSON><PERSON> điện thoại của khách hàng
   * @example "+84912345678"
   */
  @ApiProperty({
    description: 'Số điện thoại của khách hàng',
    example: '+84912345678',
    required: false,
  })
  @IsOptional()
  @IsPhoneNumber(undefined, { message: 'Số điện thoại không hợp lệ' })
  phone?: string;

  /**
   * Loại file avatar (chỉ cho phép image) - Nếu có thì sẽ trả về URL tạm thời để upload
   * @example "image/jpeg"
   */
  @ApiProperty({
    description: 'Loại file avatar (chỉ cho phép image) - Nếu có thì sẽ trả về URL tạm thời để upload',
    enum: ImageTypeEnum,
    example: ImageTypeEnum.JPEG,
    required: false,
  })
  @IsOptional()
  @IsEnum(ImageTypeEnum, { message: 'Loại file phải là image (JPEG, PNG, WEBP, GIF)' })
  avatarMediaType?: ImageTypeEnum;

  /**
   * Các trường tùy chỉnh
   */
  @ApiProperty({
    description: 'Các trường tùy chỉnh',
    type: [CreateCustomFieldDto],
    required: false,
  })
  @IsOptional()
  @Type(() => CreateCustomFieldDto)
  customFields?: CreateCustomFieldDto[];

  /**
   * Các tag ID
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Các tag ID',
    example: [1, 2, 3],
    required: false,
    type: [Number],
  })
  @IsOptional()
  tagIds?: number[];
}
