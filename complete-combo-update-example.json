{"description": "🎯 EXAMPLE HOÀN CHỈNH - Combo Product Update với Product & Combo Images", "fullExample": {"customerProduct": {"name": "Combo <PERSON> thun + Quần jean cao cấp", "description": "Combo thời trang nam gồm áo thun cotton và quần jean slim fit", "productType": "COMBO", "tags": ["combo", "th<PERSON>i trang", "nam", "t<PERSON><PERSON><PERSON> k<PERSON>"]}, "customerProductPricing": {"displayPrice": {"listPrice": 800000, "salePrice": 650000, "currency": "VND"}, "typePrice": "HAS_PRICE"}, "customFields": [{"customFieldId": 1, "value": {"value": "Premium"}}], "comboProduct": {"comboItems": [{"productId": 5, "total": 2}, {"productId": 14, "total": 1}, {"productId": 1, "total": 1}], "maxQuantity": 100}, "productImageOperations": [{"operation": "add", "mediaId": "dbadc742-60b9-4fac-969d-7330ed1daa5e"}, {"operation": "delete", "entityMediaId": 123}], "comboImageOperations": [{"operation": "add", "mediaId": "cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2"}, {"operation": "delete", "entityMediaId": 456}]}, "imageArchitecture": {"productImages": {"description": "Ảnh chính của combo product", "storage": "entity_has_media.product_id = 15", "purpose": "Ảnh đại diện cho combo, hiển thị trong danh sách sản phẩm", "examples": ["Ảnh tổng thể combo", "Ảnh bao bì", "Ảnh marketing"]}, "comboImages": {"description": "Ảnh chi tiết combo", "storage": "entity_has_media.product_combo_id = 15", "purpose": "Ảnh chi tiết về combo, hướng dẫn sử dụng", "examples": ["Ảnh từng sản phẩm trong combo", "Ảnh hướng dẫn", "Ảnh so sánh"]}}, "databaseOperations": {"productImageAdd": "INSERT INTO entity_has_media (product_id, media_id) VALUES (15, 'uuid')", "comboImageAdd": "INSERT INTO entity_has_media (product_combo_id, media_id) VALUES (15, 'uuid')", "productImageDelete": "DELETE FROM entity_has_media WHERE id = 123 AND product_id = 15", "comboImageDelete": "DELETE FROM entity_has_media WHERE id = 456 AND product_combo_id = 15"}, "responseStructure": {"customerProduct": "Thông tin từ customer_products table", "comboProduct": "Thông tin từ combo_products table + calculated price", "productImages": "<PERSON><PERSON><PERSON>nh từ entity_has_media.product_id", "comboImages": "A<PERSON>y <PERSON>nh từ entity_has_media.product_combo_id", "operationResults": {"productImages": "Kết quả operations cho product images", "comboImages": "Kết quả operations cho combo images"}}, "useCases": {"productImages": ["Ảnh thumbnail trong danh s<PERSON>ch sản phẩm", "Ảnh ch<PERSON>h trong trang chi tiết", "Ảnh cho SEO và social sharing", "Ảnh trong giỏ hàng và checkout"], "comboImages": ["Ảnh chi tiết từng item trong combo", "Ảnh hướng dẫn cách sử dụng combo", "Ảnh so s<PERSON>h giá trị combo vs mua lẻ", "Ảnh gallery chi ti<PERSON><PERSON> sản ph<PERSON>m"]}}