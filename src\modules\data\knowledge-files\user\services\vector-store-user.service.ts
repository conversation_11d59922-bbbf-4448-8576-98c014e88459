import { Injectable, Logger } from '@nestjs/common';
import { DataSource, In } from 'typeorm';
import {
  KnowledgeFileRepository,
  VectorStoreFileRepository,
  VectorStoreRepository,
} from '../../repositories';
import {
  AssignFilesDto,
  AssignFilesResponseDto,
  CreateBulkVectorStoresDto,
  VectorStoreItemDto,
  BulkVectorStoresResponseDto,
  DeleteVectorStoresDto,
  ProcessedFileDetailDto,
  QueryVectorStoreDto,
  UpdateVectorStoreDto,
  VectorStoreResponseDto,
} from '../dto';
import { plainToInstance } from 'class-transformer';
import { PaginatedResult } from '@common/response/api-response-dto';
import { OwnerType } from '@shared/enums';
import { AppException } from '@common/exceptions/app.exception';
import { KNOWLEDGE_FILE_ERROR_CODES } from '@modules/data/knowledge-files/exceptions';
import { KnowledgeFileUserValidationHelper } from '../helpers';
import { RagFileProcessingService } from '@shared/services/ai/rag-file-processing.service';
import { RedisService } from '@shared/services/redis.service';
import { UserRagApiKeyRepository } from '@modules/user/repositories';
import { Transactional } from 'typeorm-transactional';
import { v4 as uuidv4 } from 'uuid';
@Injectable()
export class VectorStoreUserService {
  private readonly logger = new Logger(VectorStoreUserService.name);
  private readonly RAG_API_KEY_CACHE_PREFIX = 'rag_api_key_info:';
  private readonly RAG_API_KEY_CACHE_TTL = 3600; // 1 hour

  constructor(
    private readonly vectorStoreRepository: VectorStoreRepository,
    private readonly vectorStoreFileRepository: VectorStoreFileRepository,
    private readonly knowledgeFileRepository: KnowledgeFileRepository,
    private readonly dataSource: DataSource,
    private readonly validationHelper: KnowledgeFileUserValidationHelper,
    private readonly ragFileProcessingService: RagFileProcessingService,
    private readonly redisService: RedisService,
    private readonly userRagApiKeyRepository: UserRagApiKeyRepository,
  ) {}

  /**
   * Lấy RAG API key của user từ database
   * @param userId ID của user
   * @returns RAG API key của user
   */
  private async getUserRagApiKey(userId: number): Promise<string> {
    try {
      const userApiKey = await this.userRagApiKeyRepository.findByUserId(userId);

      if (!userApiKey) {
        throw new AppException(
          KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_LIST_ERROR,
          `User ${userId} chưa có RAG API key. Vui lòng liên hệ admin để được cấp API key.`
        );
      }

      this.logger.log(`Found RAG API key for user ${userId}: ${userApiKey.ragApiKey.substring(0, 10)}...`);
      return userApiKey.ragApiKey;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting RAG API key for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_LIST_ERROR,
        `Lỗi khi lấy RAG API key cho user ${userId}: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin API key từ RAG API với caching theo user
   * @param userId ID của user
   * @returns Thông tin API key bao gồm user_id và role
   */
  private async getApiKeyInfoForUser(userId: number): Promise<{user_id: number, role: string}> {
    try {
      // Tạo cache key dựa trên user ID
      const cacheKey = `${this.RAG_API_KEY_CACHE_PREFIX}user_${userId}`;

      // Try cache first
      const cachedInfo = await this.redisService.get(cacheKey);
      if (cachedInfo) {
        const parsed = JSON.parse(cachedInfo);
        this.logger.log(`API key info retrieved from cache for user ${userId}: user_id=${parsed.user_id}`);
        return parsed;
      }

      // Lấy API key của user từ database
      const userApiKey = await this.getUserRagApiKey(userId);

      // Call RAG API với API key của user
      this.logger.log(`API key info not in cache for user ${userId}, calling RAG API`);
      const apiKeyInfo = await this.ragFileProcessingService.getApiKeyInfo(userApiKey);

      // Validate API key thuộc về đúng user
      if (apiKeyInfo.user_id !== userId) {
        throw new AppException(
          KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_LIST_ERROR,
          `API key trong database không khớp với user ${userId}. Expected: ${userId}, Got: ${apiKeyInfo.user_id}`
        );
      }

      // Cache for 1 hour
      await this.redisService.setWithExpiry(
        cacheKey,
        JSON.stringify(apiKeyInfo),
        this.RAG_API_KEY_CACHE_TTL
      );

      this.logger.log(`API key info cached for user ${userId}: user_id=${apiKeyInfo.user_id}, role=${apiKeyInfo.role}`);
      return apiKeyInfo;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting API key info for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_LIST_ERROR,
        `Lỗi khi lấy thông tin API key cho user ${userId}: ${error.message}`,
      );
    }
  }

  /**
   * Tạo nhiều vector stores cùng lúc
   * @param dto Thông tin các vector stores cần tạo
   * @param userId ID của người dùng
   * @returns Thông tin các vector stores đã tạo
   */
  @Transactional()
  async createBulkVectorStores(
    dto: CreateBulkVectorStoresDto,
    userId: number,
  ): Promise<BulkVectorStoresResponseDto> {
    try {
      this.logger.log(`Bắt đầu tạo ${dto.vectorStores.length} vector stores cho user ID ${userId}`);

      // Lấy API key của user
      const userApiKey = await this.getUserRagApiKey(userId);

      // Gửi dữ liệu đến RAG API trước với API key của user
      const ragResponse = await this.ragFileProcessingService.createBulkVectorStoresWithApiKey(
        dto.vectorStores.map(vs => ({ name: vs.name })),
        userApiKey
      );

      this.logger.log(`RAG API đã tạo thành công ${ragResponse.created_count} vector stores`);

      // Lưu các vector stores vào database dựa trên response từ RAG API
      const vectorStoresToSave = ragResponse.vector_stores.map(ragVectorStore => ({
        id: ragVectorStore.id,
        name: ragVectorStore.name,
        storage: ragVectorStore.storage || 0,
        ownerType: OwnerType.USER,
        ownerId: userId,
        createdAt: ragVectorStore.created_at || Date.now(),
        updateAt: ragVectorStore.update_at || Date.now(),
      }));

      // Lưu tất cả vector stores vào database
      const savedVectorStores = await this.vectorStoreRepository.save(vectorStoresToSave);
      this.logger.log(`Đã lưu ${savedVectorStores.length} vector stores vào database`);

      // Chuyển đổi sang response DTO
      const vectorStoreResponses = savedVectorStores.map(store => ({
        storeId: store.id,
        storeName: store.name,
        size: store.storage,
        agents: 0, // Mới tạo nên chưa có agents
        files: 0,  // Mới tạo nên chưa có files
        createdAt: store.createdAt,
        updatedAt: store.updateAt,
      }));

      return plainToInstance(
        BulkVectorStoresResponseDto,
        {
          createdCount: ragResponse.created_count,
          message: ragResponse.message || `Đã tạo thành công ${ragResponse.created_count} vector stores`,
          success: ragResponse.success !== undefined ? ragResponse.success : true,
          vectorStores: vectorStoreResponses,
        },
        { excludeExtraneousValues: true },
      );
    } catch (error) {
      this.logger.error(
        `Error creating bulk vector stores: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_CREATE_ERROR,
        `Lỗi khi tạo nhiều vector stores: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách vector store của người dùng
   * @param queryDto Tham số truy vấn
   * @param userId ID của người dùng
   * @returns Danh sách vector store với phân trang
   */
  async getVectorStores(
    queryDto: QueryVectorStoreDto,
    userId: number,
  ): Promise<PaginatedResult<VectorStoreResponseDto>> {
    try {
      // Sử dụng repository method để lấy danh sách vector store
      const result =
        await this.vectorStoreRepository.findAllByUserIdWithPagination(
          queryDto,
          userId,
        );
      const vectorStores = result.items;

      // Lấy số lượng file và agent trong mỗi vector store
      const storeIds = vectorStores.map((store) => store.id);

      // Chỉ truy vấn khi có vector stores
      const fileCountMap =
        await this.vectorStoreRepository.countFilesInMultipleVectorStores(
          storeIds,
        );
      const agentCountMap =
        await this.vectorStoreRepository.countAgentsInMultipleVectorStores(
          storeIds,
        );

      // Chuyển đổi sang DTO
      const vectorStoreResponses = vectorStores.map((store) => {
        return plainToInstance(
          VectorStoreResponseDto,
          {
            storeId: store.id,
            storeName: store.name,
            size: store.storage,
            agents: agentCountMap.get(store.id) || 0,
            files: fileCountMap.get(store.id) || 0,
            createdAt: store.createdAt,
            updatedAt: store.updateAt,
          },
          { excludeExtraneousValues: true },
        );
      });

      // Trả về kết quả phân trang
      return {
        items: vectorStoreResponses,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting vector stores: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_LIST_ERROR,
        `Lỗi khi lấy danh sách vector store: ${error.message}`,
      );
    }
  }

  /**
   * Gán các file vào vector store
   * @param vectorStoreId ID của vector store
   * @param dto Thông tin các file cần gán
   * @param userId ID của người dùng
   * @returns Thông tin về việc gán file thành công, bao gồm số lượng file đã xử lý và danh sách file bị bỏ qua
   */
  @Transactional()
  async assignFilesToVectorStore(
    vectorStoreId: string,
    dto: AssignFilesDto,
    userId: number,
  ): Promise<AssignFilesResponseDto> {
    try {
      // Kiểm tra vector store có tồn tại và thuộc về người dùng không
      const vectorStore = await this.vectorStoreRepository.findOneByIdAndUserId(
        vectorStoreId,
        userId,
      );

      // Xác thực vector store tồn tại
      this.validationHelper.validateVectorStoreExists(
        vectorStore,
        vectorStoreId,
      );

      // Kiểm tra các file có tồn tại và thuộc về người dùng không
      const files = await this.knowledgeFileRepository.find({
        where: {
          id: In(dto.fileIds),
          ownedBy: userId,
          ownerType: OwnerType.USER,
        },
      });

      // Xác thực các file tồn tại
      this.validationHelper.validateFilesExist(files, dto.fileIds);

      // Kiểm tra xem file đã được gán vào vector store nào chưa
      const existingVectorStoreFiles =
        await this.vectorStoreFileRepository.find({
          where: { fileId: In(dto.fileIds) },
        });

      // Lấy thông tin chi tiết của các file
      const filesDetails = await this.knowledgeFileRepository.find({
        where: {
          id: In(dto.fileIds),
          ownedBy: userId,
          ownerType: OwnerType.USER,
        },
      });

      // Lọc ra các file cần xử lý: chưa được gán vào vector store hiện tại hoặc đã gán nhưng chưa có fileId
      const filesToAssign = dto.fileIds.filter((fileId) => {
        const existingAssignment = existingVectorStoreFiles.find(
          (vsf) => vsf.fileId === fileId && vsf.vectorStoreId === vectorStoreId
        );

        // Nếu file chưa được gán vào vector store hiện tại, cần xử lý
        if (!existingAssignment) {
          return true;
        }

        // Nếu file đã được gán vào vector store hiện tại, kiểm tra xem có fileId chưa
        const fileDetail = filesDetails.find(file => file.id === fileId);
        return fileDetail && !fileDetail.fileId; // Cần xử lý nếu file chưa có fileId
      });

      // Nếu không có file nào cần xử lý, trả về kết quả thành công
      if (filesToAssign.length === 0) {
        return {
          success: true,
          message: 'Tất cả các file đã được gán vào vector store này.',
          processedFiles: 0,
          processedFileDetails: [],
          skippedFiles: [],
          chunkSize: dto.chunkSize || 2000,
          chunkOverlap: dto.chunkOverlap || 100,
        };
      }

      // Lọc ra các bản ghi liên kết cần xóa (chỉ xóa các bản ghi liên kết với vector store khác)
      const vectorStoreFilesToRemove = existingVectorStoreFiles.filter(
        (vsf) => vsf.vectorStoreId !== vectorStoreId && filesToAssign.includes(vsf.fileId)
      );

      // Xóa các bản ghi liên kết cũ (nếu file đã được gán vào vector store khác)
      if (vectorStoreFilesToRemove.length > 0) {
        await this.vectorStoreFileRepository.remove(vectorStoreFilesToRemove);
      }

      // Lọc ra các file chưa được gán vào vector store hiện tại
      const filesToCreateAssignment = filesToAssign.filter((fileId) => {
        return !existingVectorStoreFiles.some(
          (vsf) => vsf.fileId === fileId && vsf.vectorStoreId === vectorStoreId
        );
      });

      // Tạo các bản ghi liên kết mới cho các file chưa được gán
      if (filesToCreateAssignment.length > 0) {
        const vectorStoreFiles = filesToCreateAssignment.map((fileId) => ({
          vectorStoreId,
          fileId,
        }));

        // Lưu vào database
        await this.vectorStoreFileRepository.save(vectorStoreFiles);
      }

      // Lấy danh sách file đã được gán
      const filesToProcess = files.filter((file) =>
        filesToAssign.includes(file.id),
      );

      // Gửi file đến RAG API để lấy file ID
      const processedFileDetails: ProcessedFileDetailDto[] = [];
      const skippedFiles: string[] = [];

      // Lấy API key của user
      const userApiKey = await this.getUserRagApiKey(userId);

      this.logger.log(`Bắt đầu gửi ${filesToProcess.length} file đến RAG API để lấy file ID`);

      for (const file of filesToProcess) {
        try {
          this.logger.log(`Đang gửi file ${file.id} đến RAG API để lấy file ID với storageKey ${file.storageKey}`);

          // Gọi RAG API để lấy file ID ngay lập tức với API key của user
          const processResult = await this.ragFileProcessingService.processFileFromS3KeyWithApiKey(
            file.storageKey,
            dto.chunkSize || 2000,
            dto.chunkOverlap || 100,
            vectorStoreId,
            userApiKey
          );

          this.logger.log(`Đã tạo file ID cho file ${file.id}: ${processResult.file_id}`);

          // Cập nhật fileId trong database với ID từ RAG API
          file.fileId = processResult.file_id;
          await this.knowledgeFileRepository.save(file);

          // Thêm file đã nhận ID thành công vào danh sách
          processedFileDetails.push({
            id: file.id,
            openAiFileId: file.fileId,
            fileId: file.fileId, // Thêm fileId để hiển thị trong response
          });

          this.logger.log(`Đã lưu file ID ${file.id} thành công: ${file.fileId}`);

        } catch (error) {
          this.logger.warn(
            `Lỗi khi gửi file ${file.id} đến RAG API: ${error.message}`,
            error.stack,
          );
          skippedFiles.push(file.id);
        }
      }

      const successfullyProcessedCount = processedFileDetails.length;

      const result: AssignFilesResponseDto = {
        success: true,
        message: `Đã nhận file ID từ RAG API cho ${successfullyProcessedCount} file thành công. Đã gán ${successfullyProcessedCount} file vào vector store.`,
        processedFiles: successfullyProcessedCount,
        processedFileDetails,
        skippedFiles,
        chunkSize: dto.chunkSize || 2000,
        chunkOverlap: dto.chunkOverlap || 100,
      };

      return result;
    } catch (error) {
      this.logger.error(
        `Error assigning files to vector store: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        // Re-throw existing AppException
        throw error;
      }
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_ASSIGN_FILES_ERROR,
        `Lỗi khi gán file vào vector store: ${error.message}`,
      );
    }
  }

  /**
   * Xóa các file khỏi vector store
   * @param vectorStoreId ID của vector store
   * @param fileIds Danh sách ID của các file cần xóa
   * @param userId ID của người dùng
   * @returns Thông tin về việc xóa file thành công
   */
  @Transactional()
  async removeFilesFromVectorStore(
    vectorStoreId: string,
    fileIds: string[],
    userId: number,
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      // Kiểm tra vector store có tồn tại và thuộc về người dùng không
      const vectorStore = await this.vectorStoreRepository.findOneByIdAndUserId(
        vectorStoreId,
        userId,
      );

      // Xác thực vector store tồn tại
      this.validationHelper.validateVectorStoreExists(
        vectorStore,
        vectorStoreId,
      );

      // Kiểm tra các file có tồn tại trong vector store không
      const vectorStoreFiles = await this.vectorStoreFileRepository.find({
        where: { vectorStoreId, fileId: In(fileIds) },
      });

      if (vectorStoreFiles.length === 0) {
        return {
          success: true,
          message: 'Không có file nào cần xóa khỏi vector store.',
        };
      }

      // Lấy thông tin các file để xóa khỏi OpenAI
      const filesToRemove = await this.knowledgeFileRepository.find({
        where: { id: In(vectorStoreFiles.map((vsf) => vsf.fileId)) },
      });

      // Xóa các bản ghi liên kết
      await this.vectorStoreFileRepository.remove(vectorStoreFiles);

      // Không cần xóa các file khỏi vector store trên OpenAI nữa
      this.logger.log(`Bỏ qua bước xóa file khỏi OpenAI, chỉ xóa liên kết trong database`);

      return {
        success: true,
        message: `Đã xóa ${vectorStoreFiles.length} file khỏi vector store thành công.`,
      };
    } catch (error) {
      this.logger.error(
        `Error removing files from vector store: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_REMOVE_FILES_ERROR,
        `Lỗi khi xóa file khỏi vector store: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thông tin vector store
   * @param vectorStoreId ID của vector store cần cập nhật
   * @param dto Thông tin cập nhật
   * @param userId ID của người dùng
   * @returns Thông tin vector store đã cập nhật
   */
  @Transactional()
  async updateVectorStore(
    vectorStoreId: string,
    dto: UpdateVectorStoreDto,
    userId: number,
  ): Promise<VectorStoreResponseDto> {
    try {
      this.logger.log(`Bắt đầu cập nhật vector store ${vectorStoreId} cho user ${userId}`);

      // Kiểm tra vector store có tồn tại và thuộc về người dùng không
      const vectorStore = await this.vectorStoreRepository.findOneByIdAndUserId(
        vectorStoreId,
        userId,
      );

      // Xác thực vector store tồn tại
      this.validationHelper.validateVectorStoreExists(
        vectorStore,
        vectorStoreId,
      );

      // Lấy API key của user
      const userApiKey = await this.getUserRagApiKey(userId);

      // Cập nhật trên RAG API trước với API key của user
      const ragResult = await this.ragFileProcessingService.updateVectorStoreWithApiKey(
        vectorStoreId,
        { name: dto.name },
        userApiKey
      );

      this.logger.log(`Đã cập nhật vector store ${vectorStoreId} trên RAG API`);

      // Cập nhật thông tin trong database dựa trên response từ RAG API
      vectorStore.name = ragResult.name;
      vectorStore.storage = ragResult.storage;
      vectorStore.updateAt = ragResult.update_at;

      // Lưu vào database
      await this.vectorStoreRepository.save(vectorStore);

      // Đếm số lượng file và agent trong vector store
      const fileCount =
        await this.vectorStoreRepository.countFilesByVectorStoreId(
          vectorStoreId,
        );
      const agentCount =
        await this.vectorStoreRepository.countAgentsByVectorStoreId(
          vectorStoreId,
        );

      // Trả về thông tin vector store đã cập nhật
      return plainToInstance(
        VectorStoreResponseDto,
        {
          storeId: vectorStore.id,
          storeName: vectorStore.name,
          size: vectorStore.storage,
          agents: agentCount,
          files: fileCount,
          createdAt: vectorStore.createdAt,
          updatedAt: vectorStore.updateAt,
        },
        { excludeExtraneousValues: true },
      );
    } catch (error) {
      this.logger.error(
        `Error updating vector store: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_UPDATE_ERROR,
        `Lỗi khi cập nhật vector store: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết của một vector store
   * @param vectorStoreId ID của vector store
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết của vector store
   */
  async getVectorStoreDetail(
    vectorStoreId: string,
    userId: number,
  ): Promise<VectorStoreResponseDto> {
    try {
      // Kiểm tra vector store có tồn tại và thuộc về người dùng không
      const vectorStore = await this.vectorStoreRepository.findOneByIdAndUserId(
        vectorStoreId,
        userId,
      );

      // Xác thực vector store tồn tại
      this.validationHelper.validateVectorStoreExists(
        vectorStore,
        vectorStoreId,
      );

      // Đếm số lượng file và agent trong vector store
      const fileCount =
        await this.vectorStoreRepository.countFilesByVectorStoreId(
          vectorStoreId,
        );
      const agentCount =
        await this.vectorStoreRepository.countAgentsByVectorStoreId(
          vectorStoreId,
        );

      // Trả về thông tin vector store
      return plainToInstance(
        VectorStoreResponseDto,
        {
          storeId: vectorStore.id,
          storeName: vectorStore.name,
          size: vectorStore.storage,
          agents: agentCount,
          files: fileCount,
          createdAt: vectorStore.createdAt,
          updatedAt: vectorStore.updateAt,
        },
        { excludeExtraneousValues: true },
      );
    } catch (error) {
      this.logger.error(
        `Error getting vector store detail: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DETAIL_ERROR,
        `Lỗi khi lấy thông tin chi tiết vector store: ${error.message}`,
      );
    }
  }

  /**
   * Xóa một vector store
   * @param vectorStoreId ID của vector store
   * @param userId ID của người dùng
   */
  @Transactional()
  async deleteVectorStore(
    vectorStoreId: string,
    userId: number,
  ): Promise<void> {
    try {
      // Kiểm tra vector store có tồn tại và thuộc về người dùng không
      const vectorStore = await this.vectorStoreRepository.findOneByIdAndUserId(
        vectorStoreId,
        userId,
      );

      // Xác thực vector store tồn tại
      this.validationHelper.validateVectorStoreExists(
        vectorStore,
        vectorStoreId,
      );

      // Không cần xóa vector store trên OpenAI nữa
      this.logger.log(`Bỏ qua bước xóa vector store trên OpenAI, chỉ xóa trong database`);

      // Xóa các bản ghi liên kết với file
      await this.vectorStoreFileRepository.delete({ vectorStoreId });

      // Xóa vector store
      await this.vectorStoreRepository.delete({ id: vectorStoreId });
    } catch (error) {
      this.logger.error(
        `Error deleting vector store: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DELETE_ERROR,
        `Lỗi khi xóa vector store: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều vector store
   * @param dto Thông tin các vector store cần xóa
   * @param userId ID của người dùng
   * @returns Thông tin về việc xóa vector store thành công
   */
  @Transactional()
  async deleteVectorStores(
    dto: DeleteVectorStoresDto,
    userId: number,
  ): Promise<{
    success: boolean;
    deletedCount: number;
    failedItems?: { id: string; reason: string }[];
  }> {
    try {
      this.logger.log(`Bắt đầu xóa ${dto.storeIds.length} vector stores cho user ID ${userId}`);

      const { storeIds } = dto;
      const failedItems: { id: string; reason: string }[] = [];

      // Lấy danh sách vector stores thuộc về user
      const vectorStores = await this.vectorStoreRepository.find({
        where: {
          id: In(storeIds),
          ownerId: userId,
          ownerType: OwnerType.USER,
        },
      });

      // Kiểm tra các vector store không tồn tại hoặc không thuộc về user
      const foundStoreIds = vectorStores.map(vs => vs.id);
      const notFoundStoreIds = storeIds.filter(id => !foundStoreIds.includes(id));

      // Thêm các vector store không tìm thấy vào danh sách failed
      notFoundStoreIds.forEach(id => {
        failedItems.push({
          id,
          reason: 'Vector store không tồn tại hoặc bạn không có quyền truy cập',
        });
      });

      // Lấy API key của user
      const userApiKey = await this.getUserRagApiKey(userId);

      // Xóa trên RAG API trước với API key của user
      let ragDeletedCount = 0;
      if (foundStoreIds.length > 0) {
        try {
          const ragResult = await this.ragFileProcessingService.deleteBulkVectorStoresWithApiKey(foundStoreIds, userApiKey);
          ragDeletedCount = ragResult.created_count; // RAG API trả về created_count cho số lượng đã xóa
          this.logger.log(`Đã xóa ${ragDeletedCount} vector stores trên RAG API`);
        } catch (error) {
          this.logger.error(`Lỗi khi xóa vector stores trên RAG API: ${error.message}`);
          // Tiếp tục xóa trong database ngay cả khi RAG API lỗi
        }
      }

      // Xóa trong database
      let deletedCount = 0;
      for (const vectorStore of vectorStores) {
        try {
          // Xóa các bản ghi liên kết với file
          await this.vectorStoreFileRepository.delete({ vectorStoreId: vectorStore.id });

          // Xóa vector store
          await this.vectorStoreRepository.delete({ id: vectorStore.id });

          deletedCount++;
          this.logger.log(`Đã xóa vector store ${vectorStore.id} thành công trong database`);
        } catch (error) {
          this.logger.error(`Lỗi khi xóa vector store ${vectorStore.id} trong database: ${error.message}`);
          failedItems.push({
            id: vectorStore.id,
            reason: `Lỗi khi xóa trong database: ${error.message}`,
          });
        }
      }

      this.logger.log(`Hoàn thành xóa vector stores. Đã xóa: ${deletedCount}, Thất bại: ${failedItems.length}`);

      return {
        success: true,
        deletedCount,
        failedItems: failedItems.length > 0 ? failedItems : undefined,
      };
    } catch (error) {
      this.logger.error(
        `Error deleting vector stores: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DELETE_ERROR,
        `Lỗi khi xóa vector stores: ${error.message}`,
      );
    }
  }

  /**
   * Tạo nhiều vector stores (fire-and-forget)
   * Chỉ gọi RAG API, không nhận response, không lưu DB
   * @param dto Thông tin các vector stores cần tạo
   * @param userId ID của người dùng
   * @returns Thông báo thành công
   */
  async createBulkVectorStoresFireAndForget(
    dto: CreateBulkVectorStoresDto,
    userId: number,
  ): Promise<{ success: boolean; message: string }> {
    try {
      this.logger.log(`Bắt đầu tạo ${dto.vectorStores.length} vector stores cho user ID ${userId} (fire-and-forget)`);

      // Lấy API key của user
      const userApiKey = await this.getUserRagApiKey(userId);

      // Gọi RAG API (fire-and-forget) với API key của user
      await this.ragFileProcessingService.createBulkVectorStoresFireAndForgetWithApiKey(
        dto.vectorStores.map(vs => ({ name: vs.name })),
        userApiKey
      );

      this.logger.log(`Đã gửi request tạo ${dto.vectorStores.length} vector stores đến RAG API (fire-and-forget)`);

      return {
        success: true,
        message: `Đã gửi request tạo ${dto.vectorStores.length} vector stores đến RAG API thành công.`
      };
    } catch (error) {
      this.logger.error(
        `Error creating vector stores (fire-and-forget): ${error.message}`,
        error.stack,
      );
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_CREATE_ERROR,
        `Lỗi khi gửi request tạo vector stores: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách vector stores từ RAG API với validation user
   * @param queryDto Tham số truy vấn
   * @param userId ID của người dùng
   * @returns Danh sách vector stores từ RAG API (chỉ của user hiện tại)
   */
  async getVectorStoresFromRag(
    queryDto: QueryVectorStoreDto,
    userId: number,
  ): Promise<{
    total: number;
    vector_stores: Array<{
      created_at: number;
      id: string;
      name: string;
      storage: number;
      update_at: number;
    }>;
  }> {
    try {
      this.logger.log(`Lấy danh sách vector stores từ RAG API cho user ${userId}`);

      // Validate user có quyền truy cập không bằng API key info
      try {
        const apiKeyInfo = await this.getApiKeyInfoForUser(userId);

        if (apiKeyInfo.user_id !== userId) {
          this.logger.warn(`User ${userId} attempted to access vector stores but API key belongs to user ${apiKeyInfo.user_id}`);
          throw new AppException(
            KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
            'Không có quyền truy cập vector stores này'
          );
        }

        this.logger.log(`User ${userId} validated successfully with API key (role: ${apiKeyInfo.role})`);

        // Lấy dữ liệu từ RAG API (đã validate user)
        const ragParams = {
          skip: ((queryDto.page || 1) - 1) * (queryDto.limit || 10),
          limit: queryDto.limit || 10,
          sort_by: this.mapSortByToRagApi(queryDto.sortBy || 'createdAt'),
          sort_order: queryDto.sortDirection?.toLowerCase() || 'desc',
          search: queryDto.search,
        };

        // Lấy API key của user để gọi RAG API
        const userApiKey = await this.getUserRagApiKey(userId);

        // Gọi RAG API với API key của user
        const ragResult = await this.ragFileProcessingService.getVectorStoresWithApiKey(ragParams, userApiKey);
        this.logger.log(`RAG API trả về ${ragResult.total} vector stores`);

        // Filter chỉ những vector stores thuộc về API key của user hiện tại
        const filteredVectorStores = ragResult.vector_stores.filter(store => {
          // Check nếu store có api_key field
          if ('api_key' in store) {
            const storeApiKey = (store as any).api_key;
            const isOwner = storeApiKey === userApiKey;
            this.logger.log(`Vector store ${store.id}: api_key=${storeApiKey.substring(0, 10)}..., isOwner=${isOwner}`);
            return isOwner;
          }
          // Nếu không có api_key field, bỏ qua (không thuộc về ai)
          return false;
        });

        // Remove api_key field khỏi response
        const cleanedVectorStores = filteredVectorStores.map(store => {
          const { api_key, ...cleanStore } = store as any;
          return cleanStore;
        });

        this.logger.log(`Filtered ${cleanedVectorStores.length}/${ragResult.total} vector stores for user ${userId}`);

        return {
          total: cleanedVectorStores.length,
          vector_stores: cleanedVectorStores
        };

      } catch (validationError) {
        // Fallback: Use database filtering if API key validation fails
        this.logger.warn(`API key validation failed for user ${userId}, falling back to database filtering: ${validationError.message}`);

        const ragParams = {
          skip: ((queryDto.page || 1) - 1) * (queryDto.limit || 10),
          limit: queryDto.limit || 10,
          sort_by: this.mapSortByToRagApi(queryDto.sortBy || 'createdAt'),
          sort_order: queryDto.sortDirection?.toLowerCase() || 'desc',
          search: queryDto.search,
        };

        // Get all vector stores from RAG API
        const allVectorStores = await this.ragFileProcessingService.getVectorStores(ragParams);

        // Get user's vector stores from database
        const userVectorStores = await this.vectorStoreRepository.find({
          where: { ownerId: userId, ownerType: OwnerType.USER }
        });

        this.logger.log(`Database has ${userVectorStores.length} vector stores for user ${userId}`);
        this.logger.log(`RAG API returned ${allVectorStores.vector_stores.length} vector stores`);

        // Log database vector store IDs
        const userVectorStoreIds = userVectorStores.map(vs => vs.id);
        this.logger.log(`Database vector store IDs: ${JSON.stringify(userVectorStoreIds)}`);

        // Log RAG API vector store IDs
        const ragVectorStoreIds = allVectorStores.vector_stores.map(vs => vs.id);
        this.logger.log(`RAG API vector store IDs: ${JSON.stringify(ragVectorStoreIds)}`);

        // Filter results based on database
        const filteredStores = allVectorStores.vector_stores.filter(
          store => userVectorStoreIds.includes(store.id)
        );

        this.logger.log(`Filtered ${filteredStores.length} vector stores for user ${userId} using database fallback`);

        return {
          total: filteredStores.length,
          vector_stores: filteredStores
        };
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error getting vector stores from RAG API: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_LIST_ERROR,
        `Lỗi khi lấy danh sách vector stores từ RAG API: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết vector store từ RAG API với validation user
   * @param vectorStoreId ID của vector store
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết vector store từ RAG API (chỉ nếu thuộc về user)
   */
  async getVectorStoreDetailFromRag(
    vectorStoreId: string,
    userId: number,
  ): Promise<{
    created_at: number;
    id: string;
    name: string;
    storage: number;
    update_at: number;
  }> {
    try {
      this.logger.log(`Lấy thông tin chi tiết vector store ${vectorStoreId} từ RAG API cho user ${userId}`);

      // Lấy API key của user
      const userApiKey = await this.getUserRagApiKey(userId);

      // Lấy thông tin từ RAG API với API key của user
      // RAG API sẽ tự validate ownership thông qua API key
      const ragResult = await this.ragFileProcessingService.getVectorStoreDetailWithApiKey(vectorStoreId, userApiKey);

      this.logger.log(`Successfully retrieved vector store ${vectorStoreId} from RAG API for user ${userId}`);

      // Remove api_key field nếu có
      const { api_key, ...cleanResult } = ragResult as any;
      return cleanResult;

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error getting vector store detail from RAG API: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DETAIL_ERROR,
        `Lỗi khi lấy thông tin chi tiết vector store từ RAG API: ${error.message}`,
      );
    }
  }

  /**
   * Map sortBy field từ internal format sang RAG API format
   */
  private mapSortByToRagApi(sortBy: string): string {
    const mapping: Record<string, string> = {
      'createdAt': 'created_at',
      'name': 'name',
      'storage': 'storage',
    };
    return mapping[sortBy] || 'created_at';
  }

  /**
   * Xóa nhiều vector store (fire-and-forget)
   * Chỉ gọi RAG API, không nhận response, không lưu DB
   * @param dto Thông tin các vector store cần xóa
   * @param userId ID của người dùng
   * @returns Thông báo thành công
   */
  async deleteVectorStoresFireAndForget(
    dto: DeleteVectorStoresDto,
    userId: number,
  ): Promise<{ success: boolean; message: string }> {
    try {
      this.logger.log(`Bắt đầu xóa ${dto.storeIds.length} vector stores cho user ID ${userId} (fire-and-forget)`);

      const { storeIds } = dto;

      // Lấy danh sách vector stores thuộc về user để validate
      const vectorStores = await this.vectorStoreRepository.find({
        where: {
          id: In(storeIds),
          ownerId: userId,
          ownerType: OwnerType.USER,
        },
      });

      const foundStoreIds = vectorStores.map(vs => vs.id);
      const notFoundStoreIds = storeIds.filter(id => !foundStoreIds.includes(id));

      if (notFoundStoreIds.length > 0) {
        this.logger.warn(`Một số vector stores không tồn tại hoặc không thuộc về user: ${notFoundStoreIds.join(', ')}`);
      }

      // Lấy API key của user
      const userApiKey = await this.getUserRagApiKey(userId);

      // Chỉ gọi RAG API với các vector stores hợp lệ và API key của user
      if (foundStoreIds.length > 0) {
        await this.ragFileProcessingService.deleteBulkVectorStoresFireAndForgetWithApiKey(foundStoreIds, userApiKey);
        this.logger.log(`Đã gửi request xóa ${foundStoreIds.length} vector stores đến RAG API (fire-and-forget)`);
      }

      return {
        success: true,
        message: `Đã gửi request xóa ${foundStoreIds.length} vector stores đến RAG API thành công.`
      };
    } catch (error) {
      this.logger.error(
        `Error deleting vector stores (fire-and-forget): ${error.message}`,
        error.stack,
      );
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DELETE_ERROR,
        `Lỗi khi gửi request xóa vector stores: ${error.message}`,
      );
    }
  }

}
