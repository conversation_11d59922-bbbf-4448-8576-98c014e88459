import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsNumber, IsObject, IsOptional, IsString } from 'class-validator';
import { EntityStatusEnum, PriceTypeEnum, ProductTypeEnum } from '@modules/business/enums';

/**
 * DTO cho response trả về thông tin sản phẩm khách hàng
 */
export class CustomerProductResponseDto {
  @ApiProperty({
    description: 'ID của sản phẩm',
    example: 123,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Sản phẩm A',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Giá sản phẩm (JSONB)',
    example: {
      listPrice: 500000,
      salePrice: 450000,
      currency: 'VND'
    },
    nullable: true,
  })
  @IsOptional()
  @IsObject()
  price: any | null;

  @ApiProperty({
    description: 'Loại giá',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    nullable: true,
  })
  @IsOptional()
  @IsEnum(PriceTypeEnum)
  typePrice: PriceTypeEnum | null;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
    examples: [
      ProductTypeEnum.PHYSICAL,
      ProductTypeEnum.DIGITAL,
      ProductTypeEnum.EVENT,
      ProductTypeEnum.SERVICE,
      ProductTypeEnum.COMBO
    ]
  })
  @IsEnum(ProductTypeEnum)
  productType: ProductTypeEnum;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Mô tả chi tiết về sản phẩm A',
    nullable: true,
  })
  @IsOptional()
  @IsString()
  description: string | null;

  @ApiProperty({
    description: 'Tags sản phẩm',
    nullable: true,
    type: [String],
    example: ['thời trang', 'nam', 'cotton']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags: string[] | null;

  @ApiProperty({
    description: 'ID người tạo sản phẩm',
    example: 456,
    nullable: true,
  })
  @IsOptional()
  @IsNumber()
  userId: number | null;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  @IsOptional()
  @IsNumber()
  createdAt: number | null;

  @ApiProperty({
    description: 'Thời gian cập nhật (timestamp)',
    example: 1625184000000,
    nullable: true,
  })
  @IsOptional()
  @IsNumber()
  updatedAt: number | null;

  @ApiProperty({
    description: 'Trạng thái của sản phẩm',
    enum: EntityStatusEnum,
    example: EntityStatusEnum.PENDING,
    nullable: false,
  })
  @IsEnum(EntityStatusEnum)
  status: EntityStatusEnum;

  @ApiProperty({
    description: 'Các trường tùy chỉnh (JSONB)',
    example: {
      color: 'Đỏ',
      size: 'L',
      material: 'Cotton'
    },
    nullable: true,
  })
  @IsOptional()
  @IsObject()
  customFields: any | null;
}
