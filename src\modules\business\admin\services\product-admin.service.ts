import { Injectable, Logger } from '@nestjs/common';
import { CustomFieldStatus } from '@modules/business/entities/custom-field.entity';
import { In } from 'typeorm';
import { CustomerProductAdminRepository } from '@modules/business/repositories';
import { QueryCustomerProductDto } from '@modules/business/admin/dto';
import { CustomerProductResponseDto } from '@modules/business/admin/dto';
import { CustomerProductDetailResponseDto } from '@modules/business/admin/dto';
import { ClassificationResponseDto, CustomFieldValueWithDetailsResponseDto } from '@modules/business/admin/dto/customfields/classification-price.dto';
import { CustomGroupFormResponseDto, CustomFieldWithValueResponseDto } from '@modules/business/admin/dto/customfields/custom-field-response.dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { ValidationHelper } from '../helpers/validation.helper';
import {
  CustomFieldRepository,
  UserClassificationRepository,
  PhysicalProductRepository,
  PhysicalProductVariantRepository,
  DigitalProductRepository,
  DigitalProductVersionRepository,
  ServiceProductRepository,
  ServicePackageOptionRepository,
  EventProductRepository,
  EventProductTicketRepository,
  ComboProductRepository,
  EntityHasMediaRepository,
} from '../../repositories';
import { MediaRepository } from '@modules/data/media/repositories/media.repository';
import { AppException } from '@common/exceptions';
import { BUSINESS_ADMIN_ERROR_CODES } from '@modules/business/admin/exceptions';
import { CustomField, CustomerProduct, UserClassification } from '@modules/business/entities';
import { EntityStatusEnum } from '@modules/business/enums/entity-status.enum';
import { UpdateProductStatusDto } from '../dto/customfields/update-product-status.dto';
import { Transactional } from 'typeorm-transactional';

/**
 * Service xử lý nghiệp vụ liên quan đến sản phẩm khách hàng cho admin
 */
@Injectable()
export class ProductAdminService {
  private readonly logger = new Logger(ProductAdminService.name);

  constructor(
    private readonly customerProductAdminRepository: CustomerProductAdminRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly userClassificationRepository: UserClassificationRepository,
    private readonly validationHelper: ValidationHelper,
    // Repositories for complete product info
    private readonly physicalProductRepository: PhysicalProductRepository,
    private readonly physicalProductVariantRepository: PhysicalProductVariantRepository,
    private readonly digitalProductRepository: DigitalProductRepository,
    private readonly digitalProductVersionRepository: DigitalProductVersionRepository,
    private readonly serviceProductRepository: ServiceProductRepository,
    private readonly servicePackageOptionRepository: ServicePackageOptionRepository,
    private readonly eventProductRepository: EventProductRepository,
    private readonly eventProductTicketRepository: EventProductTicketRepository,
    private readonly comboProductRepository: ComboProductRepository,
    private readonly entityHasMediaRepository: EntityHasMediaRepository,
    private readonly mediaRepository: MediaRepository,
  ) {}

  /**
   * Lấy danh sách sản phẩm với phân trang và lọc
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách sản phẩm phân trang
   * @note Phương thức này chỉ đọc dữ liệu, không cần @Transactional
   */
  async getProducts(
    employeeId: number,
    queryDto: QueryCustomerProductDto,
  ): Promise<PaginatedResult<CustomerProductResponseDto>> {
    this.logger.log(
      `Nhân viên ${employeeId} đang lấy danh sách sản phẩm với query: ${JSON.stringify(queryDto)}`,
    );

    try {
      // Lấy danh sách sản phẩm từ repository
      this.logger.log(`Đang truy vấn danh sách sản phẩm từ database`);
      const productsResult =
        await this.customerProductAdminRepository.findProducts(queryDto);
      this.logger.log(`Đã tìm thấy ${productsResult.items.length} sản phẩm`);

      // Chuyển đổi từ entity sang DTO
      this.logger.log(`Đang chuyển đổi dữ liệu sang DTO`);
      const items = productsResult.items.map((product) =>
        this.mapToCustomerProductResponseDto(product),
      );

      return {
        items,
        meta: productsResult.meta,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách sản phẩm: ${error.message}`,
        error.stack,
        `method: ${this.getProducts.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.PRODUCT_FETCH_ERROR,
        'Lỗi khi lấy danh sách sản phẩm',
      );
    }
  }

  /**
   * Lấy chi tiết sản phẩm theo ID với thông tin hoàn chỉnh
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param productId ID của sản phẩm
   * @returns Chi tiết sản phẩm hoàn chỉnh từ tất cả bảng liên quan
   * @note Phương thức này chỉ đọc dữ liệu, không cần @Transactional
   */
  async getProductById(
    employeeId: number,
    productId: number,
  ): Promise<CustomerProductDetailResponseDto> {
    this.logger.log(
      `Nhân viên ${employeeId} đang lấy chi tiết hoàn chỉnh sản phẩm với ID: ${productId}`,
    );

    // Lấy thông tin sản phẩm cơ bản
    this.logger.log(`Đang truy vấn thông tin sản phẩm với ID: ${productId}`);
    const product =
      await this.customerProductAdminRepository.findProductById(productId);
    this.validationHelper.validateProductExists(product);

    // Đảm bảo product không phải là null sau khi validate
    // Vì validateProductExists sẽ throw exception nếu product là null
    const validProduct = product as CustomerProduct;
    this.logger.log(`Đã tìm thấy sản phẩm: ${validProduct.name}`);

    try {
      // Lấy thông tin hoàn chỉnh dựa trên productType
      this.logger.log(`Đang lấy thông tin hoàn chỉnh cho sản phẩm loại: ${validProduct.productType}`);
      const completeProductInfo = await this.getCompleteProductInfo(validProduct);

      // Chuyển đổi từ entity sang DTO
      this.logger.log(`Đang chuyển đổi dữ liệu sản phẩm sang DTO`);
      const productDto = this.mapToCustomerProductResponseDto(validProduct);

      const detailDto: CustomerProductDetailResponseDto = {
        ...productDto,
        ...completeProductInfo, // Thêm thông tin hoàn chỉnh từ các bảng chuyên biệt
      };

      this.logger.log(`Hoàn tất lấy chi tiết hoàn chỉnh sản phẩm ID: ${productId}`);
      return detailDto;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy chi tiết sản phẩm: ${error.message}`,
        error.stack,
        `method: ${this.getProductById.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.PRODUCT_DETAIL_FETCH_ERROR,
        'Lỗi khi lấy chi tiết sản phẩm',
      );
    }
  }

  /**
   * Cập nhật trạng thái sản phẩm
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param updateStatusDto DTO chứa thông tin cập nhật trạng thái
   * @returns Số lượng sản phẩm đã cập nhật
   */
  @Transactional() async updateProductStatus(
    employeeId: number,
    updateStatusDto: UpdateProductStatusDto,
  ): Promise<number> {
    const { productIds, status, rejectReason } = updateStatusDto;
    this.logger.log(
      `Nhân viên ${employeeId} đang cập nhật trạng thái ${status} cho ${productIds.length} sản phẩm`,
    );

    try {
      // Kiểm tra lý do từ chối nếu trạng thái mới là REJECTED
      this.validationHelper.validateRejectReason(status, rejectReason);

      // Kiểm tra từng sản phẩm
      for (const productId of productIds) {
        // Kiểm tra sản phẩm tồn tại
        const product =
          await this.customerProductAdminRepository.findProductById(productId);
        this.validationHelper.validateProductExists(product);

        // Nếu đang phê duyệt sản phẩm (chuyển sang APPROVED), kiểm tra cấu trúc giá
        if (status === EntityStatusEnum.APPROVED && product) {
          this.logger.log(`Đang kiểm tra cấu trúc giá của sản phẩm ${productId} trước khi phê duyệt`);
          this.logger.log(`Thông tin sản phẩm - ID: ${product.id}, typePrice: ${product.typePrice}, price: ${JSON.stringify(product.price)}`);
          this.validationHelper.validateProductPrice(product);
        }
      }

      // Cập nhật trạng thái
      const updatedCount =
        await this.customerProductAdminRepository.updateProductsStatus(
          productIds,
          status,
          rejectReason,
        );
      this.logger.log(`Đã cập nhật trạng thái cho ${updatedCount} sản phẩm`);

      // Lưu lý do từ chối nếu có
      if (status === EntityStatusEnum.REJECTED && rejectReason) {
        // TODO: Lưu lý do từ chối vào bảng ghi chú hoặc lịch sử nếu cần
        this.logger.log(`Đã lưu lý do từ chối: ${rejectReason}`);
      }

      return updatedCount;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật trạng thái sản phẩm: ${error.message}`,
        error.stack,
        `method: ${this.updateProductStatus.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.PRODUCT_STATUS_UPDATE_ERROR,
        'Lỗi khi cập nhật trạng thái sản phẩm',
      );
    }
  }

  /**
   * Xây dựng DTO cho các nhóm trường tùy chỉnh
   * @param customGroupForms Danh sách nhóm trường tùy chỉnh
   * @param customGroupFormFields Danh sách tất cả các trường trong các nhóm
   * @returns Danh sách DTO của các nhóm trường tùy chỉnh
   */
  // buildCustomGroupFormDto đã bị xóa - CustomGroupForm đã bị xóa hoàn toàn
  /*
  private async buildCustomGroupFormDto(
    customGroupForms: CustomGroupForm[],
    customGroupFormFields: CustomGroupFormField[]
  ): Promise<CustomGroupFormResponseDto[]> {
    this.logger.log(`Bắt đầu xây dựng DTO cho nhóm trường tùy chỉnh`, `method: ${this.buildCustomGroupFormDto.name}`);
    if (!customGroupForms || customGroupForms.length === 0) {
      this.logger.log(`Không tìm thấy nhóm trường tùy chỉnh nào cho sản phẩm này`);
      return [];
    }

    this.logger.log(`Đã tìm thấy ${customGroupForms.length} nhóm trường tùy chỉnh`);

    // Nhóm các trường theo formGroupId
    const fieldsByFormGroupId = customGroupFormFields.reduce((acc, field) => {
      if (!acc[field.formGroupId]) {
        acc[field.formGroupId] = [];
      }
      acc[field.formGroupId].push(field);
      return acc;
    }, {} as Record<number, CustomGroupFormField[]>);

    // Lấy tất cả fieldIds để truy vấn chi tiết trường một lần duy nhất
    const allFieldIds = customGroupFormFields.map(field => field.fieldId);
    const uniqueFieldIds = [...new Set(allFieldIds)];

    // Lấy chi tiết tất cả các trường tùy chỉnh trong một lần truy vấn
    this.logger.log(`Đang truy vấn chi tiết ${uniqueFieldIds.length} trường tùy chỉnh`);
    const customFields = uniqueFieldIds.length > 0
      ? await this.customFieldRepository.findByIds(uniqueFieldIds)
      : [];
    this.logger.log(`Đã tìm thấy ${customFields.length} trường tùy chỉnh`);

    // Tạo map để tra cứu nhanh thông tin trường theo ID
    const customFieldsMap = customFields.reduce((map, field) => {
      map[field.id] = field;
      return map;
    }, {} as Record<number, CustomField>);

    // Xử lý từng nhóm trường tùy chỉnh
    const result: CustomGroupFormResponseDto[] = [];

    for (const customGroupForm of customGroupForms) {
      this.logger.log(`Đang xử lý nhóm trường tùy chỉnh: ${customGroupForm.label}`);

      // Lấy danh sách trường trong nhóm từ map đã tạo
      const groupFields = fieldsByFormGroupId[customGroupForm.id] || [];
      this.logger.log(`Đã tìm thấy ${groupFields.length} trường trong nhóm`);

      // Tạo map giá trị trường
      const fieldValuesMap = groupFields.reduce((map, field) => {
        map[field.fieldId] = field.value;
        return map;
      }, {} as Record<number, any>);

      // Lấy chi tiết từng trường
      const fieldIds = groupFields.map((field) => field.fieldId);
      if (fieldIds.length === 0) {
        result.push({
          id: customGroupForm.id,
          label: customGroupForm.label,
          fields: []
        });
        continue;
      }

      result.push({
        id: customGroupForm.id,
        label: customGroupForm.label,
        fields: fieldIds.map(fieldId => {
          const field = customFieldsMap[fieldId];
          if (!field) return null;

          return {
            id: field.id,
            component: field.component,
            configId: field.configId,
            label: field.label,
            type: field.type,
            required: field.required,
            configJson: field.configJson,
            employeeId: field.employeeId,
            userId: field.userId,
            createAt: field.createAt,
            status: field.status as unknown as CustomFieldStatus,
            value: fieldValuesMap[field.id] || null
          };
        }).filter(field => field !== null) as CustomFieldWithValueResponseDto[]
      });
    }

    return result;
  }
  */

  /**
   * Lấy thông tin hoàn chỉnh từ các bảng chuyên biệt dựa trên productType
   * @param product Sản phẩm khách hàng
   * @returns Thông tin hoàn chỉnh từ các bảng liên quan
   */
  private async getCompleteProductInfo(product: CustomerProduct): Promise<any> {
    const productType = product.productType;
    const productId = product.id;

    this.logger.log(`Lấy thông tin hoàn chỉnh cho sản phẩm loại ${productType}, ID: ${productId}`);

    // Lấy hình ảnh product-level cho tất cả loại sản phẩm
    const productImages = await this.loadProductImages(productId);

    switch (productType) {
      case 'PHYSICAL':
        return await this.getPhysicalProductInfo(productId, productImages);

      case 'DIGITAL':
        return await this.getDigitalProductInfo(productId, productImages);

      case 'SERVICE':
        return await this.getServiceProductInfo(productId, productImages);

      case 'EVENT':
        return await this.getEventProductInfo(productId, productImages);

      case 'COMBO':
        return await this.getComboProductInfo(productId, productImages);

      default:
        this.logger.warn(`Loại sản phẩm không được hỗ trợ: ${productType}`);
        return {};
    }
  }





  /**
   * Lấy hình ảnh sản phẩm từ entity_has_media và media_data
   * @param productId ID sản phẩm
   * @returns Danh sách hình ảnh
   */
  private async loadProductImages(productId: number): Promise<any[]> {
    try {
      // Lấy media links từ entity_has_media (product level)
      const mediaLinks = await this.entityHasMediaRepository.findByProductId(productId);

      // Filter chỉ lấy ảnh product level
      const productLevelLinks = mediaLinks.filter(link =>
        link.productId &&
        !link.physicalVarial &&
        !link.ticketVarial &&
        !link.versionId &&
        !link.productComboId &&
        !link.productPlanVarialId &&
        link.mediaId
      );

      if (productLevelLinks.length === 0) {
        return [];
      }

      // Lấy media records từ media_data
      const mediaIds = productLevelLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      // Build response với CDN URLs
      return productLevelLinks.map((link, index) => {
        const media = mediaRecords.find(m => m.id === link.mediaId);
        if (!media) return null;

        const viewUrl = media.storageKey ? `https://cdn.redai.vn/${media.storageKey}` : '';

        return {
          id: `img-${String(index + 1).padStart(3, '0')}`,
          key: media.storageKey || '',
          url: viewUrl,
          mediaId: media.id,
          entityMediaId: link.id,
          name: media.name,
          size: media.size?.toString() || '0',
        };
      }).filter(Boolean);
    } catch (error) {
      this.logger.error(`Lỗi khi load product images: ${error.message}`);
      return [];
    }
  }

  /**
   * Lấy thông tin sản phẩm vật lý
   * @param productId ID sản phẩm
   * @param productImages Danh sách hình ảnh product-level
   * @returns Thông tin sản phẩm vật lý
   */
  private async getPhysicalProductInfo(productId: number, productImages: any[]): Promise<any> {
    try {
      // Lấy thông tin từ physical_products
      const physicalProduct = await this.physicalProductRepository.findById(productId);

      // Lấy thông tin variants từ physical_product_variants
      const variants = await this.physicalProductVariantRepository.findByPhysicalProductId(productId);

      // Lấy ảnh cho từng variant
      const variantsWithImages = await Promise.all(
        (variants || []).map(async (variant) => {
          const variantImages = await this.loadVariantImages(productId, variant.id);
          return {
            ...variant,
            images: variantImages,
          };
        })
      );

      return {
        physicalInfo: {
          stockQuantity: physicalProduct?.stockQuantity || undefined,
          sku: physicalProduct?.sku || undefined,
          barcode: physicalProduct?.barcode || undefined,
          shipmentConfig: physicalProduct?.shipmentConfig || undefined,
          images: productImages, // Ảnh product-level
        },
        variants: variantsWithImages,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin physical product: ${error.message}`);
      return {
        physicalInfo: { images: productImages },
        variants: []
      };
    }
  }

  /**
   * Lấy thông tin sản phẩm số
   * @param productId ID sản phẩm
   * @param productImages Danh sách hình ảnh product-level
   * @returns Thông tin sản phẩm số
   */
  private async getDigitalProductInfo(productId: number, productImages: any[]): Promise<any> {
    try {
      // Lấy thông tin từ digital_products
      const digitalProduct = await this.digitalProductRepository.findById(productId);

      // Lấy thông tin versions từ digital_product_versions
      const versions = await this.digitalProductVersionRepository.findByDigitalProductId(productId);

      // Lấy ảnh cho từng version
      const versionsWithImages = await Promise.all(
        (versions || []).map(async (version) => {
          const versionImages = await this.loadVersionImages(productId, version.id);
          return {
            ...version,
            images: versionImages,
          };
        })
      );

      return {
        digitalInfo: {
          deliveryMethod: digitalProduct?.deliveryMethod || undefined,
          deliveryTime: digitalProduct?.deliveryTime || undefined,
          waitingTime: digitalProduct?.waitingTime || undefined,
          images: productImages, // Ảnh product-level
        },
        versions: versionsWithImages,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin digital product: ${error.message}`);
      return {
        digitalInfo: { images: productImages },
        versions: []
      };
    }
  }

  /**
   * Lấy thông tin sản phẩm dịch vụ
   * @param productId ID sản phẩm
   * @param productImages Danh sách hình ảnh product-level
   * @returns Thông tin sản phẩm dịch vụ
   */
  private async getServiceProductInfo(productId: number, productImages: any[]): Promise<any> {
    try {
      // Lấy thông tin từ service_products
      const serviceProduct = await this.serviceProductRepository.findById(productId);

      // Lấy thông tin packages từ service_packages_option
      const packages = await this.servicePackageOptionRepository.findByServiceProductId(productId);

      // Lấy ảnh cho từng package
      const packagesWithImages = await Promise.all(
        (packages || []).map(async (pkg) => {
          const packageImages = await this.loadPackageImages(productId, pkg.id);
          return {
            ...pkg,
            images: packageImages,
          };
        })
      );

      return {
        serviceInfo: {
          serviceType: serviceProduct?.serviceType || undefined,
          location: serviceProduct?.location || undefined,
          providerName: serviceProduct?.providerName || undefined,
          createdAt: serviceProduct?.createdAt || undefined,
          updatedAt: serviceProduct?.updatedAt || undefined,
          images: productImages, // Ảnh product-level
        },
        packages: packagesWithImages,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin service product: ${error.message}`);
      return {
        serviceInfo: { images: productImages },
        packages: []
      };
    }
  }

  /**
   * Lấy thông tin sản phẩm sự kiện
   * @param productId ID sản phẩm
   * @param productImages Danh sách hình ảnh product-level
   * @returns Thông tin sản phẩm sự kiện
   */
  private async getEventProductInfo(productId: number, productImages: any[]): Promise<any> {
    try {
      // Lấy thông tin từ event_products
      const eventProduct = await this.eventProductRepository.findById(productId);

      // Lấy thông tin tickets từ event_product_tickets
      const tickets = await this.eventProductTicketRepository.findByEventProductId(productId);

      // Lấy ảnh cho từng ticket
      const ticketsWithImages = await Promise.all(
        (tickets || []).map(async (ticket) => {
          const ticketImages = await this.loadTicketImages(productId, ticket.id);
          return {
            ...ticket,
            images: ticketImages,
          };
        })
      );

      return {
        eventInfo: {
          participationType: eventProduct?.participationType || undefined,
          location: eventProduct?.location || undefined,
          participationUrl: eventProduct?.participationUrl || undefined,
          startDate: eventProduct?.startDate || undefined,
          endDate: eventProduct?.endDate || undefined,
          timeZone: eventProduct?.timeZone || undefined,
          images: productImages, // Ảnh product-level
        },
        tickets: ticketsWithImages,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin event product: ${error.message}`);
      return {
        eventInfo: { images: productImages },
        tickets: []
      };
    }
  }

  /**
   * Lấy thông tin sản phẩm combo
   * @param productId ID sản phẩm
   * @param productImages Danh sách hình ảnh product-level
   * @returns Thông tin sản phẩm combo
   */
  private async getComboProductInfo(productId: number, productImages: any[]): Promise<any> {
    try {
      // Lấy thông tin từ combo_products
      const comboProduct = await this.comboProductRepository.findById(productId);

      // Lấy combo images từ entity_has_media (product_combo_id)
      const comboImages = await this.loadComboImages(productId);

      return {
        comboInfo: {
          price: comboProduct?.price || undefined,
          comboItems: comboProduct?.comboItems || undefined,
          maxQuantity: comboProduct?.maxQuantity || undefined,
          images: productImages, // Ảnh product-level
          comboImages: comboImages, // Ảnh combo-level
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin combo product: ${error.message}`);
      return {
        comboInfo: {
          images: productImages,
          comboImages: []
        }
      };
    }
  }

  /**
   * Lấy hình ảnh variant từ entity_has_media (physical_varial)
   * @param productId ID sản phẩm (không sử dụng, chỉ để tương thích)
   * @param variantId ID variant
   * @returns Danh sách hình ảnh variant
   */
  private async loadVariantImages(productId: number, variantId: number): Promise<any[]> {
    try {
      const mediaLinks = await this.entityHasMediaRepository.findByPhysicalVarial(variantId);

      if (mediaLinks.length === 0) {
        return [];
      }

      const mediaIds = mediaLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      return mediaLinks.map((link, index) => {
        const media = mediaRecords.find(m => m.id === link.mediaId);
        if (!media) return null;

        const viewUrl = media.storageKey ? `https://cdn.redai.vn/${media.storageKey}` : '';

        return {
          id: `variant-img-${String(index + 1).padStart(3, '0')}`,
          key: media.storageKey || '',
          url: viewUrl,
          mediaId: media.id,
          entityMediaId: link.id,
          name: media.name,
          size: media.size?.toString() || '0',
        };
      }).filter(Boolean);
    } catch (error) {
      this.logger.error(`Lỗi khi load variant images: ${error.message}`);
      return [];
    }
  }

  /**
   * Lấy hình ảnh version từ entity_has_media (version_id)
   * @param productId ID sản phẩm (không sử dụng, chỉ để tương thích)
   * @param versionId ID version
   * @returns Danh sách hình ảnh version
   */
  private async loadVersionImages(productId: number, versionId: number): Promise<any[]> {
    try {
      const mediaLinks = await this.entityHasMediaRepository.findByVersionId(versionId);

      if (mediaLinks.length === 0) {
        return [];
      }

      const mediaIds = mediaLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      return mediaLinks.map((link, index) => {
        const media = mediaRecords.find(m => m.id === link.mediaId);
        if (!media) return null;

        const viewUrl = media.storageKey ? `https://cdn.redai.vn/${media.storageKey}` : '';

        return {
          id: `version-img-${String(index + 1).padStart(3, '0')}`,
          key: media.storageKey || '',
          url: viewUrl,
          mediaId: media.id,
          entityMediaId: link.id,
          name: media.name,
          size: media.size?.toString() || '0',
        };
      }).filter(Boolean);
    } catch (error) {
      this.logger.error(`Lỗi khi load version images: ${error.message}`);
      return [];
    }
  }

  /**
   * Lấy hình ảnh package từ entity_has_media (product_plan_varial_id)
   * @param productId ID sản phẩm (không sử dụng, chỉ để tương thích)
   * @param packageId ID package
   * @returns Danh sách hình ảnh package
   */
  private async loadPackageImages(productId: number, packageId: number): Promise<any[]> {
    try {
      const mediaLinks = await this.entityHasMediaRepository.findByProductPlanVarialId(packageId);

      if (mediaLinks.length === 0) {
        return [];
      }

      const mediaIds = mediaLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      return mediaLinks.map((link, index) => {
        const media = mediaRecords.find(m => m.id === link.mediaId);
        if (!media) return null;

        const viewUrl = media.storageKey ? `https://cdn.redai.vn/${media.storageKey}` : '';

        return {
          id: `package-img-${String(index + 1).padStart(3, '0')}`,
          key: media.storageKey || '',
          url: viewUrl,
          mediaId: media.id,
          entityMediaId: link.id,
          name: media.name,
          size: media.size?.toString() || '0',
        };
      }).filter(Boolean);
    } catch (error) {
      this.logger.error(`Lỗi khi load package images: ${error.message}`);
      return [];
    }
  }

  /**
   * Lấy hình ảnh ticket từ entity_has_media (ticket_varial)
   * @param productId ID sản phẩm (không sử dụng, chỉ để tương thích)
   * @param ticketId ID ticket
   * @returns Danh sách hình ảnh ticket
   */
  private async loadTicketImages(productId: number, ticketId: number): Promise<any[]> {
    try {
      const mediaLinks = await this.entityHasMediaRepository.findByTicketVarial(ticketId);

      if (mediaLinks.length === 0) {
        return [];
      }

      const mediaIds = mediaLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      return mediaLinks.map((link, index) => {
        const media = mediaRecords.find(m => m.id === link.mediaId);
        if (!media) return null;

        const viewUrl = media.storageKey ? `https://cdn.redai.vn/${media.storageKey}` : '';

        return {
          id: `ticket-img-${String(index + 1).padStart(3, '0')}`,
          key: media.storageKey || '',
          url: viewUrl,
          mediaId: media.id,
          entityMediaId: link.id,
          name: media.name,
          size: media.size?.toString() || '0',
        };
      }).filter(Boolean);
    } catch (error) {
      this.logger.error(`Lỗi khi load ticket images: ${error.message}`);
      return [];
    }
  }

  /**
   * Lấy hình ảnh combo từ entity_has_media (product_combo_id)
   * @param productId ID sản phẩm
   * @returns Danh sách hình ảnh combo
   */
  private async loadComboImages(productId: number): Promise<any[]> {
    try {
      const mediaLinks = await this.entityHasMediaRepository.findByProductComboId(productId);

      if (mediaLinks.length === 0) {
        return [];
      }

      const mediaIds = mediaLinks
        .map(link => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      return mediaLinks.map((link, index) => {
        const media = mediaRecords.find(m => m.id === link.mediaId);
        if (!media) return null;

        const viewUrl = media.storageKey ? `https://cdn.redai.vn/${media.storageKey}` : '';

        return {
          id: `combo-img-${String(index + 1).padStart(3, '0')}`,
          key: media.storageKey || '',
          url: viewUrl,
          mediaId: media.id,
          entityMediaId: link.id,
          name: media.name,
          size: media.size?.toString() || '0',
        };
      }).filter(Boolean);
    } catch (error) {
      this.logger.error(`Lỗi khi load combo images: ${error.message}`);
      return [];
    }
  }

  /**
   * Chuyển đổi từ entity sang DTO
   * @param product Entity sản phẩm khách hàng
   * @returns DTO sản phẩm khách hàng
   */
  private mapToCustomerProductResponseDto(
    product: CustomerProduct,
  ): CustomerProductResponseDto {
    this.logger.log(`Đang chuyển đổi sản phẩm ${product.id} sang DTO`, `method: ${this.mapToCustomerProductResponseDto.name}`);

    // Đảm bảo tags không bị null
    const tags = product.tags || [];

    this.logger.log(
      `Sản phẩm có ${Array.isArray(tags) ? tags.length : 0} tags`,
    );

    return {
      id: product.id,
      name: product.name,
      price: product.price || null,
      typePrice: product.typePrice,
      productType: product.productType,
      description: product.description || null,
      tags: Array.isArray(tags) ? tags : null,
      userId: product.userId || null,
      createdAt: product.createdAt || null,
      updatedAt: product.updatedAt || null,
      status: product.status,
      customFields: product.customFields || null
    };
  }
}
