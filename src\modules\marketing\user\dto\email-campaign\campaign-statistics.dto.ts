import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thống kê chiến dịch email cụ thể
 */
export class EmailCampaignStatisticsDto {
  /**
   * ID chiến dịch
   * @example 1
   */
  @ApiProperty({
    description: 'ID chiến dịch',
    example: 1,
  })
  campaignId: number;

  /**
   * Tên chiến dịch
   * @example "Khuyến mãi Black Friday 2024"
   */
  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Khuyến mãi Black Friday 2024',
  })
  campaignName: string;

  /**
   * Tổng số email đã gửi
   * @example 1500
   */
  @ApiProperty({
    description: 'Tổng số email đã gửi',
    example: 1500,
  })
  totalSent: number;

  /**
   * Tổng số email đã mở
   * @example 750
   */
  @ApiProperty({
    description: 'Tổng số email đã mở',
    example: 750,
  })
  totalOpened: number;

  /**
   * Tổng số click
   * @example 300
   */
  @ApiProperty({
    description: 'Tổng số click',
    example: 300,
  })
  totalClicks: number;

  /**
   * Tổng số hủy đăng ký
   * @example 25
   */
  @ApiProperty({
    description: 'Tổng số hủy đăng ký',
    example: 25,
  })
  totalUnsubscribed: number;

  /**
   * Tổng số email bounce (thất bại)
   * @example 50
   */
  @ApiProperty({
    description: 'Tổng số email bounce (thất bại)',
    example: 50,
  })
  totalBounced: number;

  /**
   * Tổng số người nhận
   * @example 1600
   */
  @ApiProperty({
    description: 'Tổng số người nhận',
    example: 1600,
  })
  totalRecipients: number;

  /**
   * Trạng thái chiến dịch
   * @example "SENT"
   */
  @ApiProperty({
    description: 'Trạng thái chiến dịch',
    example: 'SENT',
  })
  status: string;

  /**
   * Thời gian tạo chiến dịch (Unix timestamp)
   * @example 1640995200
   */
  @ApiProperty({
    description: 'Thời gian tạo chiến dịch (Unix timestamp)',
    example: 1640995200,
  })
  createdAt: number;

  /**
   * Thời gian chạy chiến dịch (Unix timestamp)
   * @example 1640995200
   */
  @ApiProperty({
    description: 'Thời gian chạy chiến dịch (Unix timestamp)',
    example: 1640995200,
    required: false,
  })
  runAt?: number;
}

/**
 * DTO cho response API thống kê chiến dịch
 */
export class EmailCampaignStatisticsResponseDto {
  /**
   * Thông tin thống kê chiến dịch
   */
  @ApiProperty({
    description: 'Thông tin thống kê chiến dịch',
    type: EmailCampaignStatisticsDto,
  })
  statistics: EmailCampaignStatisticsDto;
}
