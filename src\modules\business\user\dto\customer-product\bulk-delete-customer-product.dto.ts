import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNumber, ArrayMinSize, ArrayMaxSize } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc xóa nhiều sản phẩm khách hàng cùng lúc
 */
export class BulkDeleteCustomerProductDto {
  @ApiProperty({
    description: 'Danh sách ID sản phẩm cần xóa',
    type: [Number],
    example: [123, 124, 125],
    minItems: 1,
    maxItems: 50,
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'Phải có ít nhất 1 sản phẩm để xóa' })
  @ArrayMaxSize(50, { message: 'Chỉ có thể xóa tối đa 50 sản phẩm cùng lúc' })
  @Type(() => Number)
  @IsNumber({}, { each: true, message: 'ID sản phẩm phải là số' })
  ids: number[];
}

/**
 * Response DTO cho việc xóa batch customer products
 */
export class BulkDeleteCustomerProductResponseDto {
  @ApiProperty({
    description: 'Số lượng sản phẩm đã xóa thành công',
    example: 3,
  })
  deletedCount: number;

  @ApiProperty({
    description: 'Tổng số sản phẩm được yêu cầu xóa',
    example: 5,
  })
  totalRequested: number;

  @ApiProperty({
    description: 'Danh sách ID sản phẩm xóa thất bại',
    example: [4, 5],
    type: [Number],
  })
  failedIds: number[];

  @ApiProperty({
    description: 'Danh sách lý do thất bại tương ứng với failedIds',
    example: ['Không tìm thấy sản phẩm', 'Không có quyền xóa sản phẩm này'],
    type: [String],
  })
  failedReasons: string[];

  @ApiProperty({
    description: 'Trạng thái tổng quát',
    example: 'partial_success',
    enum: ['success', 'partial_success', 'failed'],
  })
  status: 'success' | 'partial_success' | 'failed';
}
