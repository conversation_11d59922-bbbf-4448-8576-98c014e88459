import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsNumber,
  IsArray,
  ValidateNested,
  IsObject,
  Min,
  MaxLength,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  PriceTypeEnum,
  ProductTypeEnum
} from '@modules/business/enums';
import { CustomFieldInputDto } from '../custom-field-metadata.dto';
import { ProductImageOperationDto } from '../image-operations/image-operation.dto';
import { HasPriceDto, StringPriceDto } from '../price.dto';
import { ComboOperationsDto } from './combo-operations.dto';

/**
 * DTO cho thông tin cơ bản từ customer_products table
 */
export class CustomerProductBasicInfoDto {
  @ApiProperty({
    description: 'Tên combo (customer_products.name)',
    example: 'Combo Áo thun + Quần jean cao cấp',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  name?: string;

  @ApiProperty({
    description: '<PERSON><PERSON> tả combo (customer_products.description)',
    example: 'Combo thời trang nam gồm áo thun cotton và quần jean slim fit',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Loại sản phẩm (customer_products.product_type)',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.COMBO,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductTypeEnum)
  productType?: ProductTypeEnum;

  @ApiProperty({
    description: 'Danh sách tags (customer_products.tags)',
    type: [String],
    example: ['combo', 'thời trang', 'nam', 'tiết kiệm'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * DTO cho thông tin giá từ customer_products table
 */
export class CustomerProductPricingDto {
  @ApiProperty({
    description: 'Giá hiển thị cho khách hàng (customer_products.price) - Giá do người dùng nhập, hiển thị trên giao diện',
    example: {
      listPrice: 800000,
      salePrice: 650000,
      currency: 'VND'
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  displayPrice?: HasPriceDto | StringPriceDto; // Type-safe union

  @ApiProperty({
    description: 'Loại giá combo (customer_products.type_price)',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
  })
  @IsOptional()
  @IsEnum(PriceTypeEnum)
  typePrice?: PriceTypeEnum;
}

/**
 * DTO cho combo item trong combo_products.combo_items
 */
export class ComboItemDto {
  @ApiProperty({
    description: 'ID sản phẩm trong combo',
    example: 123,
  })
  @IsNumber()
  productId: number;

  @ApiProperty({
    description: 'Số lượng sản phẩm trong combo',
    example: 2,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  total: number;
}

/**
 * DTO cho thông tin từ bảng combo_products
 */
export class ComboProductInfoDto {
  @ApiProperty({
    description: 'Giá tính toán tự động từ combo items (combo_products.price) - Hệ thống tự động tính toán, không cần nhập',
    example: {
      listPrice: 1000000,
      salePrice: 900000,
      currency: 'VND',
      calculatedAt: 1750219218427,
      note: 'Giá tính toán từ tổng giá các sản phẩm con'
    },
    required: false,
    readOnly: true,
  })
  @IsOptional()
  @IsObject()
  calculatedPrice?: HasPriceDto; // Type-safe, chỉ hỗ trợ HasPriceDto

  @ApiProperty({
    description: 'Danh sách sản phẩm trong combo (combo_products.combo_items) - Cập nhật trực tiếp',
    type: [ComboItemDto],
    example: [
      { productId: 123, total: 2 },
      { productId: 456, total: 1 }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ComboItemDto)
  comboItems?: ComboItemDto[];

  @ApiProperty({
    description: 'Số lượng combo tối đa có thể bán (combo_products.max_quantity)',
    example: 100,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  maxQuantity?: number;
}

// Xóa ComboAdvancedInfoDto vì không cần thiết

/**
 * DTO hoàn chỉnh cho việc cập nhật combo product
 *
 * 🏗️ KIẾN TRÚC:
 * - customer_products table (customerProduct + customerProductPricing + customFields)
 * - combo_products table (comboProduct - comboItems, maxQuantity, calculatedPrice)
 * - entity_has_media table:
 *   + product_id: Ảnh chính combo (productImageOperations)
 *   + product_combo_id: Ảnh chi tiết combo (comboImageOperations)
 *
 * 🎯 CÁCH SỬ DỤNG:
 * - Combo items: Cập nhật trực tiếp qua comboProduct.comboItems (quan hệ 1:1)
 * - Product images: Sử dụng productImageOperations (entity_has_media.product_id)
 * - Combo images: Sử dụng comboImageOperations (entity_has_media.product_combo_id)
 *
 * 💰 GIÁ CẢ:
 * - customerProductPricing.displayPrice: Giá hiển thị do user nhập
 * - comboProduct.calculatedPrice: Giá tự động tính từ combo items (read-only)
 */
export class CompleteUpdateComboProductDto {
  // ========== CUSTOMER PRODUCTS TABLE ==========
  @ApiProperty({
    description: 'Thông tin cơ bản từ customer_products table',
    type: CustomerProductBasicInfoDto,
    example: {
      name: 'Combo Áo thun + Quần jean cao cấp',
      description: 'Combo thời trang nam gồm áo thun cotton và quần jean slim fit',
      productType: 'COMBO',
      tags: ['combo', 'thời trang', 'nam', 'tiết kiệm']
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CustomerProductBasicInfoDto)
  customerProduct?: CustomerProductBasicInfoDto;

  @ApiProperty({
    description: 'Thông tin giá từ customer_products table',
    type: CustomerProductPricingDto,
    example: {
      displayPrice: {
        listPrice: 800000,
        salePrice: 650000,
        currency: 'VND'
      },
      typePrice: 'HAS_PRICE'
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CustomerProductPricingDto)
  customerProductPricing?: CustomerProductPricingDto;

  @ApiProperty({
    description: 'Custom fields từ customer_products.custom_fields',
    type: [CustomFieldInputDto],
    example: [
      {
        customFieldId: 1,
        value: { value: 'Premium' }
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];

  // ========== COMBO PRODUCTS TABLE ==========
  @ApiProperty({
    description: 'Thông tin từ combo_products table',
    type: ComboProductInfoDto,
    example: {
      comboItems: [
        { productId: 123, total: 2 },
        { productId: 456, total: 1 }
      ],
      maxQuantity: 100
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ComboProductInfoDto)
  comboProduct?: ComboProductInfoDto;

  // ========== IMAGE OPERATIONS - CHỈ CHO QUAN HỆ 1:NHIỀU ==========
  @ApiProperty({
    description: 'Thao tác với hình ảnh product level (entity_has_media.product_id) - Ảnh chính của combo product',
    type: [ProductImageOperationDto],
    example: [
      {
        operation: 'add',
        mediaId: 'dbadc742-60b9-4fac-969d-7330ed1daa5e'
      },
      {
        operation: 'delete',
        entityMediaId: 123
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductImageOperationDto)
  productImageOperations?: ProductImageOperationDto[];

  @ApiProperty({
    description: 'Thao tác với hình ảnh combo level (entity_has_media.product_combo_id) - Ảnh chi tiết combo',
    type: [ProductImageOperationDto],
    example: [
      {
        operation: 'add',
        mediaId: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2'
      },
      {
        operation: 'delete',
        entityMediaId: 456
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductImageOperationDto)
  comboImageOperations?: ProductImageOperationDto[];
}
