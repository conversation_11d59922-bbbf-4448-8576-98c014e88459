import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, ValidateNested, Allow, IsObject } from 'class-validator';
import { Type, Transform } from 'class-transformer';

/**
 * Enum cho loại template ZNS theo Zalo API
 */
export enum ZnsTemplateType {
  CUSTOM = 1,           // ZNS tùy chỉnh
  AUTHENTICATION = 2,   // ZNS xác thực
  PAYMENT_REQUEST = 3,  // ZNS yêu cầu thanh toán
  VOUCHER = 4,         // ZNS voucher
  SERVICE_RATING = 5,  // ZNS Đánh giá dịch vụ
}

/**
 * Enum cho tag template ZNS - theo tài liệu Zalo API tag phải là string
 */
export enum ZnsTemplateTag {
  TRANSACTION = '1',      // Transaction
  CUSTOMER_CARE = '2',    // Customer care
  PROMOTION = '3',        // Promotion
}

/**
 * Enum cho param type
 */
export enum ZnsParamType {
  CUSTOMER_NAME = 1,        // Tên khách hàng (30)
  PHONE_NUMBER = 2,         // Số điện thoại (15)
  ADDRESS = 3,              // Địa chỉ (200)
  CODE = 4,                 // Mã số (30)
  CUSTOM_LABEL = 5,         // Nhãn tùy chỉnh (30)
  TRANSACTION_STATUS = 6,   // Trạng thái giao dịch (30)
  CONTACT_INFO = 7,         // Thông tin liên hệ (50)
  GENDER_TITLE = 8,         // Giới tính / Danh xưng (5)
  PRODUCT_BRAND = 9,        // Tên sản phẩm / Thương hiệu (200)
  QUANTITY_AMOUNT = 10,     // Số lượng / Số tiền (20)
  TIME = 11,                // Thời gian (20)
  OTP = 12,                 // OTP (10)
  URL = 13,                 // URL (200)
  CURRENCY_VND = 14,        // Tiền tệ (VNĐ) (12)
  BANK_TRANSFER_NOTE = 15,  // Bank transfer note (90)
}

/**
 * DTO cho param template ZNS
 */
export class ZnsTemplateParamDto {
  @ApiProperty({
    description: 'Tên của param',
    example: 'customer_name',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Loại param',
    enum: ZnsParamType,
    example: ZnsParamType.CUSTOMER_NAME,
  })
  @IsEnum(ZnsParamType)
  type: ZnsParamType;

  @ApiProperty({
    description: 'Dữ liệu mẫu của param',
    example: 'Nguyễn Văn A',
  })
  @IsString()
  @IsNotEmpty()
  sample_value: string;
}

/**
 * DTO cho component layout - sử dụng cấu trúc dynamic
 * Mỗi component sẽ có cấu trúc: { "COMPONENT_TYPE": { ...data } }
 *
 * @example
 * { "TITLE": { "value": "Xác nhận đơn hàng" } }
 * { "PARAGRAPH": { "value": "Cảm ơn <name> đã mua hàng" } }
 * { "BUTTONS": { "items": [{ "content": "https://example.com", "type": 1, "title": "Xem chi tiết" }] } }
 */
export class ZnsLayoutComponentDto {
  [key: string]: any;
}

/**
 * DTO cho layout section (header, body, footer)
 * Sử dụng index signature để cho phép các property động
 *
 * @example
 * {
 *   "components": [
 *     { "TITLE": { "value": "Xác nhận đơn hàng" } },
 *     { "PARAGRAPH": { "value": "Cảm ơn <name> đã mua hàng" } }
 *   ]
 * }
 */
export class ZnsLayoutSectionDto {
  [key: string]: any;
}

/**
 * DTO cho layout template ZNS
 * Sử dụng index signature để cho phép các property động (header, body, footer)
 *
 * @example
 * {
 *   "header": { "components": [...] },
 *   "body": { "components": [...] },
 *   "footer": { "components": [...] }
 * }
 */
export class ZnsTemplateLayoutDto {
  [key: string]: any;
}

/**
 * DTO cho việc đăng ký template ZNS theo Zalo API
 */
export class RegisterZnsTemplateDto {
  @ApiProperty({
    description: 'Tên mẫu tin (10-60 ký tự)',
    example: 'Mẫu chăm sóc khách hàng',
    minLength: 10,
    maxLength: 60,
  })
  @IsString()
  @IsNotEmpty()
  template_name: string;

  @ApiProperty({
    description: 'Loại mẫu tin',
    enum: ZnsTemplateType,
    example: ZnsTemplateType.CUSTOM,
  })
  @IsEnum(ZnsTemplateType)
  template_type: ZnsTemplateType;

  @ApiProperty({
    description: 'Tag mẫu tin (string theo tài liệu Zalo API)',
    enum: ZnsTemplateTag,
    example: ZnsTemplateTag.TRANSACTION,
  })
  @IsEnum(ZnsTemplateTag)
  tag: ZnsTemplateTag;

  @ApiProperty({
    description: 'Layout template bao gồm header, body, footer',
    example: {
      "body": {
        "components": [
          { "TITLE": { "value": "Xác nhận đơn hàng" } },
          { "PARAGRAPH": { "value": "Cảm ơn <name> đã mua hàng" } }
        ]
      }
    }
  })
  @IsObject()
  @Allow()
  layout: any;

  @ApiProperty({
    description: 'Mã tracking do đối tác tự định nghĩa',
    example: 'abc123',
  })
  @IsString()
  @IsNotEmpty()
  tracking_id: string;

  @ApiPropertyOptional({
    description: 'Thông tin về param (nếu có)',
    type: [ZnsTemplateParamDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ZnsTemplateParamDto)
  params?: ZnsTemplateParamDto[];

  @ApiPropertyOptional({
    description: 'Ghi chú kiểm duyệt (1-400 ký tự)',
    example: 'Ghi chú kiểm duyệt',
    minLength: 1,
    maxLength: 400,
  })
  @IsOptional()
  @IsString()
  note?: string;
}
