import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  getSchemaPath,
} from '@nestjs/swagger';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { ZaloZnsCampaignService } from '../services/zalo-zns-campaign.service';
import {
  CreateZnsCampaignDto,
  UpdateZnsCampaignDto,
  ZnsCampaignQueryDto,
  ZnsCampaignResponseDto,
  SendSingleZnsDto,
  SendBatchZnsDto,
  ZnsJobResponseDto,
} from '../dto/zalo';
import { ZaloZnsCampaign } from '../entities/zalo-zns-campaign.entity';
import { JwtUserGuard } from '@/modules/auth/guards';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';

/**
 * <PERSON> xử lý chiến dịch ZNS Zalo
 */
@ApiTags('Zalo ZNS Campaign')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo/:oaId/zns-campaigns')
export class ZaloZnsCampaignController {
  constructor(private readonly znsCampaignService: ZaloZnsCampaignService) {}

  /**
   * Tạo chiến dịch ZNS mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo chiến dịch ZNS mới',
    description: 'Tạo chiến dịch gửi ZNS với danh sách số điện thoại và template'
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo chiến dịch ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsCampaignResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy Official Account' })
  async createCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Body() createDto: CreateZnsCampaignDto,
  ): Promise<ApiResponseDto<ZaloZnsCampaign>> {
    const result = await this.znsCampaignService.createCampaign(user.id, oaId, createDto);
    return ApiResponseDto.success(result, 'Tạo chiến dịch ZNS thành công');
  }

  /**
   * Lấy danh sách chiến dịch ZNS
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách chiến dịch ZNS',
    description: 'Lấy danh sách chiến dịch ZNS với phân trang và tìm kiếm'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách chiến dịch ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(ZnsCampaignResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number', example: 100 },
                    itemCount: { type: 'number', example: 10 },
                    itemsPerPage: { type: 'number', example: 10 },
                    totalPages: { type: 'number', example: 10 },
                    currentPage: { type: 'number', example: 1 }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy Official Account' })
  async getCampaigns(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Query() queryDto: ZnsCampaignQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloZnsCampaign>>> {
    const result = await this.znsCampaignService.getCampaigns(user.id, oaId, queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách chiến dịch ZNS thành công');
  }

  /**
   * Lấy chi tiết chiến dịch ZNS
   */
  @Get(':campaignId')
  @ApiOperation({
    summary: 'Lấy chi tiết chiến dịch ZNS',
    description: 'Lấy thông tin chi tiết của một chiến dịch ZNS'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết chiến dịch ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsCampaignResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch ZNS' })
  async getCampaignDetail(
    @CurrentUser() user: JwtPayload,
    @Param('campaignId') campaignId: number,
  ): Promise<ApiResponseDto<ZaloZnsCampaign>> {
    const result = await this.znsCampaignService.getCampaignDetail(user.id, campaignId);
    return ApiResponseDto.success(result, 'Lấy chi tiết chiến dịch ZNS thành công');
  }

  /**
   * Cập nhật chiến dịch ZNS
   */
  @Put(':campaignId')
  @ApiOperation({
    summary: 'Cập nhật chiến dịch ZNS',
    description: 'Cập nhật thông tin chiến dịch ZNS (chỉ cho phép khi ở trạng thái DRAFT hoặc SCHEDULED)'
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật chiến dịch ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsCampaignResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Không thể cập nhật chiến dịch ở trạng thái hiện tại' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch ZNS' })
  async updateCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('campaignId') campaignId: number,
    @Body() updateDto: UpdateZnsCampaignDto,
  ): Promise<ApiResponseDto<ZaloZnsCampaign>> {
    const result = await this.znsCampaignService.updateCampaign(user.id, campaignId, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật chiến dịch ZNS thành công');
  }

  /**
   * Xóa chiến dịch ZNS
   */
  @Delete(':campaignId')
  @ApiOperation({
    summary: 'Xóa chiến dịch ZNS',
    description: 'Xóa chiến dịch ZNS (chỉ cho phép khi không đang chạy)'
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa chiến dịch ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { type: 'boolean', example: true }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Không thể xóa chiến dịch đang chạy' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch ZNS' })
  async deleteCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('campaignId') campaignId: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.znsCampaignService.deleteCampaign(user.id, campaignId);
    return ApiResponseDto.success(result, 'Xóa chiến dịch ZNS thành công');
  }

  /**
   * Chạy chiến dịch ZNS ngay lập tức
   */
  @Post(':campaignId/run')
  @ApiOperation({
    summary: 'Chạy chiến dịch ZNS ngay lập tức',
    description: 'Bắt đầu chạy chiến dịch ZNS và đẩy vào queue để xử lý'
  })
  @ApiResponse({
    status: 200,
    description: 'Chạy chiến dịch ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsCampaignResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Chiến dịch không ở trạng thái có thể chạy' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch ZNS' })
  async runCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('campaignId') campaignId: number,
  ): Promise<ApiResponseDto<ZaloZnsCampaign>> {
    const result = await this.znsCampaignService.runCampaign(user.id, campaignId);
    return ApiResponseDto.success(result, 'Chạy chiến dịch ZNS thành công');
  }

  /**
   * Hủy chiến dịch ZNS
   */
  @Post(':campaignId/cancel')
  @ApiOperation({
    summary: 'Hủy chiến dịch ZNS',
    description: 'Hủy chiến dịch ZNS đang chạy hoặc đã lên lịch'
  })
  @ApiResponse({
    status: 200,
    description: 'Hủy chiến dịch ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsCampaignResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Không thể hủy chiến dịch ở trạng thái hiện tại' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch ZNS' })
  async cancelCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('campaignId') campaignId: number,
  ): Promise<ApiResponseDto<ZaloZnsCampaign>> {
    const result = await this.znsCampaignService.cancelCampaign(user.id, campaignId);
    return ApiResponseDto.success(result, 'Hủy chiến dịch ZNS thành công');
  }

  /**
   * Tạo job gửi ZNS đơn lẻ
   */
  @Post('send-single-zns')
  @ApiOperation({
    summary: 'Tạo job gửi ZNS đơn lẻ',
    description: 'Tạo job để gửi một tin nhắn ZNS đơn lẻ và đẩy vào queue'
  })
  @ApiResponse({
    status: 200,
    description: 'Tạo job gửi ZNS đơn lẻ thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsJobResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy Official Account' })
  async sendSingleZns(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Body() sendDto: SendSingleZnsDto,
  ): Promise<ApiResponseDto<ZnsJobResponseDto>> {
    const jobId = await this.znsCampaignService.createSingleZnsJob(
      oaId,
      sendDto.phone,
      sendDto.templateId,
      sendDto.templateData,
      undefined,
      sendDto.trackingId,
    );
    return ApiResponseDto.success({ jobId }, 'Tạo job gửi ZNS đơn lẻ thành công');
  }

  /**
   * Tạo job gửi batch ZNS
   */
  @Post('send-batch-zns')
  @ApiOperation({
    summary: 'Tạo job gửi batch ZNS',
    description: 'Tạo job để gửi nhiều tin nhắn ZNS cùng lúc và đẩy vào queue'
  })
  @ApiResponse({
    status: 200,
    description: 'Tạo job gửi batch ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsJobResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy Official Account' })
  async sendBatchZns(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Body() sendDto: SendBatchZnsDto,
  ): Promise<ApiResponseDto<ZnsJobResponseDto>> {
    const jobId = await this.znsCampaignService.createBatchZnsJob(
      oaId,
      sendDto.messages,
      undefined,
      sendDto.batchIndex || 0,
      sendDto.totalBatches || 1,
    );
    return ApiResponseDto.success({ jobId }, 'Tạo job gửi batch ZNS thành công');
  }
}
