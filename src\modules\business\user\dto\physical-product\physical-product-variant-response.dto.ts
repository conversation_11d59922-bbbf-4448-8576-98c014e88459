import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { ShipmentConfigType } from '@modules/business/interfaces';

/**
 * Response DTO cho physical product variant
 */
export class PhysicalProductVariantResponseDto {
  @ApiProperty({
    description: 'ID biến thể',
    example: 456,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'ID sản phẩm vật lý cha',
    example: 123,
  })
  @Expose()
  physicalProductId: number;

  @ApiProperty({
    description: 'Tên biến thể',
    example: 'Áo thun đỏ size M',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Mô tả biến thể',
    example: 'Áo thun màu đỏ, size M, chất liệu cotton',
    required: false,
  })
  @Expose()
  description?: string;

  @ApiProperty({
    description: 'Mã SKU riêng của biến thể',
    example: 'SHIRT-RED-M-001',
    required: false,
  })
  @Expose()
  sku?: string;

  @ApiProperty({
    description: 'Mã vạch riêng của biến thể',
    example: '1234567890123',
    required: false,
  })
  @Expose()
  barcode?: string;

  @ApiProperty({
    description: 'Các thuộc tính biến thể',
    example: {
      color: 'Đỏ',
      size: 'M',
      material: 'Cotton'
    },
    required: false,
  })
  @Expose()
  attributes?: any;

  @ApiProperty({
    description: 'Giá riêng cho biến thể',
    example: {
      listPrice: 500000,
      salePrice: 450000,
      currency: 'VND'
    },
    required: false,
  })
  @Expose()
  price?: any;

  @ApiProperty({
    description: 'Cấu hình vận chuyển riêng cho biến thể',
    example: {
      widthCm: 25,
      heightCm: 5,
      lengthCm: 30,
      weightGram: 200
    },
    required: false,
  })
  @Expose()
  shipmentConfig?: ShipmentConfigType;

  @ApiProperty({
    description: 'Danh sách hình ảnh biến thể',
    example: [
      {
        id: '123e4567-e89b-12d3-a456-426614174000',
        key: 'business/IMAGE/2025/06/variant-red-m.jpg',
        url: 'https://cdn.redai.vn/business/IMAGE/2025/06/variant-red-m.jpg?expires=1750214285&signature=abc123',
        position: 1,
        mimeType: 'image/jpeg',
        name: 'Variant Image Red M',
        size: '0'
      }
    ],
    required: false,
  })
  @Expose()
  images?: Array<{
    id: string;
    key: string;
    url: string;
    position: number;
    mimeType: string;
    name: string;
    size: string;
  }>;
}
