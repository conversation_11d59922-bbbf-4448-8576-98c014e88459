import { Injectable } from '@nestjs/common';
import { CouponRepository } from '@modules/r-point/repositories';
import { CreateCouponDto, UpdateCouponDto, CouponResponseDto, GetCouponsDto } from '../dto';
import { Coupon } from '@modules/r-point/entities';
import { AppException } from '@common/exceptions';
import { RPOINT_ERROR_CODES } from '@modules/r-point/errors';
import { v4 as uuidv4 } from 'uuid';
import { CouponStatus } from '@modules/r-point/enums';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException } from '@nestjs/common';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Service xử lý logic liên quan đến coupon cho admin
 */
@Injectable()
export class CouponAdminService {
  constructor(
    private readonly couponRepository: CouponRepository,
    @InjectRepository(Coupon)
    private readonly couponRepositoryTypeorm: Repository<Coupon>,
  ) {}

  /**
   * Tạo mới coupon
   * @param createCouponDto Thông tin coupon cần tạo
   * @returns Thông tin coupon đã tạo
   */
  async create(createCouponDto: CreateCouponDto): Promise<CouponResponseDto> {
    // Kiểm tra xem mã coupon đã tồn tại chưa
    const existingCoupon = await this.couponRepository.findOne({
      where: { code: createCouponDto.code },
    });

    if (existingCoupon) {
      throw new AppException(
        RPOINT_ERROR_CODES.COUPON_ALREADY_EXISTS,
        `Mã giảm giá ${createCouponDto.code} đã tồn tại`
      );
    }

    // Kiểm tra thời gian hợp lệ
    if (createCouponDto.startDate >= createCouponDto.endDate) {
      throw new AppException(
        RPOINT_ERROR_CODES.INVALID_DATE_RANGE,
        'Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc'
      );
    }

    // Tạo coupon mới
    const now = Date.now();
    const coupon = new Coupon();
    coupon.id = uuidv4();
    coupon.code = createCouponDto.code;
    coupon.description = createCouponDto.description !== undefined ? createCouponDto.description : '';
    coupon.discountType = createCouponDto.discountType;
    coupon.discountValue = createCouponDto.discountValue;
    coupon.minOrderValue = createCouponDto.minOrderValue !== undefined ? createCouponDto.minOrderValue : 0;
    coupon.maxDiscountAmount = createCouponDto.maxDiscountAmount !== undefined ? createCouponDto.maxDiscountAmount : 0;
    coupon.startDate = createCouponDto.startDate;
    coupon.endDate = createCouponDto.endDate;
    coupon.usageLimit = createCouponDto.usageLimit !== undefined ? createCouponDto.usageLimit : 0;
    coupon.perUserLimit = createCouponDto.perUserLimit !== undefined ? createCouponDto.perUserLimit : 1;
    coupon.status = CouponStatus.ACTIVE;
    coupon.createdAt = now;
    coupon.updatedAt = now;

    try {
      // Lưu coupon vào database
      const savedCoupon = await this.couponRepository.save(coupon);
      return this.mapCouponToDto(savedCoupon);
    } catch (error) {
      throw new AppException(
        RPOINT_ERROR_CODES.COUPON_CREATION_FAILED,
        'Không thể tạo mã giảm giá',
        error
      );
    }
  }

  /**
   * Cập nhật thông tin coupon
   * @param id ID của coupon cần cập nhật
   * @param updateCouponDto Thông tin cần cập nhật
   * @returns Thông tin coupon sau khi cập nhật
   */
  async update(id: string, updateCouponDto: UpdateCouponDto): Promise<CouponResponseDto> {
    // Tìm coupon theo ID
    const coupon = await this.couponRepository.findOne({ where: { id } });
    if (!coupon) {
      throw new AppException(
        RPOINT_ERROR_CODES.COUPON_NOT_FOUND,
        `Không tìm thấy mã giảm giá với ID ${id}`
      );
    }

    // Kiểm tra thời gian hợp lệ nếu có cập nhật thời gian
    if (
      updateCouponDto.startDate !== undefined &&
      updateCouponDto.endDate !== undefined &&
      updateCouponDto.startDate >= updateCouponDto.endDate
    ) {
      throw new AppException(
        RPOINT_ERROR_CODES.INVALID_DATE_RANGE,
        'Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc'
      );
    } else if (
      updateCouponDto.startDate !== undefined &&
      updateCouponDto.endDate === undefined &&
      updateCouponDto.startDate >= coupon.endDate
    ) {
      throw new AppException(
        RPOINT_ERROR_CODES.INVALID_DATE_RANGE,
        'Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc'
      );
    } else if (
      updateCouponDto.startDate === undefined &&
      updateCouponDto.endDate !== undefined &&
      coupon.startDate >= updateCouponDto.endDate
    ) {
      throw new AppException(
        RPOINT_ERROR_CODES.INVALID_DATE_RANGE,
        'Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc'
      );
    }

    // Cập nhật thông tin coupon
    if (updateCouponDto.description !== undefined) {
      coupon.description = updateCouponDto.description;
    }
    if (updateCouponDto.discountType !== undefined) {
      coupon.discountType = updateCouponDto.discountType;
    }
    if (updateCouponDto.discountValue !== undefined) {
      coupon.discountValue = updateCouponDto.discountValue;
    }
    if (updateCouponDto.minOrderValue !== undefined) {
      coupon.minOrderValue = updateCouponDto.minOrderValue;
    }
    if (updateCouponDto.maxDiscountAmount !== undefined) {
      // Đảm bảo không gán null cho maxDiscountAmount
      coupon.maxDiscountAmount = updateCouponDto.maxDiscountAmount || 0;
    }
    if (updateCouponDto.startDate !== undefined) {
      coupon.startDate = updateCouponDto.startDate;
    }
    if (updateCouponDto.endDate !== undefined) {
      coupon.endDate = updateCouponDto.endDate;
    }
    if (updateCouponDto.usageLimit !== undefined) {
      // Đảm bảo không gán null cho usageLimit
      coupon.usageLimit = updateCouponDto.usageLimit || 0;
    }
    if (updateCouponDto.perUserLimit !== undefined) {
      coupon.perUserLimit = updateCouponDto.perUserLimit;
    }
    if (updateCouponDto.status !== undefined) {
      coupon.status = updateCouponDto.status;
    }

    // Cập nhật thời gian cập nhật
    coupon.updatedAt = Date.now();

    try {
      // Lưu coupon vào database
      const updatedCoupon = await this.couponRepository.save(coupon);
      return this.mapCouponToDto(updatedCoupon);
    } catch (error) {
      throw new AppException(
        RPOINT_ERROR_CODES.COUPON_UPDATE_FAILED,
        'Không thể cập nhật mã giảm giá',
        error
      );
    }
  }

  /**
   * Xóa coupon
   * @param id ID của coupon cần xóa
   * @returns Thông tin xóa thành công
   */
  async remove(id: string): Promise<{ success: boolean }> {
    // Tìm coupon theo ID
    const coupon = await this.couponRepository.findOne({ where: { id } });
    if (!coupon) {
      throw new AppException(
        RPOINT_ERROR_CODES.COUPON_NOT_FOUND,
        `Không tìm thấy mã giảm giá với ID ${id}`
      );
    }

    try {
      // Xóa coupon khỏi database
      await this.couponRepository.remove(coupon);
      return { success: true };
    } catch (error) {
      throw new AppException(
        RPOINT_ERROR_CODES.COUPON_DELETION_FAILED,
        'Không thể xóa mã giảm giá',
        error
      );
    }
  }

  /**
   * Xóa nhiều coupon
   * @param ids Danh sách ID của các coupon cần xóa
   */  async bulkDelete(ids: string[]): Promise<void> {
    try {
      if (!ids || ids.length === 0) {
        throw new AppException(
          RPOINT_ERROR_CODES.COUPON_INVALID,
          'Danh sách ID không được để trống'
        );
      }

      // Kiểm tra xem các coupon có tồn tại không
      const coupons = await this.couponRepository.findBy({ id: { $in: ids } as any });
      if (coupons.length !== ids.length) {
        throw new AppException(
          RPOINT_ERROR_CODES.COUPON_NOT_FOUND,
          'Một số coupon không tồn tại'
        );
      }      // Kiểm tra trạng thái của các coupon
      const activeCoupons = coupons.filter(coupon => coupon.status === CouponStatus.ACTIVE);
      if (activeCoupons.length > 0) {
        throw new AppException(
          RPOINT_ERROR_CODES.COUPON_INVALID,
          'Không thể xóa coupon đang hoạt động'
        );
      }

      // Xóa các coupon
      await this.couponRepository.delete(ids);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        RPOINT_ERROR_CODES.COUPON_INVALID,
        `Không thể xóa các coupon: ${error.message}`
      );
    }
  }

  /**
   * Chuyển đổi Coupon entity sang DTO
   * @param coupon Coupon entity
   * @returns CouponResponseDto
   */
  private mapCouponToDto(coupon: Coupon): CouponResponseDto {
    return {
      id: coupon.id,
      code: coupon.code,
      description: coupon.description || '',
      discountType: coupon.discountType,
      discountValue: coupon.discountValue,
      minOrderValue: coupon.minOrderValue || 0,
      maxDiscountAmount: coupon.maxDiscountAmount || 0,
      startDate: coupon.startDate,
      endDate: coupon.endDate,
      usageLimit: coupon.usageLimit || 0,
      perUserLimit: coupon.perUserLimit || 1,
      status: coupon.status,
      createdAt: coupon.createdAt,
      updatedAt: coupon.updatedAt
    };
  }

  /**
   * Lấy danh sách coupon có phân trang
   * @param query Thông tin phân trang và tìm kiếm
   * @returns Danh sách coupon và thông tin phân trang
   */
  async findAll(query: GetCouponsDto): Promise<PaginatedResult<CouponResponseDto>> {
    const { page = 1, limit = 10, search, status, sortBy, sortDirection } = query;
    const skip = (page - 1) * limit;

    // Tạo query builder
    const queryBuilder = this.couponRepositoryTypeorm.createQueryBuilder('coupon');

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      queryBuilder.andWhere(
        '(coupon.code ILIKE :search OR coupon.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Thêm điều kiện lọc theo trạng thái nếu có
    if (status) {
      queryBuilder.andWhere('coupon.status = :status', { status });
    }

    // Sắp xếp
    if (sortBy) {
      queryBuilder.orderBy(`coupon.${sortBy}`, sortDirection ?? 'DESC');
    } else {
      queryBuilder.orderBy('coupon.createdAt', 'DESC');
    }

    // Lấy tổng số bản ghi
    const totalItems = await queryBuilder.getCount();

    // Thêm phân trang
    queryBuilder.skip(skip).take(limit);

    // Thực hiện query
    const items = await queryBuilder.getMany();

    // Tính toán thông tin phân trang
    const totalPages = Math.ceil(totalItems / limit);
    const meta = {
      totalItems,
      itemCount: items.length,
      itemsPerPage: limit,
      totalPages,
      currentPage: page
    };

    return {
      items: items.map(item => ({
        id: item.id,
        code: item.code,
        description: item.description,
        discountType: item.discountType,
        discountValue: item.discountValue,
        minOrderValue: item.minOrderValue,
        maxDiscountAmount: item.maxDiscountAmount,
        startDate: item.startDate,
        endDate: item.endDate,
        usageLimit: item.usageLimit,
        perUserLimit: item.perUserLimit,
        status: item.status,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt
      })),
      meta
    };
  }
}
