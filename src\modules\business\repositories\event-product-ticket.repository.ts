import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { EventProductTicket } from '../entities/event-product-ticket.entity';

/**
 * Repository cho EventProductTicket entity
 * Xử lý các thao tác database cho vé sự kiện
 */
@Injectable()
export class EventProductTicketRepository {
  private readonly logger = new Logger(EventProductTicketRepository.name);

  constructor(
    @InjectRepository(EventProductTicket)
    private readonly repository: Repository<EventProductTicket>,
  ) {}

  /**
   * Tạo vé sự kiện mới
   * @param data Dữ liệu vé sự kiện
   * @returns Vé sự kiện đã tạo
   */
  async create(data: Partial<EventProductTicket>): Promise<EventProductTicket> {
    const ticket = this.repository.create(data);
    return this.repository.save(ticket);
  }

  /**
   * Tìm vé sự kiện theo ID
   * @param id ID vé sự kiện
   * @returns Vé sự kiện hoặc null
   */
  async findById(id: number): Promise<EventProductTicket | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm tất cả vé theo event product ID
   * @param eventProductId ID sản phẩm sự kiện
   * @returns Danh sách vé sự kiện
   */
  async findByEventProductId(eventProductId: number): Promise<EventProductTicket[]> {
    return this.repository.find({
      where: { eventProductId },
      order: { id: 'ASC' },
    });
  }

  /**
   * Cập nhật vé sự kiện
   * @param id ID vé sự kiện
   * @param data Dữ liệu cập nhật
   * @returns Vé sự kiện đã cập nhật
   */
  async update(id: number, data: Partial<EventProductTicket>): Promise<EventProductTicket | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa vé sự kiện
   * @param id ID vé sự kiện
   */
  async delete(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Xóa nhiều vé sự kiện
   * @param ids Danh sách ID vé sự kiện
   */
  async bulkDelete(ids: number[]): Promise<void> {
    if (ids.length === 0) {
      return;
    }
    
    await this.repository.delete({
      id: In(ids),
    });
  }

  /**
   * Lưu vé sự kiện
   * @param ticket Vé sự kiện cần lưu
   * @returns Vé sự kiện đã lưu
   */
  async save(ticket: EventProductTicket): Promise<EventProductTicket> {
    return this.repository.save(ticket);
  }

  /**
   * Tìm nhiều vé sự kiện theo danh sách ID
   * @param ids Danh sách ID vé sự kiện
   * @returns Danh sách vé sự kiện
   */
  async findByIds(ids: number[]): Promise<EventProductTicket[]> {
    if (ids.length === 0) {
      return [];
    }
    
    return this.repository.findByIds(ids);
  }

  /**
   * Kiểm tra vé sự kiện có tồn tại không
   * @param id ID vé sự kiện
   * @returns true nếu tồn tại
   */
  async exists(id: number): Promise<boolean> {
    const count = await this.repository.count({
      where: { id },
    });
    return count > 0;
  }

  /**
   * Kiểm tra SKU có tồn tại không
   * @param sku Mã SKU
   * @param excludeId ID vé cần loại trừ
   * @returns true nếu SKU đã tồn tại
   */
  async existsBySku(sku: string, excludeId?: number): Promise<boolean> {
    const queryBuilder = this.repository.createQueryBuilder('ticket');
    queryBuilder.where('ticket.sku = :sku', { sku });
    
    if (excludeId) {
      queryBuilder.andWhere('ticket.id != :excludeId', { excludeId });
    }

    const count = await queryBuilder.getCount();
    return count > 0;
  }

  /**
   * Tìm vé theo khoảng giá
   * @param minPrice Giá tối thiểu
   * @param maxPrice Giá tối đa
   * @returns Danh sách vé sự kiện
   */
  async findByPriceRange(minPrice: number, maxPrice: number): Promise<EventProductTicket[]> {
    return this.repository
      .createQueryBuilder('ticket')
      .where('ticket.price >= :minPrice', { minPrice })
      .andWhere('ticket.price <= :maxPrice', { maxPrice })
      .orderBy('ticket.price', 'ASC')
      .getMany();
  }

  /**
   * Tìm vé đang trong thời gian bán
   * @param currentTime Thời gian hiện tại (epoch milliseconds)
   * @returns Danh sách vé đang bán
   */
  async findTicketsOnSale(currentTime: number): Promise<EventProductTicket[]> {
    return this.repository
      .createQueryBuilder('ticket')
      .where('ticket.saleStart <= :currentTime', { currentTime })
      .andWhere('ticket.saleEnd >= :currentTime', { currentTime })
      .getMany();
  }

  /**
   * Tìm vé sắp mở bán
   * @param currentTime Thời gian hiện tại (epoch milliseconds)
   * @param hoursAhead Số giờ trước khi mở bán
   * @returns Danh sách vé sắp mở bán
   */
  async findUpcomingSaleTickets(currentTime: number, hoursAhead: number = 24): Promise<EventProductTicket[]> {
    const futureTime = currentTime + (hoursAhead * 60 * 60 * 1000);
    
    return this.repository
      .createQueryBuilder('ticket')
      .where('ticket.saleStart > :currentTime', { currentTime })
      .andWhere('ticket.saleStart <= :futureTime', { futureTime })
      .orderBy('ticket.saleStart', 'ASC')
      .getMany();
  }

  /**
   * Đếm số lượng vé theo event product ID
   * @param eventProductId ID sản phẩm sự kiện
   * @returns Số lượng vé
   */
  async countByEventProductId(eventProductId: number): Promise<number> {
    return this.repository.count({
      where: { eventProductId },
    });
  }

  /**
   * Tìm vé theo múi giờ
   * @param timeZone Múi giờ
   * @returns Danh sách vé sự kiện
   */
  async findByTimeZone(timeZone: string): Promise<EventProductTicket[]> {
    return this.repository.find({
      where: { timeZone },
    });
  }

  /**
   * Tính tổng số lượng vé của một sự kiện
   * @param eventProductId ID sản phẩm sự kiện
   * @returns Tổng số lượng vé
   */
  async getTotalQuantityByEventProductId(eventProductId: number): Promise<number> {
    const result = await this.repository
      .createQueryBuilder('ticket')
      .select('SUM(ticket.totalQuantity)', 'total')
      .where('ticket.eventProductId = :eventProductId', { eventProductId })
      .getRawOne();
    
    return parseInt(result?.total || '0', 10);
  }

  /**
   * Tìm vé có giá thấp nhất của một sự kiện
   * @param eventProductId ID sản phẩm sự kiện
   * @returns Vé có giá thấp nhất
   */
  async findCheapestTicket(eventProductId: number): Promise<EventProductTicket | null> {
    return this.repository
      .createQueryBuilder('ticket')
      .where('ticket.eventProductId = :eventProductId', { eventProductId })
      .orderBy('ticket.price', 'ASC')
      .getOne();
  }

  /**
   * Tìm vé có giá cao nhất của một sự kiện
   * @param eventProductId ID sản phẩm sự kiện
   * @returns Vé có giá cao nhất
   */
  async findMostExpensiveTicket(eventProductId: number): Promise<EventProductTicket | null> {
    return this.repository
      .createQueryBuilder('ticket')
      .where('ticket.eventProductId = :eventProductId', { eventProductId })
      .orderBy('ticket.price', 'DESC')
      .getOne();
  }
}
