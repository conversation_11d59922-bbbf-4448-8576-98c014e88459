import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsString, IsUUID, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc truy vấn danh sách liên kết media với entity
 */
export class QueryEntityHasMediaDto {
  @ApiProperty({
    description: 'Số trang',
    example: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: 'Số lượng item trên mỗi trang',
    example: 10,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiProperty({
    description: 'ID sản phẩm (filter theo sản phẩm)',
    example: 456,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  productId?: number;

  @ApiProperty({
    description: 'ID biến thể vật lý (filter theo biến thể vật lý)',
    example: 789,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  physicalVarial?: number;

  @ApiProperty({
    description: 'ID biến thể vé (filter theo biến thể vé)',
    example: 101,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  ticketVarial?: number;

  @ApiProperty({
    description: 'ID phiên bản (filter theo phiên bản)',
    example: 202,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  versionId?: number;

  @ApiProperty({
    description: 'ID combo sản phẩm (filter theo combo)',
    example: 303,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  productComboId?: number;

  @ApiProperty({
    description: 'ID gói dịch vụ (filter theo gói dịch vụ)',
    example: 404,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  productPlanVarialId?: number;

  @ApiProperty({
    description: 'ID media (filter theo media)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  mediaId?: string;
}
