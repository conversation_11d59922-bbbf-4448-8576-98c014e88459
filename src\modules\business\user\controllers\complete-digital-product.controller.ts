import {
  Controller,
  Put,
  Get,
  Param,
  Body,
  UseGuards,
  ParseIntPipe,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto } from '@common/response';
import { CompleteDigitalProductService } from '../services/complete-digital-product.service';
import { CompleteUpdateDigitalProductDto } from '../dto/digital-product/complete-update-digital-product.dto';
import { CompleteDigitalProductResponseDto } from '../dto/digital-product/complete-digital-product-response.dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các thao tác hoàn chỉnh với Digital Product
 * Bao gồm cập nhật và lấy chi tiết sản phẩm số với tất cả thông tin liên quan
 */
@ApiTags(SWAGGER_API_TAGS.USER_BUSINESS_VARIANT_PRODUCT)
@Controller('user/products/digital')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class CompleteDigitalProductController {
  constructor(
    private readonly completeDigitalProductService: CompleteDigitalProductService,
  ) {}

  /**
   * Cập nhật hoàn chỉnh digital product
   * Bao gồm customer_products + digital_products + versions + images
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật hoàn chỉnh sản phẩm số',
    description: `
    Cập nhật toàn bộ thông tin sản phẩm số bao gồm:
    - Thông tin cơ bản (tên, mô tả, tags)
    - Thông tin giá cả
    - Thông tin số hóa (phương thức giao hàng, thời gian)
    - Custom fields
    - Thao tác với phiên bản (ADD/UPDATE/DELETE)
    - Thao tác với hình ảnh (product level và version level)
    
    API này sẽ trả về thông tin sản phẩm đã cập nhật cùng với các URL upload
    cho hình ảnh mới (nếu có).
    `,
  })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm số cần cập nhật',
    type: 'integer',
    example: 123,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật sản phẩm số thành công',
    type: CompleteDigitalProductResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm hoặc không có quyền truy cập',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền cập nhật sản phẩm này',
  })
  async updateCompleteDigitalProduct(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: CompleteUpdateDigitalProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.completeDigitalProductService.updateCompleteDigitalProduct(
      id,
      dto,
      userId,
    );

    return ApiResponseDto.success<CompleteDigitalProductResponseDto>(
      result,
      'Cập nhật sản phẩm số thành công',
    );
  }

  /**
   * Lấy chi tiết hoàn chỉnh digital product
   */
  @Get(':id/complete')
  @ApiOperation({
    summary: 'Lấy chi tiết hoàn chỉnh sản phẩm số',
    description: `
    Lấy toàn bộ thông tin chi tiết của sản phẩm số bao gồm:
    - Thông tin cơ bản từ customer_products
    - Thông tin số hóa từ digital_products
    - Danh sách phiên bản từ digital_product_versions
    - Hình ảnh product level và version level
    - Custom fields
    
    API này không trả về upload URLs vì chỉ dùng để xem thông tin.
    `,
  })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm số cần lấy chi tiết',
    type: 'integer',
    example: 123,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy chi tiết sản phẩm số thành công',
    type: CompleteDigitalProductResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm hoặc không có quyền truy cập',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền xem sản phẩm này',
  })
  async getCompleteDigitalProduct(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.completeDigitalProductService.getCompleteDigitalProduct(
      id,
      userId,
    );

    return ApiResponseDto.success<CompleteDigitalProductResponseDto>(
      result,
      'Lấy chi tiết sản phẩm số thành công',
    );
  }
}
