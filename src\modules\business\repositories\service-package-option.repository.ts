import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ServicePackageOption } from '@modules/business/entities';

/**
 * Repository cho ServicePackageOption entity
 * Xử lý các thao tác database cho gói dịch vụ
 */
@Injectable()
export class ServicePackageOptionRepository {
  private readonly logger = new Logger(ServicePackageOptionRepository.name);

  constructor(
    @InjectRepository(ServicePackageOption)
    private readonly repository: Repository<ServicePackageOption>,
  ) {}

  /**
   * Tạo gói dịch vụ mới
   * @param data Dữ liệu gói dịch vụ
   * @returns Gói dịch vụ đã tạo
   */
  async create(data: Partial<ServicePackageOption>): Promise<ServicePackageOption> {
    const packageOption = this.repository.create({
      ...data,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    return this.repository.save(packageOption);
  }

  /**
   * Tìm gói dịch vụ theo ID
   * @param id ID gói dịch vụ
   * @returns Gói dịch vụ hoặc null
   */
  async findById(id: number): Promise<ServicePackageOption | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm tất cả gói dịch vụ theo service product ID
   * @param serviceProductsId ID sản phẩm dịch vụ
   * @returns Danh sách gói dịch vụ
   */
  async findByServiceProductId(serviceProductsId: number): Promise<ServicePackageOption[]> {
    return this.repository.find({
      where: { serviceProductsId },
      order: { createdAt: 'ASC' },
    });
  }

  /**
   * Cập nhật gói dịch vụ
   * @param id ID gói dịch vụ
   * @param data Dữ liệu cập nhật
   * @returns Gói dịch vụ đã cập nhật
   */
  async update(id: number, data: Partial<ServicePackageOption>): Promise<ServicePackageOption | null> {
    await this.repository.update(id, {
      ...data,
      updatedAt: Date.now(),
    });
    return this.findById(id);
  }

  /**
   * Xóa gói dịch vụ
   * @param id ID gói dịch vụ
   */
  async delete(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Kiểm tra gói dịch vụ có tồn tại không
   * @param id ID gói dịch vụ
   * @returns true nếu tồn tại
   */
  async exists(id: number): Promise<boolean> {
    const count = await this.repository.count({
      where: { id },
    });
    return count > 0;
  }

  /**
   * Đếm tổng số gói dịch vụ
   * @returns Số lượng gói dịch vụ
   */
  async count(): Promise<number> {
    return this.repository.count();
  }
}
