import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsNumber,
  IsString,
  IsUUID,
  Min,
  ValidateIf,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo liên kết media với entity
 */
export class CreateEntityHasMediaDto {
  @ApiProperty({
    description: 'ID của media',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  mediaId: string;

  @ApiProperty({
    description: 'ID sản phẩm (nếu liên kết với sản phẩm)',
    example: 456,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => !o.physicalVarial && !o.ticketVarial && !o.versionId && !o.productComboId && !o.productPlanVarialId)
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  productId?: number;

  @ApiProperty({
    description: 'ID biến thể vật lý (nếu liên kết với biến thể vật lý)',
    example: 789,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => !o.productId && !o.ticketVarial && !o.versionId && !o.productComboId && !o.productPlanVarialId)
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  physicalVarial?: number;

  @ApiProperty({
    description: 'ID biến thể vé (nếu liên kết với biến thể vé)',
    example: 101,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => !o.productId && !o.physicalVarial && !o.versionId && !o.productComboId && !o.productPlanVarialId)
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  ticketVarial?: number;

  @ApiProperty({
    description: 'ID phiên bản (nếu liên kết với phiên bản)',
    example: 202,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => !o.productId && !o.physicalVarial && !o.ticketVarial && !o.productComboId && !o.productPlanVarialId)
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  versionId?: number;

  @ApiProperty({
    description: 'ID combo sản phẩm (nếu liên kết với combo)',
    example: 303,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => !o.productId && !o.physicalVarial && !o.ticketVarial && !o.versionId && !o.productPlanVarialId)
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  productComboId?: number;

  @ApiProperty({
    description: 'ID gói dịch vụ (nếu liên kết với gói dịch vụ)',
    example: 404,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => !o.productId && !o.physicalVarial && !o.ticketVarial && !o.versionId && !o.productComboId)
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  productPlanVarialId?: number;
}
