import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsArray,
  ValidateNested,
  IsObject,
  IsEnum,
  MaxLength,
  ValidationArguments,
  registerDecorator,
  ValidationOptions,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  PriceTypeEnum,
  ProductTypeEnum,
} from '@modules/business/enums';
import { CustomFieldInputDto } from '../custom-field-metadata.dto';
import { HasPriceDto, StringPriceDto } from '../price.dto';
import { ServicePackageOperationDto } from './service-package-operation.dto';

/**
 * Custom validator để kiểm tra salePrice <= listPrice cho price object
 */
export function IsValidPriceObject(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isValidPriceObject',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!value || typeof value !== 'object') return true;

          const { listPrice, salePrice } = value;
          if (typeof listPrice === 'number' && typeof salePrice === 'number') {
            return salePrice <= listPrice;
          }
          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return 'Giá bán không được lớn hơn giá niêm yết';
        },
      },
    });
  };
}

/**
 * DTO cho thông tin cơ bản sản phẩm
 */
export class BasicInfoDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Dịch vụ chăm sóc sức khỏe toàn diện',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Dịch vụ chăm sóc sức khỏe với đội ngũ chuyên gia hàng đầu',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.SERVICE,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductTypeEnum)
  productType?: ProductTypeEnum;

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: [String],
    example: ['chăm sóc sức khỏe', 'spa', 'massage', 'thư giãn'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * DTO cho thông tin giá cả
 */
export class PricingInfoDto {
  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    oneOf: [
      { $ref: '#/components/schemas/HasPriceDto' },
      { $ref: '#/components/schemas/StringPriceDto' }
    ],
    example: {
      listPrice: 1000000,
      salePrice: 850000,
      currency: 'VND'
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  @IsValidPriceObject({
    message: 'Giá bán không được lớn hơn giá niêm yết',
  })
  price?: HasPriceDto | StringPriceDto;

  @ApiProperty({
    description: 'Kiểu giá sản phẩm',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
  })
  @IsOptional()
  @IsEnum(PriceTypeEnum)
  typePrice?: PriceTypeEnum;
}

/**
 * DTO cho thông tin dịch vụ
 */
export class ServiceInfoDto {
  @ApiProperty({
    description: 'Loại hình dịch vụ',
    example: 'spa',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  serviceType?: string;

  @ApiProperty({
    description: 'Địa điểm thực hiện dịch vụ',
    example: 'tại cửa hàng',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  location?: string;

  @ApiProperty({
    description: 'Tên người thực hiện hoặc tổ chức cung cấp dịch vụ',
    example: 'Spa Luxury Beauty',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  providerName?: string;
}

/**
 * DTO cho thao tác với ảnh sản phẩm
 */
export class ImageOperationDto {
  @ApiProperty({
    description: 'Loại thao tác với ảnh',
    enum: ['add', 'delete'],
    example: 'add',
  })
  @IsEnum(['add', 'delete'])
  operation: 'add' | 'delete';

  @ApiProperty({
    description: 'ID media (cho thao tác add)',
    example: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2',
    required: false,
  })
  @IsOptional()
  @IsString()
  mediaId?: string;

  @ApiProperty({
    description: 'ID của entity_has_media record cần xóa (cho thao tác delete)',
    example: 123,
    required: false,
  })
  @IsOptional()
  entityHasMediaId?: number;
}

/**
 * DTO cho các thao tác với dịch vụ
 */
export class ServiceOperationsDto {
  @ApiProperty({
    description: 'Danh sách thao tác với gói dịch vụ (ADD/UPDATE/DELETE)',
    type: [ServicePackageOperationDto],
    example: [
      {
        operation: 'add',
        data: {
          name: 'Gói chăm sóc da cao cấp',
          description: 'Gói chăm sóc da toàn diện với các liệu pháp hiện đại',
          price: {
            listPrice: 1000000,
            salePrice: 850000,
            currency: 'VND'
          },
          duration: 90,
          unit: 'phút',
          features: ['Massage thư giãn', 'Đắp mặt nạ collagen'],
          isActive: true,
          isLimited: false,
          imageOperations: [
            {
              operation: 'add',
              mediaId: 'dcc8f803-04fc-49b3-953a-e660505ac353',
            }
          ]
        }
      },
      {
        operation: 'update',
        id: 123,
        data: {
          name: 'Gói chăm sóc da cao cấp - Cập nhật',
          price: {
            listPrice: 1200000,
            salePrice: 1000000,
            currency: 'VND'
          },
          imageOperations: [
            {
              operation: 'delete',
              entityHasMediaId: 456
            }
          ]
        }
      },
      {
        operation: 'delete',
        id: 124
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ServicePackageOperationDto)
  packages?: ServicePackageOperationDto[];

  @ApiProperty({
    description: 'Danh sách thao tác với ảnh sản phẩm (ADD/DELETE)',
    type: [ImageOperationDto],
    example: [
      {
        operation: 'add',
        mediaId: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2',
      },
      {
        operation: 'delete',
        entityHasMediaId: 789
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ImageOperationDto)
  images?: ImageOperationDto[];
}

/**
 * DTO hoàn chỉnh cho việc cập nhật sản phẩm dịch vụ
 * Sử dụng cấu trúc nhóm theo chức năng để dễ đọc và maintain
 * Bao gồm tất cả thuộc tính từ:
 * - customer_products table (thông qua basicInfo và pricing)
 * - service_products table (thông qua serviceInfo)
 * - service_packages_option table (thông qua operations.packages)
 * - entity_has_media table (thông qua operations.images)
 */
export class CompleteUpdateServiceProductDto {
  // ========== THÔNG TIN CƠ BẢN ==========
  @ApiProperty({
    description: 'Thông tin cơ bản sản phẩm (tên, mô tả, loại, tags)',
    type: BasicInfoDto,
    example: {
      name: 'Dịch vụ chăm sóc sức khỏe toàn diện - Phiên bản mới',
      description: 'Dịch vụ chăm sóc sức khỏe với đội ngũ chuyên gia hàng đầu và thiết bị hiện đại',
      productType: 'SERVICE',
      tags: ['chăm sóc sức khỏe', 'spa', 'massage', 'thư giãn']
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => BasicInfoDto)
  basicInfo?: BasicInfoDto;

  // ========== THÔNG TIN GIÁ CẢ ==========
  @ApiProperty({
    description: 'Thông tin giá cả sản phẩm',
    type: PricingInfoDto,
    examples: {
      hasPrice: {
        summary: 'Sản phẩm có giá cụ thể',
        value: {
          price: {
            listPrice: 1000000,
            salePrice: 850000,
            currency: 'VND'
          },
          typePrice: 'HAS_PRICE'
        }
      },
      stringPrice: {
        summary: 'Sản phẩm có mô tả giá',
        value: {
          price: {
            priceDescription: 'Giá sẽ được tư vấn theo nhu cầu'
          },
          typePrice: 'STRING_PRICE'
        }
      }
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PricingInfoDto)
  pricing?: PricingInfoDto;

  // ========== THÔNG TIN DỊCH VỤ ==========
  @ApiProperty({
    description: 'Thông tin dịch vụ (loại hình, địa điểm, nhà cung cấp)',
    type: ServiceInfoDto,
    example: {
      serviceType: 'spa',
      location: 'tại cửa hàng',
      providerName: 'Spa Luxury Beauty'
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ServiceInfoDto)
  serviceInfo?: ServiceInfoDto;

  // ========== CUSTOM FIELDS ==========
  @ApiProperty({
    description: 'Danh sách custom fields',
    type: [CustomFieldInputDto],
    example: [
      {
        customFieldId: 1,
        value: { value: 'Dịch vụ cao cấp' }
      },
      {
        customFieldId: 2,
        value: { value: 'Có chứng chỉ quốc tế' }
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];

  // ========== THAO TÁC ==========
  @ApiProperty({
    description: 'Các thao tác với gói dịch vụ và hình ảnh sản phẩm',
    type: ServiceOperationsDto,
    example: {
      packages: [
        {
          operation: 'add',
          data: {
            name: 'Gói chăm sóc da cao cấp',
            description: 'Gói chăm sóc da toàn diện với các liệu pháp hiện đại',
            price: {
              listPrice: 1000000,
              salePrice: 850000,
              currency: 'VND'
            },
            duration: 90,
            unit: 'phút',
            features: ['Massage thư giãn', 'Đắp mặt nạ collagen', 'Tư vấn chuyên sâu'],
            isActive: true,
            isLimited: false,
            imageOperations: [
              {
                operation: 'add',
                mediaId: 'dcc8f803-04fc-49b3-953a-e660505ac353'
              }
            ]
          }
        },
        {
          operation: 'update',
          id: 123,
          data: {
            name: 'Gói chăm sóc da cao cấp - Cập nhật',
            price: {
              listPrice: 1200000,
              salePrice: 1000000,
              currency: 'VND'
            },
            imageOperations: [
              {
                operation: 'delete',
                entityHasMediaId: 456
              }
            ]
          }
        },
        {
          operation: 'delete',
          id: 124
        }
      ],
      images: [
        {
          operation: 'add',
          mediaId: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2'
        },
        {
          operation: 'delete',
          entityHasMediaId: 789
        }
      ]
    },
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ServiceOperationsDto)
  operations?: ServiceOperationsDto;
}
