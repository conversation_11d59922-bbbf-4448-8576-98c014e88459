# 🔑 RAG API Key Provisioning Service Rules

> Quy tắc sử dụng và phát triển RagApiKeyProvisioningService để tự động tạo RAG API key cho user mới

## 📋 Tổng quan

`RagApiKeyProvisioningService` là service chuyên biệt để tự động tạo RAG API key cho user mới đăng ký, đả<PERSON> bảo mỗi user có API key riêng để sử dụng các tính năng RAG mà không cần biết về sự tồn tại của hệ thống này.

## 🏗️ Cấu trúc Service

### 1. Dependencies Required

```typescript
// Required repositories
private readonly userRagApiKeyRepository: UserRagApiKeyRepository

// Required services  
private readonly configService: ConfigService
```

### 2. Environment Variables

```env
# Required - RAG API base URL
FAST_API_URL=http://localhost:8000

# Required - Admin API key để tạo user keys
RAG_API_ADMIN_KEY=redai-f02fdd9b-b4f8-4fd5-bcc1-a39024c689fa
```

## 🔧 Method Usage Rules

### 1. Main Provisioning Method

```typescript
async provisionApiKeyForNewUser(userId: number, userEmail: string): Promise<void>
```

**✅ Khi nào sử dụng:**
- Sau khi user đăng ký thành công
- Trong transaction hoặc event handler
- Background job để provision cho existing users

**✅ Best Practices:**
```typescript
// ✅ Correct - Gọi sau khi user được tạo thành công
const user = await this.userService.createUser(createUserDto);
await this.ragApiKeyProvisioningService.provisionApiKeyForNewUser(user.id, user.email);

// ✅ Correct - Trong transaction
@Transactional()
async registerUser(dto: RegisterDto) {
  const user = await this.userRepository.save(newUser);
  await this.ragApiKeyProvisioningService.provisionApiKeyForNewUser(user.id, user.email);
  return user;
}
```

**❌ Tránh:**
```typescript
// ❌ Wrong - Không gọi trước khi user được tạo
await this.ragApiKeyProvisioningService.provisionApiKeyForNewUser(userId, email);
const user = await this.userService.createUser(createUserDto);

// ❌ Wrong - Không handle trong try-catch riêng (service đã handle internally)
try {
  await this.ragApiKeyProvisioningService.provisionApiKeyForNewUser(userId, email);
} catch (error) {
  // Service đã handle error internally, không cần try-catch
}
```

### 2. Retry Method

```typescript
async retryProvisionApiKey(userId: number, userEmail: string, maxRetries: number = 3): Promise<boolean>
```

**✅ Khi nào sử dụng:**
- Background jobs để retry failed provisions
- Admin tools để manually provision
- Batch processing existing users

**✅ Best Practices:**
```typescript
// ✅ Correct - Background job
@Cron('0 */6 * * *') // Every 6 hours
async retryFailedProvisions() {
  const usersWithoutKeys = await this.getUsersWithoutRagKeys();
  
  for (const user of usersWithoutKeys) {
    const success = await this.ragApiKeyProvisioningService.retryProvisionApiKey(
      user.id, 
      user.email, 
      3
    );
    
    if (!success) {
      this.logger.error(`Failed to provision RAG key for user ${user.id} after retries`);
    }
  }
}
```

### 3. Utility Methods

```typescript
// Kiểm tra user đã có key chưa
async hasApiKey(userId: number): Promise<boolean>

// Debug configuration
getConfiguration(): { baseUrl: string; hasAdminKey: boolean }
```

## 🔄 Integration Patterns

### 1. Event-Driven Integration (Recommended)

```typescript
// User service
@Injectable()
export class UserService {
  async createUser(dto: CreateUserDto): Promise<User> {
    const user = await this.userRepository.save(newUser);
    
    // Emit event
    this.eventEmitter.emit('user.created', { userId: user.id, email: user.email });
    
    return user;
  }
}

// Event listener
@Injectable()
export class UserEventListener {
  @OnEvent('user.created')
  async handleUserCreated(payload: { userId: number; email: string }) {
    await this.ragApiKeyProvisioningService.provisionApiKeyForNewUser(
      payload.userId, 
      payload.email
    );
  }
}
```

### 2. Direct Integration

```typescript
// User registration service
@Injectable()
export class AuthService {
  async register(dto: RegisterDto): Promise<User> {
    const user = await this.userService.createUser(dto);
    
    // Provision RAG API key
    await this.ragApiKeyProvisioningService.provisionApiKeyForNewUser(user.id, user.email);
    
    return user;
  }
}
```

### 3. Background Job Integration

```typescript
@Injectable()
export class RagProvisioningJob {
  @Cron('0 2 * * *') // Daily at 2 AM
  async provisionMissingKeys() {
    const users = await this.userRepository.find({
      select: ['id', 'email'],
      where: { /* users without RAG keys */ }
    });

    for (const user of users) {
      await this.ragApiKeyProvisioningService.provisionApiKeyForNewUser(user.id, user.email);
    }
  }
}
```

## 🛡️ Error Handling Rules

### 1. Service Level Error Handling

**✅ Service tự handle errors:**
- Không throw error để không block user registration
- Log errors với appropriate level
- Return gracefully

```typescript
// Service internally handles errors
try {
  await this.createRagApiKey(userId, userEmail);
} catch (error) {
  this.logger.error(`RAG API key creation failed: ${error.message}`);
  // Không throw - để user registration tiếp tục
}
```

### 2. Consumer Level Error Handling

**✅ Consumers không cần try-catch:**
```typescript
// ✅ Correct - Service handles errors internally
await this.ragApiKeyProvisioningService.provisionApiKeyForNewUser(userId, email);

// ❌ Wrong - Unnecessary try-catch
try {
  await this.ragApiKeyProvisioningService.provisionApiKeyForNewUser(userId, email);
} catch (error) {
  // Service đã handle internally
}
```

**✅ Chỉ handle khi cần biết kết quả:**
```typescript
// ✅ Correct - Khi cần biết success/failure
const success = await this.ragApiKeyProvisioningService.retryProvisionApiKey(userId, email);
if (!success) {
  // Handle failure case
}
```

## 📊 Logging Rules

### 1. Log Levels

```typescript
// ✅ Correct logging levels
this.logger.log('Normal operations');           // INFO level
this.logger.debug('Detailed debug info');       // DEBUG level  
this.logger.warn('Non-critical issues');        // WARN level
this.logger.error('Critical errors');           // ERROR level
```

### 2. Log Content Rules

**✅ Log phải bao gồm:**
- User ID trong mọi log message
- Partial API key (first 10 chars) cho security
- Request/response details ở DEBUG level
- Error stack trace ở ERROR level

**❌ Không log:**
- Full API keys
- Sensitive user data
- RAG API admin key

## 🔒 Security Rules

### 1. API Key Handling

```typescript
// ✅ Correct - Log partial key only
this.logger.log(`Created key: ${apiKey.substring(0, 10)}...`);

// ❌ Wrong - Never log full key
this.logger.log(`Created key: ${apiKey}`);
```

### 2. Configuration Security

```typescript
// ✅ Correct - Check admin key availability
if (!this.ragApiAdminKey) {
  this.logger.warn('RAG_API_ADMIN_KEY not configured');
}

// ✅ Correct - Validate response
if (!responseData.key || responseData.api_metadata.user_id !== userId) {
  throw new Error('Invalid response from RAG API');
}
```

## 🧪 Testing Rules

### 1. Unit Tests Required

```typescript
describe('RagApiKeyProvisioningService', () => {
  it('should create API key for new user');
  it('should skip if user already has key');
  it('should handle RAG API errors gracefully');
  it('should retry with exponential backoff');
  it('should validate response structure');
});
```

### 2. Integration Tests

```typescript
describe('RAG API Integration', () => {
  it('should successfully call RAG API with correct payload');
  it('should handle RAG API unavailable');
  it('should save key to database after creation');
});
```

## 📋 Checklist

### ✅ Implementation Checklist

- [ ] Service implements all required methods
- [ ] Environment variables configured
- [ ] UserRagApiKeyRepository injected
- [ ] Error handling implemented (no throws in main method)
- [ ] Logging implemented with appropriate levels
- [ ] Security rules followed (no full key logging)
- [ ] Retry mechanism with exponential backoff
- [ ] Response validation implemented

### ✅ Integration Checklist

- [ ] Service registered in appropriate module
- [ ] Dependencies properly injected
- [ ] Integration point identified (event/direct/background)
- [ ] Error handling at consumer level (if needed)
- [ ] Monitoring/alerting configured
- [ ] Unit tests written
- [ ] Integration tests written

### ✅ Deployment Checklist

- [ ] RAG_API_ADMIN_KEY configured in environment
- [ ] FAST_API_URL configured correctly
- [ ] Database migration for user_rag_api_key table
- [ ] Monitoring dashboards updated
- [ ] Documentation updated
- [ ] Rollback plan prepared

## 🚨 Common Pitfalls

1. **❌ Throwing errors in main provision method** - Sẽ block user registration
2. **❌ Not checking existing keys** - Tạo duplicate keys
3. **❌ Logging full API keys** - Security risk
4. **❌ Not validating RAG API response** - Có thể lưu invalid data
5. **❌ Synchronous processing** - Làm chậm user registration
6. **❌ Not handling RAG API unavailable** - Service failure
7. **❌ Hard-coding configuration** - Không flexible
8. **❌ Not implementing retry mechanism** - Miss failed provisions

> **Lưu ý:** Service này được thiết kế để hoạt động ngầm, user không biết về sự tồn tại của RAG API key. Đảm bảo không expose thông tin này trong UI hoặc API responses.
