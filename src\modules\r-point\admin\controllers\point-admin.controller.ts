import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags, getSchemaPath } from '@nestjs/swagger';
import { PointAdminService } from '../services/point-admin.service';
import { CreatePointDto, PointFilterDto, PointResponseDto, UpdatePointDto } from '../dto';
import { Point } from '@modules/r-point/entities';
import { AppException, ErrorCode } from '@/common';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';

@ApiTags(SWAGGER_API_TAGS.R_POINT_ADMIN_POINTS)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtEmployeeGuard)
@Controller('admin/r-point/points')
export class PointAdminController {
  constructor(private readonly pointAdminService: PointAdminService) {}

  /**
   * Tạo mới gói point thường (không phải customize)
   * @param createPointDto Thông tin gói point cần tạo (tên, số tiền, số point, mô tả)
   * @returns Gói point đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới gói point thường (không phải customize)' })
  @ApiBody({ type: CreatePointDto })
  @ApiResponse({ status: 201, description: 'Tạo mới gói point thành công', type: PointResponseDto })
  @ApiResponse({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ' })
  @ApiResponse({ status: 401, description: 'Chưa đăng nhập' })
  @ApiResponse({ status: 403, description: 'Không có quyền truy cập' })
  async create(@Body() createPointDto: CreatePointDto): Promise<PointResponseDto> {
    const point = await this.pointAdminService.create(createPointDto);
    return this.mapPointToResponseDto(point);
  }

  /**
   * Lấy danh sách gói point với phân trang và lọc
   * @param filterDto Thông tin lọc và phân trang
   * @returns Danh sách gói point và thông tin phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách gói point' })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng item trên mỗi trang', type: Number })
  @ApiQuery({ name: 'search', required: false, description: 'Tìm kiếm theo tên', type: String })
  @ApiQuery({ name: 'isCustomize', required: false, description: 'Lọc theo loại gói (customize hoặc không)', type: Boolean })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sắp xếp theo trường', enum: ['id', 'name', 'cash', 'point', 'rate'] })
  @ApiQuery({ name: 'sortOrder', required: false, description: 'Thứ tự sắp xếp', enum: ['ASC', 'DESC'] })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách gói point thành công',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Lấy danh sách gói point thành công' },
        result: {
          type: 'object',
          properties: {
            items: {
              type: 'array',
              items: { $ref: getSchemaPath(PointResponseDto) }
            },
            meta: {
              type: 'object',
              properties: {
                totalItems: { type: 'number', example: 100 },
                itemCount: { type: 'number', example: 10 },
                itemsPerPage: { type: 'number', example: 10 },
                totalPages: { type: 'number', example: 10 },
                currentPage: { type: 'number', example: 1 }
              }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Chưa đăng nhập' })
  @ApiResponse({ status: 403, description: 'Không có quyền truy cập' })
  async findAll(@Query() filterDto: PointFilterDto): Promise<ApiResponseDto<PaginatedResult<PointResponseDto>>> {
    const { items, total, page, limit } = await this.pointAdminService.findAll(filterDto);

    // Tính toán thông tin phân trang
    const totalPages = Math.ceil(total / limit);

    // Tạo đối tượng PaginatedResult
    const paginatedResult: PaginatedResult<PointResponseDto> = {
      items: items.map(point => this.mapPointToResponseDto(point)),
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: totalPages,
        currentPage: page
      }
    };

    // Trả về ApiResponseDto với PaginatedResult
    return ApiResponseDto.success(
      paginatedResult,
      'Lấy danh sách gói point thành công'
    );
  }

  /**
   * Lấy thông tin chi tiết của một gói point
   * @param id ID của gói point
   * @returns Thông tin chi tiết gói point
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết gói point' })
  @ApiParam({ name: 'id', description: 'ID của gói point', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin gói point thành công',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Lấy thông tin gói point thành công' },
        result: { $ref: getSchemaPath(PointResponseDto) }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Chưa đăng nhập' })
  @ApiResponse({ status: 403, description: 'Không có quyền truy cập' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy gói point' })
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<ApiResponseDto<PointResponseDto>> {
    const point = await this.pointAdminService.findOne(id);
    return ApiResponseDto.success(
      this.mapPointToResponseDto(point),
      'Lấy thông tin gói point thành công'
    );
  }

  /**
   * Cập nhật thông tin gói point
   * @param id ID của gói point
   * @param updatePointDto Thông tin cần cập nhật
   * @returns Gói point đã cập nhật
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật thông tin gói point' })
  @ApiParam({ name: 'id', description: 'ID của gói point', type: Number })
  @ApiBody({ type: UpdatePointDto })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật gói point thành công',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Cập nhật gói point thành công' },
        result: { $ref: getSchemaPath(PointResponseDto) }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ' })
  @ApiResponse({ status: 401, description: 'Chưa đăng nhập' })
  @ApiResponse({ status: 403, description: 'Không có quyền truy cập' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy gói point' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePointDto: UpdatePointDto,
  ): Promise<ApiResponseDto<PointResponseDto>> {
    // Nếu cập nhật thành gói CUSTOMIZE, cần có min, max và rate
    if (updatePointDto.isCustomize === true) {
      if (!updatePointDto.min || !updatePointDto.max || !updatePointDto.rate) {
        throw new AppException(ErrorCode.INVALID_POINT_DATA, 'Gói CUSTOMIZE phải có giá trị tối thiểu (min), tối đa (max) và tỷ lệ quy đổi (rate)');
      }
    }

    const point = await this.pointAdminService.update(id, updatePointDto);
    return ApiResponseDto.success(
      this.mapPointToResponseDto(point),
      'Cập nhật gói point thành công'
    );
  }

  /**
   * Xóa gói point
   * @param id ID của gói point
   * @returns Thông báo xóa thành công
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa gói point' })
  @ApiParam({ name: 'id', description: 'ID của gói point', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Xóa gói point thành công',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Xóa gói point thành công' },
        result: {
          type: 'object',
          properties: {
            message: { type: 'string', example: 'Đã xóa gói point với ID 1' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Chưa đăng nhập' })
  @ApiResponse({ status: 403, description: 'Không có quyền truy cập' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy gói point' })
  async remove(@Param('id', ParseIntPipe) id: number): Promise<ApiResponseDto<{ message: string }>> {
    const result = await this.pointAdminService.remove(id);
    return ApiResponseDto.success(result, 'Xóa gói point thành công');
  }

  /**
   * Xóa nhiều gói point
   * @param ids Danh sách ID của các gói point cần xóa
   * @returns Thông báo xóa thành công
   */
  @Delete('bulk')
  @ApiOperation({ summary: 'Xóa nhiều gói point' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        ids: {
          type: 'array',
          items: {
            type: 'string',
            format: 'uuid'
          },
          description: 'Danh sách ID của các gói point cần xóa'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa các gói point thành công',
    schema: {
      allOf: [
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Xóa các gói point thành công' },
            result: { type: 'boolean', example: true }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async bulkDelete(@Body('ids') ids: string[]): Promise<ApiResponseDto<boolean>> {
    await this.pointAdminService.bulkDelete(ids);
    return ApiResponseDto.success(true, 'Xóa các gói point thành công');
  }

  /**
   * Chuyển đổi từ entity Point sang DTO PointResponseDto
   * @param point Entity Point
   * @returns DTO PointResponseDto
   */
  private mapPointToResponseDto(point: Point): PointResponseDto {
    return {
      id: point.id,
      name: point.name,
      cash: point.cash,
      rate: point.rate,
      min: point.min,
      max: point.max,
      point: point.point,
      isCustomize: point.isCustomize,
      description: point.description,
    };
  }
}
