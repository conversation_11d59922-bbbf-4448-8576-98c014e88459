import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository, In } from 'typeorm';
import { ZaloOfficialAccount } from '@modules/marketing/user';

/**
 * Repository cho ZaloOfficialAccount
 */
@Injectable()
export class ZaloOfficialAccountRepository {
  constructor(
    @InjectRepository(ZaloOfficialAccount)
    private readonly repository: Repository<ZaloOfficialAccount>,
  ) {}

  /**
   * Cập nhật agentId cho nhiều Official Account
   * @param agentId ID của agent
   * @param oaIds Danh sách ID của Official Accounts
   */
  async updateAgentId(
    agentId: string,
    oaIds: number[],
  ) {
    await this.repository.createQueryBuilder()
      .update(ZaloOfficialAccount)
      .set({ agentId: agentId })
      // .where('agentId = :agentId', { agentId })
      .andWhere('id IN (:...oaIds)', { oaIds })
      .execute();
  }

  /**
   * Tìm kiếm nhiều Official Account
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách Official Account
   */
  async find(
    options?: FindManyOptions<ZaloOfficialAccount>,
  ): Promise<ZaloOfficialAccount[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một Official Account
   * @param options Tùy chọn tìm kiếm
   * @returns Official Account hoặc null
   */
  async findOne(
    options?: FindOneOptions<ZaloOfficialAccount>,
  ): Promise<ZaloOfficialAccount | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Tìm Official Account theo ID
   * @param id ID của Official Account
   * @returns Official Account hoặc null
   */
  async findById(id: number): Promise<ZaloOfficialAccount | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm Official Account theo ID người dùng và ID Official Account
   * @param userId ID của người dùng
   * @param oaId ID của Official Account trên Zalo
   * @returns Official Account hoặc null
   */
  async findByUserIdAndOaId(
    userId: number,
    oaId: string,
  ): Promise<ZaloOfficialAccount | null> {
    return this.repository.findOne({ where: { userId, oaId } });
  }

  /**
   * Tìm Official Account theo oaId (không cần userId)
   * @param oaId ID của Official Account trên Zalo
   * @returns Official Account hoặc null
   */
  async findByOaId(oaId: string): Promise<ZaloOfficialAccount | null> {
    return this.repository.findOne({
      where: { oaId, status: 'active' },
      order: { updatedAt: 'DESC' } // Lấy bản ghi mới nhất nếu có nhiều
    });
  }

  /**
   * Tìm tất cả Official Account của một người dùng
   * @param userId ID của người dùng
   * @returns Danh sách Official Account
   */
  async findByUserId(userId: number): Promise<ZaloOfficialAccount[]> {
    return this.repository.find({ where: { userId } });
  }

  /**
   * Tạo mới Official Account
   * @param data Dữ liệu Official Account
   * @returns Official Account đã tạo
   */
  async create(
    data: Partial<ZaloOfficialAccount>,
  ): Promise<ZaloOfficialAccount> {
    const officialAccount = this.repository.create(data);
    return this.repository.save(officialAccount);
  }

  /**
   * Cập nhật Official Account
   * @param id ID của Official Account
   * @param data Dữ liệu cập nhật
   * @returns Official Account đã cập nhật
   */
  async update(
    id: number,
    data: Partial<ZaloOfficialAccount>,
  ): Promise<ZaloOfficialAccount | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Cập nhật Official Account theo ID người dùng và ID Official Account
   * @param userId ID của người dùng
   * @param oaId ID của Official Account trên Zalo
   * @param data Dữ liệu cập nhật
   * @returns Official Account đã cập nhật
   */
  async updateByUserIdAndOaId(
    userId: number,
    oaId: string,
    data: Partial<ZaloOfficialAccount>,
  ): Promise<ZaloOfficialAccount | null> {
    await this.repository.update({ userId, oaId }, data);
    return this.findByUserIdAndOaId(userId, oaId);
  }

  /**
   * Xóa Official Account
   * @param id ID của Official Account
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }

  /**
   * Xóa Official Account theo ID người dùng và ID Official Account
   * @param userId ID của người dùng
   * @param oaId ID của Official Account trên Zalo
   * @returns true nếu xóa thành công
   */
  async deleteByUserIdAndOaId(userId: number, oaId: string): Promise<boolean> {
    const result = await this.repository.delete({ userId, oaId });
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }

  /**
   * Đếm số lượng Official Account
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng Official Account
   */
  async count(options?: FindManyOptions<ZaloOfficialAccount>): Promise<number> {
    return this.repository.count(options);
  }

  /**
   * Tìm tất cả Official Account được gán cho một agent
   * @param agentId ID của agent
   * @param userId ID của người dùng (để đảm bảo quyền sở hữu)
   * @returns Danh sách Official Account
   */
  async findByAgentId(
    agentId: string,
    userId: number,
  ): Promise<ZaloOfficialAccount[]> {
    return this.repository.find({
      where: {
        agentId,
        userId,
      },
      order: {
        updatedAt: 'DESC',
      },
    });
  }

  /**
   * Tìm Official Account được gán cho agent với phân trang và tìm kiếm
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param page Số trang
   * @param limit Số lượng per page
   * @param search Từ khóa tìm kiếm
   * @param status Trạng thái lọc (optional)
   * @returns Danh sách Official Account có phân trang
   */
  async findByAgentIdPaginated(
    agentId: string,
    userId: number,
    page: number = 1,
    limit: number = 10,
    search?: string,
    status?: string,
  ): Promise<{ items: ZaloOfficialAccount[]; total: number }> {
    const queryBuilder = this.repository.createQueryBuilder('oa');

    // Điều kiện cơ bản
    queryBuilder
      .where('oa.agentId = :agentId', { agentId })
      .andWhere('oa.userId = :userId', { userId });

    // Lọc theo trạng thái
    if (status) {
      queryBuilder.andWhere('oa.status = :status', { status });
    }

    // Tìm kiếm theo tên
    if (search) {
      queryBuilder.andWhere('oa.name ILIKE :search', { search: `%${search}%` });
    }

    // Sắp xếp
    queryBuilder.orderBy('oa.updatedAt', 'DESC');

    // Phân trang
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Thực hiện truy vấn
    const [items, total] = await queryBuilder.getManyAndCount();

    return { items, total };
  }

  /**
   * Bulk thêm nhiều Official Account vào agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param oaIds Danh sách ID của Official Accounts
   * @returns Kết quả bulk operation
   */
  async bulkAddToAgent(
    agentId: string,
    userId: number,
    oaIds: number[],
  ): Promise<{
    successCount: number;
    skippedCount: number;
    successIds: number[];
    skippedItems: Array<{ id: number; reason: string }>;
  }> {
    if (!oaIds || oaIds.length === 0) {
      return {
        successCount: 0,
        skippedCount: 0,
        successIds: [],
        skippedItems: [],
      };
    }

    // Lấy tất cả OAs trong một truy vấn duy nhất
    const oas = await this.repository.find({
      where: {
        id: In(oaIds),
        userId,
        status: 'active',
      },
    });

    // Tạo map để tra cứu nhanh
    const oaMap = new Map(oas.map(oa => [oa.id, oa]));

    const result = {
      successCount: 0,
      skippedCount: 0,
      successIds: [] as number[],
      skippedItems: [] as Array<{ id: number; reason: string }>,
    };

    const validOaIds: number[] = [];

    // Validate từng OA
    for (const oaId of oaIds) {
      const oa = oaMap.get(oaId);

      if (!oa) {
        result.skippedCount++;
        result.skippedItems.push({
          id: oaId,
          reason: 'Không tìm thấy hoặc không thuộc về user hoặc không active',
        });
        continue;
      }

      // Kiểm tra đã được gán cho agent khác chưa
      if (oa.agentId && oa.agentId !== agentId) {
        result.skippedCount++;
        result.skippedItems.push({
          id: oaId,
          reason: `Đã được gán cho agent ${oa.agentId}`,
        });
        continue;
      }

      // Nếu đã gán cho cùng agent thì skip
      if (oa.agentId === agentId) {
        result.skippedCount++;
        result.skippedItems.push({
          id: oaId,
          reason: 'Đã được gán cho agent này',
        });
        continue;
      }

      validOaIds.push(oaId);
    }

    // Batch update tất cả OAs hợp lệ
    if (validOaIds.length > 0) {
      const updateResult = await this.repository
        .createQueryBuilder()
        .update()
        .set({
          agentId,
          updatedAt: Date.now(),
        })
        .where('id IN (:...oaIds)', { oaIds: validOaIds })
        .andWhere('userId = :userId', { userId })
        .execute();

      result.successCount = updateResult.affected || 0;
      result.successIds = validOaIds.slice(0, result.successCount);
    }

    return result;
  }

  /**
   * Bulk gỡ nhiều Official Account khỏi agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param oaIds Danh sách ID của Official Accounts
   * @returns Kết quả bulk operation
   */
  async bulkRemoveFromAgent(
    agentId: string,
    userId: number,
    oaIds: number[],
  ): Promise<{
    successCount: number;
    skippedCount: number;
    successIds: number[];
    skippedItems: Array<{ id: number; reason: string }>;
  }> {
    const result = {
      successCount: 0,
      skippedCount: 0,
      successIds: [] as number[],
      skippedItems: [] as Array<{ id: number; reason: string }>,
    };

    for (const oaId of oaIds) {
      try {
        // Kiểm tra OA có tồn tại và thuộc về user không
        const oa = await this.repository.findOne({
          where: {
            id: oaId,
            userId,
          },
        });

        if (!oa) {
          result.skippedCount++;
          result.skippedItems.push({
            id: oaId,
            reason: 'Không tìm thấy hoặc không thuộc về user',
          });
          continue;
        }

        // Kiểm tra có được gán cho agent này không
        if (oa.agentId !== agentId) {
          result.skippedCount++;
          result.skippedItems.push({
            id: oaId,
            reason: oa.agentId
              ? `Được gán cho agent ${oa.agentId}`
              : 'Chưa được gán cho agent nào',
          });
          continue;
        }

        // Gỡ agent
        await this.repository.update(oaId, {
          agentId: null,
          updatedAt: Date.now(),
        });

        result.successCount++;
        result.successIds.push(oaId);
      } catch (error) {
        result.skippedCount++;
        result.skippedItems.push({
          id: oaId,
          reason: `Lỗi: ${error.message}`,
        });
      }
    }

    return result;
  }

  /**
   * Gỡ tất cả Official Account khỏi agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Số lượng đã gỡ
   */
  async removeAllFromAgent(agentId: string, userId: number): Promise<number> {
    const result = await this.repository.update(
      {
        agentId,
        userId,
      },
      {
        agentId: null,
        updatedAt: Date.now(),
      },
    );

    return result.affected || 0;
  }
}
