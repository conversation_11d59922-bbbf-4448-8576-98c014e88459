import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, Min, ValidateNested, IsObject } from 'class-validator';
import { Type } from 'class-transformer';
import { InvoiceInfoDto } from './invoice-info.dto';

/**
 * DTO cho việc mua R-Point
 */
export class PurchasePointDto {
  @ApiProperty({
    description: 'ID của gói R-Point',
    example: 1
  })
  @IsNumber()
  pointId: number;

  @ApiProperty({
    description: 'Số lượng R-Point muốn mua',
    example: 100
  })
  @IsNumber()
  @Min(1, { message: 'Số lượng R-Point phải lớn hơn 0' })
  pointAmount: number;

  @ApiProperty({
    description: 'Mã khuyến mãi (nếu có)',
    example: 'SUMMER2023',
    required: false
  })
  @IsOptional()
  @IsString()
  couponCode?: string;

  @ApiProperty({
    description: 'Thông tin hóa đơn (nếu có)',
    type: InvoiceInfoDto,
    required: false,
    example: {
      type: 'business',
      representativeName: 'Nguyễn Văn A',
      representativePosition: 'Giám đốc',
      companyName: 'Công ty TNHH ABC',
      companyAddress: '123 Đường ABC, Quận 1, TP.HCM',
      taxCode: '0123456789',
      email: '<EMAIL>'
    }
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => InvoiceInfoDto)
  invoiceInfo?: InvoiceInfoDto;
}
