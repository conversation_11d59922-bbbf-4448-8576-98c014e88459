import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * Response DTO cho phiên bản sản phẩm số
 * Bao gồm thông tin từ digital_product_versions + images
 */
export class DigitalProductVersionResponseDto {
  @ApiProperty({
    description: 'ID phiên bản',
    example: 123,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'ID sản phẩm số gốc',
    example: 456,
  })
  @Expose()
  digitalProductId: number;

  @ApiProperty({
    description: 'Tên phiên bản',
    example: 'Bản tiêu chuẩn',
  })
  @Expose()
  versionName: string;

  @ApiProperty({
    description: 'Mô tả chi tiết về phiên bản',
    example: 'Phiên bản cơ bản với đầy đủ tính năng chính',
    required: false,
  })
  @Expose()
  description?: string;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> trường tùy chỉnh cho phiên bản',
    example: {
      language: 'Tiếng Việt',
      format: 'PDF',
      pages: 100
    },
    required: false,
  })
  @Expose()
  customFields?: any;

  @ApiProperty({
    description: 'Liên kết nội dung sản phẩm số',
    example: 'https://example.com/download/product-v1.pdf',
    required: false,
  })
  @Expose()
  contentLink?: string;

  @ApiProperty({
    description: 'Mã SKU riêng của phiên bản',
    example: 'EBOOK-STD-V1',
    required: false,
  })
  @Expose()
  sku?: string;

  @ApiProperty({
    description: 'Mã vạch riêng của phiên bản',
    example: '1234567890123',
    required: false,
  })
  @Expose()
  barcode?: string;

  @ApiProperty({
    description: 'Số lượng tối thiểu mỗi lần mua',
    example: 1,
    required: false,
  })
  @Expose()
  minQuantity?: number;

  @ApiProperty({
    description: 'Số lượng tối đa mỗi lần mua',
    example: 10,
    required: false,
  })
  @Expose()
  maxQuantity?: number;

  @ApiProperty({
    description: 'Danh sách hình ảnh phiên bản (version level)',
    example: [
      {
        id: '123e4567-e89b-12d3-a456-426614174000',
        key: 'business/IMAGE/2025/06/version-image-1.jpg',
        url: 'https://cdn.redai.vn/business/IMAGE/2025/06/version-image-1.jpg?expires=1750214285&signature=abc123',
        position: 1,
        mimeType: 'image/jpeg',
        name: 'Version Image 1',
        size: '0'
      },
      {
        id: '456e7890-e89b-12d3-a456-426614174001',
        key: 'business/IMAGE/2025/06/version-image-2.jpg',
        url: 'https://cdn.redai.vn/business/IMAGE/2025/06/version-image-2.jpg?expires=1750214285&signature=def456',
        position: 2,
        mimeType: 'image/png',
        name: 'Version Image 2',
        size: '0'
      }
    ],
    required: false,
  })
  @Expose()
  images?: Array<{
    id: string;
    key: string;
    url: string;
    position: number;
    mimeType: string;
    name: string;
    size: string;
  }>;
}
