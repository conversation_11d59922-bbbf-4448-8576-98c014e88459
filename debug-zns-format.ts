/**
 * Script debug format ZNS template để tìm format đúng
 */

// Test các format component khác nhau
const testFormats = {
  // Format 1: Hiện tại đang dùng
  current: {
    template_name: "Test current format",
    template_type: 1,
    tag: "2",
    layout: {
      body: {
        components: [
          {
            TITLE: {
              value: "Test title"
            }
          }
        ]
      }
    },
    tracking_id: "test_current"
  },

  // Format 2: Lowercase component
  lowercase: {
    template_name: "Test lowercase format",
    template_type: 1,
    tag: "2",
    layout: {
      body: {
        components: [
          {
            title: {
              value: "Test title"
            }
          }
        ]
      }
    },
    tracking_id: "test_lowercase"
  },

  // Format 3: Type property
  typeProperty: {
    template_name: "Test type property format",
    template_type: 1,
    tag: "2",
    layout: {
      body: {
        components: [
          {
            type: "title",
            value: "Test title"
          }
        ]
      }
    },
    tracking_id: "test_type_prop"
  },

  // Format 4: Text property thay vì value
  textProperty: {
    template_name: "Test text property format",
    template_type: 1,
    tag: "2",
    layout: {
      body: {
        components: [
          {
            TITLE: {
              text: "Test title"
            }
          }
        ]
      }
    },
    tracking_id: "test_text_prop"
  },

  // Format 5: Chỉ có text đơn giản
  simpleText: {
    template_name: "Test simple text format",
    template_type: 1,
    tag: "2",
    layout: {
      body: {
        text: "Test simple message"
      }
    },
    tracking_id: "test_simple_text"
  },

  // Format 6: Array layout
  arrayLayout: {
    template_name: "Test array layout format",
    template_type: 1,
    tag: "2",
    layout: [
      {
        type: "body",
        text: "Test array message"
      }
    ],
    tracking_id: "test_array_layout"
  },

  // Format 7: Minimal - chỉ có các field bắt buộc
  minimal: {
    template_name: "Test minimal format",
    template_type: 1,
    tag: "2",
    tracking_id: "test_minimal"
  }
};

console.log('🧪 Debug ZNS Template Formats\n');

Object.entries(testFormats).forEach(([name, format]) => {
  console.log(`📋 ${name.toUpperCase()}:`);
  console.log(JSON.stringify(format, null, 2));
  console.log(`Size: ${JSON.stringify(format).length} bytes\n`);
});

console.log('📝 Gợi ý test:');
console.log('1. Test từ format minimal → current để tìm field gây lỗi');
console.log('2. Test các component type khác nhau');
console.log('3. Kiểm tra quyền ZNS của Official Account');
console.log('4. Test với OA khác (nếu có)');

// Hiển thị curl commands để test
console.log('\n🔧 Curl commands để test:');
console.log('```bash');
console.log('# Test minimal format');
console.log(`curl -X POST 'http://localhost:3003/v1/marketing/zalo/305001418732969574/zns/templates' \\`);
console.log(`  -H 'Authorization: Bearer YOUR_TOKEN' \\`);
console.log(`  -H 'Content-Type: application/json' \\`);
console.log(`  -d '${JSON.stringify(testFormats.minimal)}'`);
console.log('```');
