import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber } from 'class-validator';

/**
 * DTO cho việc xóa nhiều sản phẩm dịch vụ
 */
export class BulkDeleteServiceProductDto {
  @ApiProperty({
    description: '<PERSON>h sách ID sản phẩm dịch vụ cần xóa',
    example: [1, 2, 3, 4, 5],
    type: [Number],
  })
  @IsArray()
  @IsNotEmpty()
  @IsNumber({}, { each: true })
  ids: number[];
}

/**
 * DTO response cho việc xóa nhiều sản phẩm dịch vụ
 */
export class BulkDeleteServiceProductResponseDto {
  @ApiProperty({
    description: 'Số lượng sản phẩm dịch vụ đã xóa thành công',
    example: 3,
  })
  deletedCount: number;

  @ApiProperty({
    description: '<PERSON><PERSON> sách ID sản phẩm dịch vụ đã xóa thành công',
    example: [1, 2, 3],
    type: [Number],
  })
  deletedIds: number[];

  @ApiProperty({
    description: 'Danh sách ID sản phẩm dịch vụ không thể xóa (nếu có)',
    example: [4, 5],
    type: [Number],
    required: false,
  })
  failedIds?: number[];

  @ApiProperty({
    description: 'Thông báo kết quả',
    example: 'Đã xóa thành công 3 sản phẩm dịch vụ',
  })
  message: string;
}
