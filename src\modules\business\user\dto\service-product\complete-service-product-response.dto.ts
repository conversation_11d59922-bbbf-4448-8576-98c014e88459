import { ApiProperty } from '@nestjs/swagger';
import { PriceTypeEnum, ProductTypeEnum } from '@modules/business/enums';
import { ServicePackageResponseDto } from './service-package-response.dto';

/**
 * DTO cho response hoàn chỉnh của sản phẩm dịch vụ
 * Bao gồm tất cả thông tin từ:
 * - customer_products table
 * - service_products table
 * - service_packages_option table
 * - entity_has_media table
 */
export class CompleteServiceProductResponseDto {
  // ========== THÔNG TIN CƠ BẢN TỪ CUSTOMER_PRODUCTS ==========
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 123,
  })
  id: number;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Dịch vụ chăm sóc sức khỏe toàn diện',
  })
  name: string;

  @ApiProperty({
    description: '<PERSON>ô tả sản phẩm',
    example: 'Dịch vụ chăm sóc sức khỏe với đội ngũ chuyên gia hàng đầu',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.SERVICE,
  })
  productType: ProductTypeEnum;

  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    example: {
      listPrice: 1000000,
      salePrice: 850000,
      currency: 'VND'
    },
    required: false,
  })
  price?: Record<string, unknown>;

  @ApiProperty({
    description: 'Kiểu giá sản phẩm',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
  })
  typePrice?: PriceTypeEnum;

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: [String],
    example: ['chăm sóc sức khỏe', 'spa', 'massage', 'thư giãn'],
    required: false,
  })
  tags?: string[];

  @ApiProperty({
    description: 'Danh sách custom fields',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        customFieldId: { type: 'number', example: 1 },
        value: { type: 'object', example: { value: 'Dịch vụ cao cấp' } },
      },
    },
    example: [
      {
        customFieldId: 1,
        value: { value: 'Dịch vụ cao cấp' }
      },
      {
        customFieldId: 2,
        value: { value: 'Có chứng chỉ quốc tế' }
      }
    ],
    required: false,
  })
  customFields?: Record<string, unknown>[];

  @ApiProperty({
    description: 'Thời gian tạo sản phẩm (timestamp)',
    example: 1704067200000,
    nullable: true,
  })
  createdAt: number | null;

  @ApiProperty({
    description: 'Thời gian cập nhật sản phẩm (timestamp)',
    example: 1704067200000,
    nullable: true,
  })
  updatedAt: number | null;

  @ApiProperty({
    description: 'ID người dùng sở hữu sản phẩm',
    example: 456,
  })
  userId: number;

  // ========== THÔNG TIN TỪ SERVICE_PRODUCTS ==========
  @ApiProperty({
    description: 'Loại hình dịch vụ',
    example: 'spa',
  })
  serviceType: string;

  @ApiProperty({
    description: 'Địa điểm thực hiện dịch vụ',
    example: 'tại cửa hàng',
    required: false,
  })
  location?: string;

  @ApiProperty({
    description: 'Tên người thực hiện hoặc tổ chức cung cấp dịch vụ',
    example: 'Spa Luxury Beauty',
    required: false,
  })
  providerName?: string;

  // ========== DANH SÁCH GÓI DỊCH VỤ ==========
  @ApiProperty({
    description: 'Danh sách gói dịch vụ',
    type: [ServicePackageResponseDto],
    example: [
      {
        id: 789,
        serviceProductsId: 123,
        name: 'Gói chăm sóc da cao cấp',
        description: 'Gói chăm sóc da toàn diện với các liệu pháp hiện đại',
        price: {
          listPrice: 1000000,
          salePrice: 850000,
          currency: 'VND'
        },
        duration: 90,
        unit: 'phút',
        features: ['Massage thư giãn', 'Đắp mặt nạ collagen', 'Tư vấn chuyên sâu'],
        isActive: true,
        isLimited: false,
        maxSales: null,
        images: [
          {
            id: 'dcc8f803-04fc-49b3-953a-e660505ac353',
            url: 'https://example.com/package-image.jpg',
            name: 'package-image.jpg',
            size: 1024000,
            mimeType: 'image/jpeg',
          }
        ],
        createdAt: '2024-01-15T10:30:00.000Z',
        updatedAt: '2024-01-15T14:45:00.000Z'
      }
    ],
  })
  packages: ServicePackageResponseDto[];

  // ========== HÌNH ẢNH SẢN PHẨM ==========
  @ApiProperty({
    description: 'Danh sách hình ảnh sản phẩm',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2' },
        url: { type: 'string', example: 'media/IMAGE/2025/06/user_1/1750047286909-9ce4daff-4366-401c-b966-467e9f3a3c84.png' },
        name: { type: 'string', example: 'product-image.jpg' },
        size: { type: 'number', example: 2048000 },
      },
    },
    example: [
      {
        id: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2',
        url: 'media/IMAGE/2025/06/user_1/1750047286909-9ce4daff-4366-401c-b966-467e9f3a3c84.png',
        name: 'service-product-main.jpg',
        size: 2048000,
      },
    ],
  })
  images: Record<string, unknown>[];
}
