import { <PERSON>, Get, Param, UseGuards, Lo<PERSON>, <PERSON><PERSON>, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { MessageEvent } from '@nestjs/common';
import { MediaTrackingAdminService } from '../services/media-tracking-admin.service';
import { MediaTrackingResponseDto } from '../../dto/media-tracking-response.dto';
import { JwtPayload } from '@modules/auth/guards/jwt.util';

/**
 * Controller xử lý tracking tiến độ xử lý media cho admin
 */
@ApiTags('📊 Media Tracking (Admin)')
@Controller('admin/media-tracking')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth("JWT-auth")
export class MediaTrackingAdminController {
  private readonly logger = new Logger(MediaTrackingAdminController.name);

  constructor(private readonly mediaTrackingAdminService: MediaTrackingAdminService) {}

  /**
   * Theo dõi tiến độ xử lý media (SSE) - Admin
   */
  @ApiOperation({
    summary: '📡 Theo dõi tiến độ xử lý media (SSE) - Admin',
    description: `
Theo dõi tiến độ xử lý media theo thời gian thực sử dụng Server-Sent Events (SSE) cho admin.

**Admin có thể theo dõi tất cả media trong hệ thống.**

**Business Rules Applied:**
- R001-R003: Authentication và authorization (Admin)
- R005: Rate limiting cho SSE connections
- R018-R022: Progress data validation

**SSE Format:**
\`\`\`
data: {"media_id": "media-xxx", "progress": 50, "status": "processing", "message": "...", "timestamp": 1640995200000}

data: {"media_id": "media-xxx", "progress": 100, "status": "completed", "message": "...", "timestamp": 1640995300000}
\`\`\`

**Status Values:**
- \`pending\`: Đang chờ xử lý
- \`processing\`: Đang xử lý
- \`completed\`: Hoàn thành
- \`error\`: Có lỗi
- \`timeout\`: Hết thời gian chờ

**Connection Details:**
- Timeout: 5 phút (300 giây)
- Heartbeat: 5 giây
- Auto-close khi hoàn thành hoặc lỗi
    `,
  })
  @ApiParam({
    name: 'mediaId',
    description: 'ID của media cần theo dõi tiến độ',
    example: 'media-wtpRtUVPyEIouFZ0',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'SSE stream cho tracking tiến độ media',
    content: {
      'text/event-stream': {
        schema: {
          type: 'string',
          example: 'data: {"media_id": "media-xxx", "progress": 50, "status": "processing", "message": "Đang tạo embeddings", "timestamp": 1640995200000}',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy media',
  })
  @Sse('progress/:mediaId')
  async trackMediaProgress(
    @Param('mediaId') mediaId: string,
    @CurrentUser() user: JwtPayload,
  ): Promise<Observable<MessageEvent>> {
    this.logger.log(`Starting media tracking for media ${mediaId} by admin ${user.sub}`);

    try {
      const trackingStream = await this.mediaTrackingAdminService.trackMediaProgress(
        mediaId,
        user.sub,
      );

      // Chuyển đổi MediaTrackingResponseDto thành MessageEvent format cho SSE
      return trackingStream.pipe(
        map((data: MediaTrackingResponseDto) => {
          const messageEvent = {
            data: JSON.stringify(data),
            type: 'message',
          } as MessageEvent;

          this.logger.debug(`Sending SSE event for media ${mediaId}: ${data.status} - ${data.progress}%`);
          
          return messageEvent;
        }),
      );
    } catch (error) {
      this.logger.error(
        `Error starting tracking for media ${mediaId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
