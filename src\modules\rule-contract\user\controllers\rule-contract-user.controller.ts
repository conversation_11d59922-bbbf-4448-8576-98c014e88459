import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiExtraModels,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { RuleContractUserService } from '../services';
import {
  RuleContractQueryDto,
  RuleContractResponseDto,
  RegisterRuleContractDto,
  RuleContractStatusResponseDto,
  CreateIndividualRuleContractDto,
  CreateBusinessRuleContractDto,
  RuleContractExtendedResponseDto,
  SignContractDto,
} from '../dto';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { RuleContractStateService } from '../../state-machine/rule-contract-state.service';
import {
  RuleContractState,
  CreateEventData,
  mapStateToStatus
} from '../../state-machine/rule-contract.types';
import { ContractStatusEnum, ContractTypeEnum } from '../../entities/rule-contract.entity';

/**
 * Controller xử lý API liên quan đến hợp đồng nguyên tắc cho user
 */
@Controller('user/rule-contracts')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.USER_RULE_CONTRACT)
@ApiExtraModels(
  ApiResponseDto,
  RuleContractResponseDto,
  RuleContractStatusResponseDto,
  RuleContractExtendedResponseDto,
  PaginatedResult
)
export class RuleContractUserController {
  constructor(
    private readonly ruleContractUserService: RuleContractUserService,
    private readonly ruleContractStateService: RuleContractStateService,
  ) {}

  /**
   * Đăng ký loại hợp đồng nguyên tắc
   * @param user Thông tin người dùng hiện tại
   * @param dto Thông tin đăng ký
   * @returns Trạng thái hợp đồng
   */
  @Post('register')
  @ApiOperation({ summary: 'Đăng ký loại hợp đồng nguyên tắc' })
  @ApiResponse({
    status: 200,
    description: 'Đăng ký loại hợp đồng nguyên tắc thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(RuleContractStatusResponseDto) },
          },
        },
      ],
    },
  })
  async registerTypeRuleContract(
    @CurrentUser() user: JwtPayload,
    @Body() dto: RegisterRuleContractDto,
  ): Promise<ApiResponseDto<RuleContractStatusResponseDto>> {
    // Sử dụng state machine để tạo hợp đồng
    const createData: CreateEventData = {
      userId: user.id,
      contractType: dto.type,
    };

    // Tạo hợp đồng mới thông qua state machine
    await this.ruleContractStateService.createContract(user.id, createData);

    // Trả về trạng thái hợp đồng
    return ApiResponseDto.success(
      {
        status: mapStateToStatus(RuleContractState.DRAFT),
        type: dto.type,
      },
      'Đăng ký loại hợp đồng nguyên tắc thành công',
    );
  }

  /**
   * Lấy danh sách hợp đồng nguyên tắc của người dùng
   * @param user Thông tin người dùng hiện tại
   * @param queryDto Tham số truy vấn
   * @returns Danh sách hợp đồng với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách hợp đồng nguyên tắc của người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách hợp đồng nguyên tắc thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              allOf: [
                { $ref: getSchemaPath(PaginatedResult) },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: getSchemaPath(RuleContractResponseDto) },
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    },
  })
  async getContracts(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: RuleContractQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<RuleContractResponseDto>>> {
    const contracts = await this.ruleContractUserService.getContracts(user.id, queryDto);
    return ApiResponseDto.success(
      contracts,
      'Lấy danh sách hợp đồng nguyên tắc thành công',
    );
  }

  /**
   * Lấy chi tiết hợp đồng nguyên tắc của người dùng
   * @param user Thông tin người dùng hiện tại
   * @param id ID của hợp đồng
   * @returns Chi tiết hợp đồng
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết hợp đồng nguyên tắc' })
  @ApiParam({
    name: 'id',
    description: 'ID của hợp đồng',
    type: 'number',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết hợp đồng nguyên tắc thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(RuleContractResponseDto) },
          },
        },
      ],
    },
  })
  async getContractById(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<RuleContractResponseDto>> {
    const contract = await this.ruleContractUserService.getContractById(user.id, id);
    return ApiResponseDto.success(
      contract,
      'Lấy chi tiết hợp đồng nguyên tắc thành công',
    );
  }

  /**
   * Tạo hợp đồng nguyên tắc cho cá nhân
   * @param user Thông tin người dùng hiện tại
   * @param dto Thông tin hợp đồng
   * @returns Thông tin hợp đồng đã tạo
   */
  @Post('individual')
  @ApiOperation({ summary: 'Tạo hợp đồng nguyên tắc cho cá nhân' })
  @ApiResponse({
    status: 200,
    description: 'Tạo hợp đồng nguyên tắc cho cá nhân thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(RuleContractExtendedResponseDto) },
          },
        },
      ],
    },
  })
  async createIndividualRuleContract(
    @CurrentUser() user: JwtPayload,
    @Body() dto: CreateIndividualRuleContractDto,
  ): Promise<ApiResponseDto<RuleContractExtendedResponseDto>> {
    // Sử dụng state machine để tạo hợp đồng cá nhân
    const result = await this.ruleContractStateService.createIndividualContract(user.id, {
      name: dto.name,
      address: dto.address,
      phone: dto.phone,
      dateOfBirth: dto.dateOfBirth,
      cccd: dto.cccd,
      issuePlace: dto.issuePlace,
      issueDate: dto.issueDate,
      taxCode: dto.taxCode,
    });

    // Trả về thông tin hợp đồng
    return ApiResponseDto.success(
      {
        status: ContractStatusEnum.DRAFT,
        type: ContractTypeEnum.INDIVIDUAL,
        contractBase64: result.contractBase64,
        contractUrl: result.contractUrl || '',
      },
      'Tạo hợp đồng nguyên tắc cho cá nhân thành công',
    );
  }

  /**
   * Tạo hợp đồng nguyên tắc cho doanh nghiệp
   * @param user Thông tin người dùng hiện tại
   * @param dto Thông tin hợp đồng doanh nghiệp
   * @returns Thông tin hợp đồng đã tạo
   */
  @Post('business')
  @ApiOperation({ summary: 'Tạo hợp đồng nguyên tắc cho doanh nghiệp' })
  @ApiResponse({
    status: 200,
    description: 'Tạo hợp đồng nguyên tắc cho doanh nghiệp thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(RuleContractExtendedResponseDto) },
          },
        },
      ],
    },
  })
  async createBusinessRuleContract(
    @CurrentUser() user: JwtPayload,
    @Body() dto: CreateBusinessRuleContractDto,
  ): Promise<ApiResponseDto<RuleContractExtendedResponseDto>> {
    // Sử dụng state machine để tạo hợp đồng doanh nghiệp
    const result = await this.ruleContractStateService.createBusinessContract(user.id, {
      businessName: dto.businessName,
      representativeName: dto.representativeName,
      representativePosition: dto.representativePosition,
      businessEmail: dto.businessEmail,
      businessPhone: dto.businessPhone,
      businessAddress: dto.businessAddress,
      taxCode: dto.taxCode,
    });

    // Trả về thông tin hợp đồng
    return ApiResponseDto.success(
      {
        status: ContractStatusEnum.DRAFT,
        type: ContractTypeEnum.BUSINESS,
        contractBase64: result.contractBase64,
        contractUrl: result.contractUrl || '',
      },
      'Tạo hợp đồng nguyên tắc cho doanh nghiệp thành công',
    );
  }

  /**
   * Lấy trạng thái hợp đồng nguyên tắc mới nhất của người dùng
   * @param user Thông tin người dùng hiện tại
   * @returns Trạng thái hợp đồng mới nhất
   */
  @Get('latest-status')
  @ApiOperation({ summary: 'Lấy trạng thái hợp đồng nguyên tắc mới nhất của người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Lấy trạng thái hợp đồng nguyên tắc mới nhất thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              oneOf: [
                { $ref: getSchemaPath(RuleContractStatusResponseDto) },
                { type: 'null' }
              ]
            },
          },
        },
      ],
    },
  })
  async getLatestContractStatus(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<RuleContractStatusResponseDto | null>> {
    const status = await this.ruleContractUserService.getLatestContractStatus(user.id);
    return ApiResponseDto.success(
      status,
      'Lấy trạng thái hợp đồng nguyên tắc mới nhất thành công',
    );
  }

  /**
   * Ký hợp đồng nguyên tắc
   * @param user Thông tin người dùng hiện tại
   * @param id ID của hợp đồng
   * @param dto Thông tin chữ ký
   * @returns Trạng thái hợp đồng sau khi ký
   */
  @Post(':id/sign')
  @ApiOperation({ summary: 'Ký hợp đồng nguyên tắc' })
  @ApiParam({
    name: 'id',
    description: 'ID của hợp đồng',
    type: 'number',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Ký hợp đồng nguyên tắc thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'object',
              properties: {
                state: {
                  type: 'string',
                  enum: Object.values(RuleContractState),
                  example: RuleContractState.PENDING_APPROVAL,
                },
              },
            },
          },
        },
      ],
    },
  })
  async signContract(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: SignContractDto,
  ): Promise<ApiResponseDto<{ state: RuleContractState }>> {
    // Kiểm tra quyền truy cập hợp đồng
    await this.ruleContractUserService.getContractById(user.id, id);

    // Sử dụng state machine để ký hợp đồng
    const state = await this.ruleContractStateService.signContract(id, {
      signatureData: dto.signatureData,
    });

    return ApiResponseDto.success(
      { state },
      'Ký hợp đồng nguyên tắc thành công',
    );
  }

  /**
   * Gửi lại hợp đồng đã bị từ chối
   * @param user Thông tin người dùng hiện tại
   * @param id ID của hợp đồng
   * @returns Trạng thái hợp đồng sau khi gửi lại
   */
  @Post(':id/resubmit')
  @ApiOperation({ summary: 'Gửi lại hợp đồng đã bị từ chối' })
  @ApiParam({
    name: 'id',
    description: 'ID của hợp đồng',
    type: 'number',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Gửi lại hợp đồng thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'object',
              properties: {
                state: {
                  type: 'string',
                  enum: Object.values(RuleContractState),
                  example: RuleContractState.DRAFT,
                },
              },
            },
          },
        },
      ],
    },
  })
  async resubmitContract(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<{ state: RuleContractState }>> {
    // Kiểm tra quyền truy cập hợp đồng
    await this.ruleContractUserService.getContractById(user.id, id);

    // Sử dụng state machine để gửi lại hợp đồng
    const state = await this.ruleContractStateService.resubmitContract(id);

    return ApiResponseDto.success(
      { state },
      'Gửi lại hợp đồng thành công',
    );
  }

  /**
   * Nâng cấp hợp đồng từ cá nhân lên doanh nghiệp
   * @param user Thông tin người dùng hiện tại
   * @param id ID của hợp đồng
   * @returns Trạng thái hợp đồng sau khi nâng cấp
   */
  @Post(':id/upgrade-to-business')
  @ApiOperation({ summary: 'Nâng cấp hợp đồng từ cá nhân lên doanh nghiệp' })
  @ApiParam({
    name: 'id',
    description: 'ID của hợp đồng',
    type: 'number',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Nâng cấp hợp đồng thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'object',
              properties: {
                state: {
                  type: 'string',
                  enum: Object.values(RuleContractState),
                  example: RuleContractState.DRAFT,
                },
              },
            },
          },
        },
      ],
    },
  })
  async upgradeToBusinessContract(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<{ state: RuleContractState }>> {
    // Kiểm tra quyền truy cập hợp đồng
    await this.ruleContractUserService.getContractById(user.id, id);

    // Sử dụng state machine để nâng cấp hợp đồng
    const state = await this.ruleContractStateService.upgradeToBusinessContract(id, {});

    return ApiResponseDto.success(
      { state },
      'Nâng cấp hợp đồng thành công',
    );
  }

  /**
   * Lấy trạng thái hiện tại của hợp đồng
   * @param user Thông tin người dùng hiện tại
   * @param id ID của hợp đồng
   * @returns Trạng thái hiện tại của hợp đồng
   */
  @Get(':id/state')
  @ApiOperation({ summary: 'Lấy trạng thái hiện tại của hợp đồng' })
  @ApiParam({
    name: 'id',
    description: 'ID của hợp đồng',
    type: 'number',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy trạng thái hợp đồng thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'object',
              properties: {
                state: {
                  type: 'string',
                  enum: Object.values(RuleContractState),
                  example: RuleContractState.DRAFT,
                },
              },
            },
          },
        },
      ],
    },
  })
  async getCurrentState(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<{ state: RuleContractState }>> {
    // Kiểm tra quyền truy cập hợp đồng
    await this.ruleContractUserService.getContractById(user.id, id);

    // Sử dụng state machine để lấy trạng thái hiện tại
    const state = await this.ruleContractStateService.getCurrentState(id);

    return ApiResponseDto.success(
      { state },
      'Lấy trạng thái hợp đồng thành công',
    );
  }
}
