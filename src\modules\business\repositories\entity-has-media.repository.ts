import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, IsNull } from 'typeorm';
import { EntityHasMedia } from '../entities/entity-has-media.entity';
import { PaginatedResult } from '@common/response';

/**
 * Repository cho EntityHasMedia entity
 * Xử lý các thao tác database cho liên kết media với entity
 */
@Injectable()
export class EntityHasMediaRepository {
  private readonly logger = new Logger(EntityHasMediaRepository.name);

  constructor(
    @InjectRepository(EntityHasMedia)
    private readonly repository: Repository<EntityHasMedia>,
  ) {}

  /**
   * Tạo liên kết media với entity
   * @param data Dữ liệu liên kết
   * @returns Liên kết đã tạo
   */
  async create(data: Partial<EntityHasMedia>): Promise<EntityHasMedia> {
    const entityMedia = this.repository.create(data);
    return this.repository.save(entityMedia);
  }

  /**
   * Tìm liên kết theo ID
   * @param id ID liên kết
   * @returns Liên kết hoặc null
   */
  async findById(id: number): Promise<EntityHasMedia | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Lấy danh sách media theo productId
   * @param productId ID sản phẩm
   * @returns Danh sách media
   */
  async findByProductId(productId: number): Promise<EntityHasMedia[]> {
    return this.repository.find({
      where: { productId },
    });
  }

  /**
   * Lấy danh sách media theo physicalVarial
   * @param physicalVarial ID biến thể vật lý
   * @returns Danh sách media
   */
  async findByPhysicalVarial(physicalVarial: number): Promise<EntityHasMedia[]> {
    return this.repository.find({
      where: { physicalVarial },
    });
  }

  /**
   * Lấy danh sách media theo ticketVarial
   * @param ticketVarial ID biến thể vé
   * @returns Danh sách media
   */
  async findByTicketVarial(ticketVarial: number): Promise<EntityHasMedia[]> {
    return this.repository.find({
      where: { ticketVarial },
    });
  }

  /**
   * Lấy danh sách media theo versionId
   * @param versionId ID phiên bản
   * @returns Danh sách media
   */
  async findByVersionId(versionId: number): Promise<EntityHasMedia[]> {
    return this.repository.find({
      where: { versionId },
    });
  }

  /**
   * Lấy danh sách media theo productComboId
   * @param productComboId ID combo
   * @returns Danh sách media
   */
  async findByProductComboId(productComboId: number): Promise<EntityHasMedia[]> {
    return this.repository.find({
      where: { productComboId },
    });
  }

  /**
   * Lấy danh sách media theo productPlanVarialId
   * @param productPlanVarialId ID gói dịch vụ
   * @returns Danh sách media
   */
  async findByProductPlanVarialId(productPlanVarialId: number): Promise<EntityHasMedia[]> {
    return this.repository.find({
      where: { productPlanVarialId },
    });
  }

  /**
   * Lấy danh sách liên kết với phân trang và filter
   * @param query Tham số truy vấn
   * @returns Danh sách liên kết với phân trang
   */
  async findAll(query: {
    page?: number;
    limit?: number;
    productId?: number;
    physicalVarial?: number;
    ticketVarial?: number;
    versionId?: number;
    productComboId?: number;
    productPlanVarialId?: number;
    mediaId?: string;
  }): Promise<PaginatedResult<EntityHasMedia>> {
    const {
      page = 1,
      limit = 10,
      productId,
      physicalVarial,
      ticketVarial,
      versionId,
      productComboId,
      productPlanVarialId,
      mediaId,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('entityMedia');

    // Filter theo các trường
    if (productId) {
      queryBuilder.andWhere('entityMedia.productId = :productId', { productId });
    }
    if (physicalVarial) {
      queryBuilder.andWhere('entityMedia.physicalVarial = :physicalVarial', { physicalVarial });
    }
    if (ticketVarial) {
      queryBuilder.andWhere('entityMedia.ticketVarial = :ticketVarial', { ticketVarial });
    }
    if (versionId) {
      queryBuilder.andWhere('entityMedia.versionId = :versionId', { versionId });
    }
    if (productComboId) {
      queryBuilder.andWhere('entityMedia.productComboId = :productComboId', { productComboId });
    }
    if (productPlanVarialId) {
      queryBuilder.andWhere('entityMedia.productPlanVarialId = :productPlanVarialId', { productPlanVarialId });
    }
    if (mediaId) {
      queryBuilder.andWhere('entityMedia.mediaId = :mediaId', { mediaId });
    }

    // Sắp xếp theo ID
    queryBuilder.orderBy('entityMedia.id', 'DESC');

    // Phân trang
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Thực hiện truy vấn
    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Cập nhật liên kết
   * @param id ID liên kết
   * @param data Dữ liệu cập nhật
   * @returns Liên kết đã cập nhật
   */
  async update(id: number, data: Partial<EntityHasMedia>): Promise<EntityHasMedia> {
    await this.repository.update(id, data);

    const updatedEntity = await this.findById(id);
    if (!updatedEntity) {
      throw new Error(`EntityHasMedia with ID ${id} not found after update`);
    }

    return updatedEntity;
  }

  /**
   * Xóa liên kết
   * @param id ID liên kết
   */
  async delete(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Xóa tất cả liên kết theo productId
   * @param productId ID sản phẩm
   */
  async deleteByProductId(productId: number): Promise<void> {
    await this.repository.delete({ productId });
  }

  /**
   * Xóa tất cả liên kết theo physicalVarial
   * @param physicalVarial ID biến thể vật lý
   */
  async deleteByPhysicalVarial(physicalVarial: number): Promise<void> {
    await this.repository.delete({ physicalVarial });
  }

  /**
   * Xóa tất cả liên kết theo ticketVarial
   * @param ticketVarial ID biến thể vé
   */
  async deleteByTicketVarial(ticketVarial: number): Promise<void> {
    await this.repository.delete({ ticketVarial });
  }

  /**
   * Xóa tất cả liên kết theo versionId
   * @param versionId ID phiên bản
   */
  async deleteByVersionId(versionId: number): Promise<void> {
    await this.repository.delete({ versionId });
  }

  /**
   * Lưu liên kết
   * @param entityMedia Liên kết cần lưu
   * @returns Liên kết đã lưu
   */
  async save(entityMedia: EntityHasMedia): Promise<EntityHasMedia> {
    return this.repository.save(entityMedia);
  }

  /**
   * Tìm liên kết theo danh sách IDs
   * @param ids Danh sách ID
   * @returns Danh sách liên kết
   */
  async findByIds(ids: number[]): Promise<EntityHasMedia[]> {
    if (!ids.length) return [];

    return this.repository.find({
      where: { id: In(ids) },
    });
  }

  /**
   * Đếm số lượng media theo entity
   * @param entityType Loại entity
   * @param entityId ID entity
   * @returns Số lượng media
   */
  async countByEntity(entityType: string, entityId: number): Promise<number> {
    const whereCondition: any = {};

    switch (entityType) {
      case 'product':
        whereCondition.productId = entityId;
        break;
      case 'physicalVarial':
        whereCondition.physicalVarial = entityId;
        break;
      case 'ticketVarial':
        whereCondition.ticketVarial = entityId;
        break;
      case 'version':
        whereCondition.versionId = entityId;
        break;
      case 'combo':
        whereCondition.productComboId = entityId;
        break;
      case 'plan':
        whereCondition.productPlanVarialId = entityId;
        break;
      default:
        return 0;
    }

    return this.repository.count({ where: whereCondition });
  }

  /**
   * Tìm liên kết media cụ thể cho product level
   * @param productId ID sản phẩm
   * @param mediaId ID media
   * @returns Liên kết media hoặc null
   */
  async findProductLevelMedia(productId: number, mediaId: string): Promise<EntityHasMedia | null> {
    return this.repository.findOne({
      where: {
        productId,
        mediaId,
        physicalVarial: IsNull(),
        ticketVarial: IsNull(),
        versionId: IsNull(),
        productComboId: IsNull(),
        productPlanVarialId: IsNull(),
      },
    });
  }

  /**
   * Tìm liên kết media cụ thể cho variant level
   * @param productId ID sản phẩm
   * @param variantId ID variant
   * @param mediaId ID media
   * @returns Liên kết media hoặc null
   */
  async findVariantLevelMedia(productId: number, variantId: number, mediaId: string): Promise<EntityHasMedia | null> {
    return this.repository.findOne({
      where: {
        productId,
        physicalVarial: variantId,
        mediaId,
        ticketVarial: IsNull(),
        versionId: IsNull(),
        productComboId: IsNull(),
        productPlanVarialId: IsNull(),
      },
    });
  }

  /**
   * Tìm liên kết media cụ thể cho version level (digital product)
   * @param productId ID sản phẩm
   * @param versionId ID version
   * @param mediaId ID media
   * @returns Liên kết media hoặc null
   */
  async findVersionLevelMedia(productId: number, versionId: number, mediaId: string): Promise<EntityHasMedia | null> {
    return this.repository.findOne({
      where: {
        productId,
        versionId: versionId,
        mediaId,
        physicalVarial: IsNull(),
        ticketVarial: IsNull(),
        productComboId: IsNull(),
        productPlanVarialId: IsNull(),
      },
    });
  }

  /**
   * Xóa liên kết media theo điều kiện cụ thể (tránh xóa nhầm)
   * @param productId ID sản phẩm
   * @param mediaId ID media
   * @param variantId ID variant (undefined nếu là product level)
   */
  async deleteSpecificMediaLink(productId: number, mediaId: string, variantId?: number): Promise<void> {
    const whereCondition: any = {
      productId,
      mediaId,
      ticketVarial: IsNull(),
      versionId: IsNull(),
      productComboId: IsNull(),
      productPlanVarialId: IsNull(),
    };

    if (variantId) {
      // Variant level
      whereCondition.physicalVarial = variantId;
    } else {
      // Product level
      whereCondition.physicalVarial = IsNull();
    }

    await this.repository.delete(whereCondition);
  }
}
