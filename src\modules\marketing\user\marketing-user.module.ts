import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { HttpModule } from '@nestjs/axios';
import * as entities from './entities';
import * as repositories from './repositories';
import * as services from './services';
import * as controllers from './controllers';
import { ZaloApiClient } from '@shared/services/zalo/zalo-api.client';
import { ZaloModule } from '@shared/services/zalo/zalo.module';
import { QueueName } from '@shared/queue/queue.constants';
import { IntegrationUserModule } from '@/modules/integration/user/integration-user.module';

// Import các repositories và services cụ thể
import { UserTagRepository } from './repositories/user-tag.repository';
import { UserSegmentRepository } from './repositories/user-segment.repository';
import { UserAudienceRepository } from './repositories/user-audience.repository';
import { UserAudienceCustomFieldRepository } from './repositories/user-audience-custom-field.repository';
import { UserAudienceCustomFieldDefinitionRepository } from './repositories/user-audience-custom-field-definition.repository';
import { UserAudienceHasTagRepository } from './repositories/user-audience-has-tag.repository';
import { UserCampaignRepository } from './repositories/user-campaign.repository';
import { UserCampaignHistoryRepository } from './repositories/user-campaign-history.repository';
import { UserTemplateEmailRepository } from './repositories/user-template-email.repository';

import { UserTagService } from './services/user-tag.service';
import { UserSegmentService } from './services/user-segment.service';
import { UserAudienceService } from './services/user-audience.service';
import { UserCampaignService } from './services/user-campaign.service';
import { UserTemplateEmailService } from './services/user-template-email.service';
import { UserMarketingStatisticsService } from './services/user-marketing-statistics.service';
import { UserAudienceCustomFieldDefinitionService } from './services/user-audience-custom-field-definition.service';
import { UserAudienceCustomFieldService } from './services/user-audience-custom-field.service';
import { MarketingOverviewService } from './services/marketing-overview.service';
import { ZaloService } from './services/zalo.service';
import { ZaloZnsService } from './services/zalo-zns.service';
import { ZaloZnsCampaignService } from './services/zalo-zns-campaign.service';
import { ZaloTokenService } from './services/zalo-token.service';
import { EmailMarketingService } from './services/email-marketing.service';

// Import shared Zalo services
import { ZaloZnsInfoService } from '@shared/services/zalo/zalo-zns-info.service';

import { UserTagController } from './controllers/user-tag.controller';
import { UserAudienceController } from './controllers/user-audience.controller';
import { UserSegmentController } from './controllers/user-segment.controller';
import { UserCampaignController } from './controllers/user-campaign.controller';
import { UserMarketingStatisticsController } from './controllers/user-marketing-statistics.controller';
import { UserTemplateEmailController } from './controllers/user-template-email.controller';
import { UserAudienceCustomFieldDefinitionController } from './controllers/user-audience-custom-field-definition.controller';
import { UserAudienceCustomFieldController } from './controllers/user-audience-custom-field.controller';
import { MarketingOverviewController } from './controllers/marketing-overview.controller';
import { ZaloController } from './controllers/zalo.controller';
import { ZaloWebhookController } from './controllers/zalo-webhook.controller';

import { ZaloZnsController } from './controllers/zalo-zns.controller';
import { ZaloZnsCampaignController } from './controllers/zalo-zns-campaign.controller';
import { ZaloSegmentController } from './controllers/zalo-segment.controller';
import { ZaloCampaignController } from './controllers/zalo-campaign.controller';
import { ZaloAutomationController } from './controllers/zalo-automation.controller';
import { ZaloTagController } from './controllers/zalo-tag.controller';
import { ZaloTemplateController } from './controllers/zalo-template.controller';
import { ZaloIntegrationController } from './controllers/zalo-integration.controller';
import { EmailCampaignController } from './controllers/email-campaign.controller';



/**
 * Module quản lý marketing user
 */
@Module({
  imports: [
    TypeOrmModule.forFeature(Object.values(entities)),
    BullModule.registerQueue({
      name: QueueName.EMAIL_MARKETING,
    }),
    BullModule.registerQueue({
      name: QueueName.ZALO_ZNS,
    }),
    forwardRef(() => IntegrationUserModule),
    ZaloModule,
    HttpModule,
  ],
  controllers: [
    UserTagController,
    UserAudienceController,
    UserSegmentController,
    UserCampaignController,
    UserMarketingStatisticsController,
    UserTemplateEmailController,
    UserAudienceCustomFieldDefinitionController,
    UserAudienceCustomFieldController,
    MarketingOverviewController,
    ZaloController,
    ZaloWebhookController,
    ZaloZnsController,
    ZaloZnsCampaignController,
    ZaloSegmentController,
    ZaloCampaignController,
    ZaloAutomationController,
    ZaloTagController,
    ZaloTemplateController,
    ZaloIntegrationController,
    EmailCampaignController,
  ],
  providers: [
    // Repositories
    UserTagRepository,
    UserSegmentRepository,
    UserAudienceRepository,
    UserAudienceCustomFieldRepository,
    UserAudienceCustomFieldDefinitionRepository,
    UserAudienceHasTagRepository,
    UserCampaignRepository,
    UserCampaignHistoryRepository,
    UserTemplateEmailRepository,

    // Zalo repositories
    repositories.ZaloOfficialAccountRepository,
    repositories.ZaloZnsTemplateRepository,
    repositories.ZaloMessageRepository,
    repositories.ZaloZnsMessageRepository,
    repositories.ZaloZnsCampaignRepository,
    repositories.ZaloFollowerRepository,
    repositories.ZaloWebhookLogRepository,
    repositories.ZaloSegmentRepository,
    repositories.ZaloCampaignRepository,
    repositories.ZaloCampaignLogRepository,
    repositories.ZaloAutomationRepository,
    repositories.ZaloAutomationLogRepository,
    repositories.ZaloMessageTemplateRepository,

    // Zalo Ads repositories
    repositories.ZaloAdsAccountRepository,
    repositories.ZaloAdsCampaignRepository,
    repositories.ZaloAdsPerformanceRepository,



    // Services
    UserTagService,
    UserSegmentService,
    UserAudienceService,
    UserCampaignService,
    UserTemplateEmailService,
    UserMarketingStatisticsService,
    UserAudienceCustomFieldDefinitionService,
    UserAudienceCustomFieldService,
    MarketingOverviewService,

    // Zalo services
    ZaloApiClient,
    ZaloService,
    ZaloZnsService,
    ZaloZnsCampaignService,
    ZaloTokenService,
    ZaloZnsInfoService,

    // Email Marketing service
    EmailMarketingService,
  ],
  exports: [
    TypeOrmModule,
    // Repositories
    UserTagRepository,
    UserSegmentRepository,
    UserAudienceRepository,
    UserAudienceCustomFieldRepository,
    UserAudienceCustomFieldDefinitionRepository,
    UserAudienceHasTagRepository,
    UserCampaignRepository,
    UserCampaignHistoryRepository,
    UserTemplateEmailRepository,

    // Zalo repositories
    repositories.ZaloOfficialAccountRepository,
    repositories.ZaloZnsTemplateRepository,
    repositories.ZaloMessageRepository,
    repositories.ZaloZnsMessageRepository,
    repositories.ZaloZnsCampaignRepository,
    repositories.ZaloFollowerRepository,
    repositories.ZaloWebhookLogRepository,
    repositories.ZaloSegmentRepository,
    repositories.ZaloCampaignRepository,
    repositories.ZaloCampaignLogRepository,
    repositories.ZaloAutomationRepository,
    repositories.ZaloAutomationLogRepository,
    repositories.ZaloMessageTemplateRepository,

    // Zalo Ads repositories
    repositories.ZaloAdsAccountRepository,
    repositories.ZaloAdsCampaignRepository,
    repositories.ZaloAdsPerformanceRepository,

    // Services
    UserTagService,
    UserSegmentService,
    UserAudienceService,
    UserCampaignService,
    UserTemplateEmailService,
    UserMarketingStatisticsService,
    UserAudienceCustomFieldDefinitionService,

    // Zalo services
    ZaloService,
    ZaloZnsService,
    ZaloZnsCampaignService,
    ZaloTokenService,
  ],
})
export class MarketingUserModule {}
