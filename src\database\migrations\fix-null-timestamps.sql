-- Fix null createdAt and updatedAt timestamps in customer_products table
UPDATE customer_products 
SET 
    created_at = EXTRACT(epoch FROM now()) * 1000,
    updated_at = EXTRACT(epoch FROM now()) * 1000
WHERE created_at IS NULL OR updated_at IS NULL;

-- Fix null createdAt and updatedAt timestamps in service_products table
UPDATE service_products 
SET 
    created_at = EXTRACT(epoch FROM now()) * 1000,
    updated_at = EXTRACT(epoch FROM now()) * 1000
WHERE created_at IS NULL OR updated_at IS NULL;

-- Fix null createdAt and updatedAt timestamps in service_packages_option table
UPDATE service_packages_option 
SET 
    created_at = EXTRACT(epoch FROM now()) * 1000,
    updated_at = EXTRACT(epoch FROM now()) * 1000
WHERE created_at IS NULL OR updated_at IS NULL;

-- Add comments
COMMENT ON COLUMN customer_products.created_at IS 'Thời điểm tạo (timestamp dạng bigint epoch milliseconds)';
COMMENT ON COLUMN customer_products.updated_at IS 'Thời điểm cập nhật (timestamp dạng bigint epoch milliseconds)';

COMMENT ON COLUMN service_products.created_at IS 'Thời điểm tạo bản ghi (timestamp dạng epoch milliseconds)';
COMMENT ON COLUMN service_products.updated_at IS 'Thời điểm cập nhật bản ghi (timestamp dạng epoch milliseconds)';

COMMENT ON COLUMN service_packages_option.created_at IS 'Thời gian tạo bản ghi (epoch milliseconds)';
COMMENT ON COLUMN service_packages_option.updated_at IS 'Thời gian cập nhật bản ghi (epoch milliseconds)';
