import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { UpdateProductStatusDto } from '../../dto/customfields/update-product-status.dto';
import { EntityStatusEnum } from '@modules/business/enums/entity-status.enum';

describe('UpdateProductStatusDto', () => {
  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductStatusDto, {
      productIds: [1, 2, 3],
      status: EntityStatusEnum.APPROVED,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với lý do từ chối khi trạng thái là REJECTED', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductStatusDto, {
      productIds: [1, 2, 3],
      status: EntityStatusEnum.REJECTED,
      rejectReason: 'Sản phẩm ok',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi thiếu productIds', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductStatusDto, {
      status: EntityStatusEnum.APPROVED,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isArray');
  });

  it('nên thất bại khi productIds không phải là mảng', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductStatusDto, {
      productIds: 1,
      status: EntityStatusEnum.APPROVED,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isArray');
  });

  it('nên thất bại khi productIds chứa giá trị không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductStatusDto, {
      productIds: [1, 'not-a-number', 3],
      status: EntityStatusEnum.APPROVED,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi thiếu status', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductStatusDto, {
      productIds: [1, 2, 3],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isEnum');
  });

  it('nên thất bại khi status không phải là giá trị hợp lệ của EntityStatusEnum', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductStatusDto, {
      productIds: [1, 2, 3],
      status: 'INVALID_STATUS',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isEnum');
  });

  it('nên thất bại khi rejectReason không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductStatusDto, {
      productIds: [1, 2, 3],
      status: EntityStatusEnum.REJECTED,
      rejectReason: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('nên chuyển đổi productIds từ chuỗi sang số', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductStatusDto, {
      productIds: ['1', '2', '3'],
      status: EntityStatusEnum.APPROVED,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.productIds).toEqual([1, 2, 3]);
  });
});
