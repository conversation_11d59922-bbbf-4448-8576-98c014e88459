import { ApiProperty } from '@nestjs/swagger';
import { ProductPrice } from '@modules/business/interfaces';

/**
 * DTO cho response của gói dịch vụ
 */
export class ServicePackageResponseDto {
  @ApiProperty({
    description: 'ID gói dịch vụ',
    example: 123,
  })
  id: number;

  @ApiProperty({
    description: 'ID sản phẩm dịch vụ liên kết',
    example: 456,
  })
  serviceProductsId: number;

  @ApiProperty({
    description: 'Tên gói dịch vụ',
    example: 'Gó<PERSON> chăm sóc da cao cấp',
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả chi tiết về gói dịch vụ',
    example: 'Gói chăm sóc da toàn diện với các liệu pháp hiện đại',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> của gói dịch vụ',
    example: {
      listPrice: 1000000,
      salePrice: 850000,
      currency: 'VND'
    },
  })
  price: ProductPrice;

  @ApiProperty({
    description: 'Thời lượng của gói dịch vụ',
    example: 90,
    required: false,
  })
  duration?: number;

  @ApiProperty({
    description: 'Đơn vị tính cho thời lượng',
    example: 'phút',
    required: false,
  })
  unit?: string;

  @ApiProperty({
    description: 'Danh sách tính năng hoặc lợi ích đi kèm gói dịch vụ',
    example: ['Massage thư giãn', 'Đắp mặt nạ collagen', 'Tư vấn chuyên sâu'],
    required: false,
  })
  features?: any;

  @ApiProperty({
    description: 'Trạng thái hoạt động của gói dịch vụ',
    example: true,
    required: false,
  })
  isActive?: boolean;

  @ApiProperty({
    description: 'Gói dịch vụ có bị giới hạn số lần bán không',
    example: false,
    required: false,
  })
  isLimited?: boolean;

  @ApiProperty({
    description: 'Số lượng tối đa được bán nếu gói bị giới hạn',
    example: 100,
    required: false,
  })
  maxSales?: number;

  @ApiProperty({
    description: 'Danh sách hình ảnh của gói dịch vụ',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'dcc8f803-04fc-49b3-953a-e660505ac353' },
        url: { type: 'string', example: 'media/IMAGE/2025/06/user_1/1750047286894-2fb8144c-5d69-498e-a5bc-9c8f4a0f12da.png' },
        name: { type: 'string', example: 'package-image.jpg' },
        size: { type: 'number', example: 1024000 },
      },
    },
    example: [
      {
        id: 'dcc8f803-04fc-49b3-953a-e660505ac353',
        url: 'media/IMAGE/2025/06/user_1/1750047286894-2fb8144c-5d69-498e-a5bc-9c8f4a0f12da.png',
        name: 'service-package-1.jpg',
        size: 1024000,
      },
    ],
  })
  images: Record<string, unknown>[];

  @ApiProperty({
    description: 'Thời gian tạo gói dịch vụ (timestamp)',
    example: 1704067200000,
    nullable: true,
    required: false,
  })
  createdAt?: number | null;

  @ApiProperty({
    description: 'Thời gian cập nhật gói dịch vụ (timestamp)',
    example: 1704067200000,
    nullable: true,
    required: false,
  })
  updatedAt?: number | null;
}
