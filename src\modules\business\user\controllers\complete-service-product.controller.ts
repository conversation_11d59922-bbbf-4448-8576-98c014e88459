import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Put,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto } from '@common/response';
import { CompleteServiceProductService } from '@modules/business/user/services';
import { CompleteUpdateServiceProductDto } from '@modules/business/user/dto/service-product';
import { CompleteServiceProductResponseDto } from '@modules/business/user/dto/service-product';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý cập nhật hoàn chỉnh Service Product
 * Bao gồm customer_products + service_products + packages + images
 */
@ApiTags(SWAGGER_API_TAGS.USER_BUSINESS_VARIANT_PRODUCT)
@Controller('user/products/service')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto,
  CompleteUpdateServiceProductDto,
  CompleteServiceProductResponseDto,
)
export class CompleteServiceProductController {
  constructor(
    private readonly completeServiceProductService: CompleteServiceProductService,
  ) {}

  /**
   * Cập nhật hoàn chỉnh sản phẩm dịch vụ
   * @param id ID của sản phẩm dịch vụ
   * @param updateDto DTO chứa toàn bộ thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm dịch vụ đã cập nhật
   */
  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cập nhật hoàn chỉnh sản phẩm dịch vụ',
    description: `Cập nhật toàn bộ thông tin sản phẩm dịch vụ bao gồm:
    - Thông tin cơ bản từ customer_products (name, description, price, tags, etc.)
    - Thông tin dịch vụ từ service_products (serviceType, location, providerName)
    - Quản lý gói dịch vụ thông qua operations (ADD/UPDATE/DELETE)
    - Quản lý images thông qua operations (ADD/DELETE) cho cả product level và package level`,
  })
  @ApiBody({
    type: CompleteUpdateServiceProductDto,
    description: 'Dữ liệu cập nhật hoàn chỉnh sản phẩm dịch vụ',
    examples: {
      'spa-service': {
        summary: 'Dịch vụ Spa chăm sóc sức khỏe',
        description: 'Ví dụ cho dịch vụ spa với các gói chăm sóc khác nhau',
        value: {
          basicInfo: {
            name: 'Dịch vụ Spa chăm sóc sức khỏe toàn diện',
            description: 'Dịch vụ spa cao cấp với đội ngũ chuyên gia và thiết bị hiện đại',
            productType: 'SERVICE',
            tags: ['spa', 'chăm sóc sức khỏe', 'massage', 'thư giãn']
          },
          pricing: {
            price: {
              listPrice: 1500000,
              salePrice: 1200000,
              currency: 'VND'
            },
            typePrice: 'HAS_PRICE'
          },
          serviceInfo: {
            serviceType: 'spa',
            location: 'tại cửa hàng',
            providerName: 'Spa Luxury Beauty'
          },
          operations: {
            packages: [
              {
                operation: 'add',
                data: {
                  name: 'Gói chăm sóc da cao cấp',
                  description: 'Gói chăm sóc da toàn diện với các liệu pháp hiện đại',
                  price: {
                    listPrice: 1000000,
                    salePrice: 850000,
                    currency: 'VND'
                  },
                  duration: 90,
                  unit: 'phút',
                  features: ['Massage thư giãn', 'Đắp mặt nạ collagen', 'Tư vấn chuyên sâu'],
                  isActive: true,
                  isLimited: true,
                  maxSales: 100,
                  imageOperations: [
                    {
                      operation: 'add',
                      mediaId: 'dcc8f803-04fc-49b3-953a-e660505ac353'
                    }
                  ]
                }
              }
            ],
            images: [
              {
                operation: 'add',
                mediaId: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2'
              }
            ]
          }
        }
      },
      'update-delete-example': {
        summary: 'Ví dụ sửa và xóa packages/images',
        description: 'Ví dụ chỉ cập nhật thông tin và thực hiện các thao tác sửa/xóa packages và images',
        value: {
          basicInfo: {
            name: 'Dịch vụ Spa chăm sóc sức khỏe toàn diện - Đã cập nhật',
            description: 'Dịch vụ spa cao cấp với đội ngũ chuyên gia và thiết bị hiện đại - Phiên bản mới',
            tags: ['spa', 'chăm sóc sức khỏe', 'massage', 'thư giãn', 'cập nhật']
          },
          pricing: {
            price: {
              listPrice: 1800000,
              salePrice: 1500000,
              currency: 'VND'
            },
            typePrice: 'HAS_PRICE'
          },
          serviceInfo: {
            serviceType: 'spa',
            location: 'tại cửa hàng và tại nhà',
            providerName: 'Spa Luxury Beauty - Chi nhánh mới'
          },
          operations: {
            packages: [
              {
                operation: 'update',
                id: 1,
                data: {
                  name: 'Gói chăm sóc da cao cấp - Nâng cấp',
                  description: 'Gói chăm sóc da toàn diện với các liệu pháp hiện đại và công nghệ mới',
                  price: {
                    listPrice: 1200000,
                    salePrice: 1000000,
                    currency: 'VND'
                  },
                  duration: 120,
                  unit: 'phút',
                  features: [
                    'Massage thư giãn toàn thân',
                    'Đắp mặt nạ collagen cao cấp',
                    'Tư vấn chuyên sâu với chuyên gia',
                    'Chăm sóc da bằng công nghệ laser'
                  ],
                  isActive: true,
                  isLimited: true,
                  maxSales: 200,
                  imageOperations: [
                    {
                      operation: 'delete',
                      entityHasMediaId: 92
                    },
                    {
                      operation: 'add',
                      mediaId: 'new-package-image-id-123'
                    }
                  ]
                }
              },
              {
                operation: 'delete',
                id: 2
              }
            ],
            images: [
              {
                operation: 'delete',
                entityHasMediaId: 47
              },
              {
                operation: 'add',
                mediaId: 'new-product-image-id-456'
              }
            ]
          }
        }
      },
      'complete-example': {
        summary: 'Ví dụ hoàn chỉnh với tất cả fields',
        description: 'Ví dụ bao gồm tất cả các trường có thể có trong request body',
        value: {
          basicInfo: {
            name: 'Dịch vụ chăm sóc sức khỏe toàn diện - Premium',
            description: 'Dịch vụ chăm sóc sức khỏe cao cấp với đội ngũ chuyên gia quốc tế và thiết bị hiện đại nhất',
            productType: 'SERVICE',
            tags: ['chăm sóc sức khỏe', 'spa', 'massage', 'thư giãn', 'cao cấp', 'premium']
          },
          pricing: {
            price: {
              listPrice: 2000000,
              salePrice: 1700000,
              currency: 'VND'
            },
            typePrice: 'HAS_PRICE'
          },
          serviceInfo: {
            serviceType: 'spa',
            location: 'tại cửa hàng',
            providerName: 'Spa Luxury Beauty International'
          },
          customFields: [
            {
              customFieldId: 1,
              value: { value: 'Dịch vụ cao cấp' }
            },
            {
              customFieldId: 2,
              value: { value: 'Có chứng chỉ quốc tế' }
            }
          ],
          operations: {
            packages: [
              {
                operation: 'add',
                data: {
                  name: 'Gói VIP Premium',
                  description: 'Gói chăm sóc VIP với dịch vụ cao cấp nhất',
                  price: {
                    listPrice: 1500000,
                    salePrice: 1300000,
                    currency: 'VND'
                  },
                  duration: 120,
                  unit: 'phút',
                  features: ['Massage toàn thân', 'Chăm sóc da mặt', 'Tư vấn dinh dưỡng', 'Phòng VIP riêng'],
                  isActive: true,
                  isLimited: true,
                  maxSales: 50,
                  imageOperations: [
                    {
                      operation: 'add',
                      mediaId: 'dcc8f803-04fc-49b3-953a-e660505ac353'
                    }
                  ]
                }
              },
              {
                operation: 'update',
                id: 123,
                data: {
                  name: 'Gói Standard - Cập nhật',
                  price: {
                    listPrice: 800000,
                    salePrice: 700000,
                    currency: 'VND'
                  },
                  isLimited: true,
                  maxSales: 150,
                  imageOperations: [
                    {
                      operation: 'delete',
                      entityHasMediaId: 456
                    }
                  ]
                }
              },
              {
                operation: 'delete',
                id: 124
              }
            ],
            images: [
              {
                operation: 'add',
                mediaId: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2'
              },
              {
                operation: 'delete',
                entityHasMediaId: 789
              }
            ]
          }
        }
      }
    }
  })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm dịch vụ cần cập nhật',
    example: 123,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật sản phẩm dịch vụ thành công',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Cập nhật sản phẩm dịch vụ thành công' },
        result: { $ref: '#/components/schemas/CompleteServiceProductResponseDto' }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Không có quyền truy cập',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Không có quyền truy cập' },
        result: { type: 'null', example: null }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền cập nhật sản phẩm này',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 403 },
        message: { type: 'string', example: 'Không có quyền cập nhật sản phẩm này' },
        result: { type: 'null', example: null }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm dịch vụ',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Không tìm thấy sản phẩm dịch vụ' },
        result: { type: 'null', example: null }
      }
    }
  })
  async updateComplete(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: CompleteUpdateServiceProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.completeServiceProductService.updateCompleteServiceProduct(
      id,
      updateDto,
      userId,
    );
    return ApiResponseDto.success<CompleteServiceProductResponseDto>(
      product,
      'Cập nhật sản phẩm dịch vụ thành công',
    );
  }

  /**
   * Lấy chi tiết hoàn chỉnh sản phẩm dịch vụ
   * @param id ID của sản phẩm dịch vụ
   * @param userId ID của người dùng hiện tại
   * @returns Chi tiết hoàn chỉnh sản phẩm dịch vụ
   */
  @Get(':id/complete')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy chi tiết hoàn chỉnh sản phẩm dịch vụ',
    description: `Lấy thông tin chi tiết hoàn chỉnh sản phẩm dịch vụ bao gồm:
    - Thông tin cơ bản từ customer_products
    - Thông tin dịch vụ từ service_products
    - Danh sách gói dịch vụ từ service_packages_option
    - Danh sách images từ entity_has_media`,
  })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm dịch vụ',
    example: 123,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy chi tiết sản phẩm dịch vụ thành công',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Lấy chi tiết sản phẩm dịch vụ thành công' },
        result: { $ref: '#/components/schemas/CompleteServiceProductResponseDto' }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Không có quyền truy cập',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Không có quyền truy cập' },
        result: { type: 'null', example: null }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền xem sản phẩm này',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 403 },
        message: { type: 'string', example: 'Không có quyền xem sản phẩm này' },
        result: { type: 'null', example: null }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm dịch vụ',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Không tìm thấy sản phẩm dịch vụ' },
        result: { type: 'null', example: null }
      }
    }
  })
  async getCompleteDetail(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.completeServiceProductService.getCompleteServiceProduct(
      id,
      userId,
    );
    return ApiResponseDto.success<CompleteServiceProductResponseDto>(
      product,
      'Lấy chi tiết sản phẩm dịch vụ thành công',
    );
  }
}
