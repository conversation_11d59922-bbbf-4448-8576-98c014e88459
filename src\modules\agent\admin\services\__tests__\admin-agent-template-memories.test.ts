import { Test, TestingModule } from '@nestjs/testing';
import { AdminAgentTemplateService } from '../admin-agent-template.service';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { AgentTemplateRepository } from '@modules/agent/repositories/agent-template.repository';
import { AgentMemoriesRepository } from '@modules/agent/repositories/agent-memories.repository';
import { TypeAgentRepository } from '@modules/agent/repositories/type-agent.repository';
import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { CdnService } from '@shared/services/cdn.service';
import { S3Service } from '@shared/services/s3.service';
import { CreateAgentTemplateDto } from '../../dto/agent-template/create-agent-template.dto';
import { AgentTemplateStatus } from '@modules/agent/constants';

describe('AdminAgentTemplateService - Memories Logic', () => {
  let service: AdminAgentTemplateService;
  let agentRepository: jest.Mocked<AgentRepository>;
  let agentTemplateRepository: jest.Mocked<AgentTemplateRepository>;
  let agentMemoriesRepository: jest.Mocked<AgentMemoriesRepository>;
  let typeAgentRepository: jest.Mocked<TypeAgentRepository>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminAgentTemplateService,
        {
          provide: AgentRepository,
          useValue: {
            save: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: AgentTemplateRepository,
          useValue: {
            save: jest.fn(),
            findById: jest.fn(),
            findByIdWithDetails: jest.fn(),
          },
        },
        {
          provide: AgentMemoriesRepository,
          useValue: {
            save: jest.fn(),
            findByAgentId: jest.fn(),
            deleteByAgentId: jest.fn(),
          },
        },
        {
          provide: TypeAgentRepository,
          useValue: {
            findById: jest.fn(),
          },
        },
        {
          provide: EmployeeInfoService,
          useValue: {
            getEmployeeInfo: jest.fn(),
          },
        },
        {
          provide: CdnService,
          useValue: {
            generateUrlView: jest.fn(),
          },
        },
        {
          provide: S3Service,
          useValue: {
            createPresignedWithID: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AdminAgentTemplateService>(AdminAgentTemplateService);
    agentRepository = module.get(AgentRepository);
    agentTemplateRepository = module.get(AgentTemplateRepository);
    agentMemoriesRepository = module.get(AgentMemoriesRepository);
    typeAgentRepository = module.get(TypeAgentRepository);
  });

  describe('create', () => {
    it('should save memories when creating agent template', async () => {
      // Arrange
      const createDto: CreateAgentTemplateDto = {
        name: 'Test Agent',
        typeId: 1,
        modelConfig: { temperature: 0.7 },
        memories: [
          {
            title: 'Test Memory 1',
            reason: 'For testing',
            content: 'This is a test memory',
          },
          {
            title: 'Test Memory 2',
            reason: 'For validation',
            content: 'This is another test memory',
          },
        ],
      };

      const savedAgent = { id: 'agent-uuid-123' };
      const typeAgent = { id: 1, name: 'Test Type' };

      typeAgentRepository.findById.mockResolvedValue(typeAgent);
      agentRepository.save.mockResolvedValue(savedAgent);
      agentTemplateRepository.save.mockResolvedValue({});
      agentMemoriesRepository.save.mockResolvedValue([]);

      // Act
      const result = await service.create(createDto, 1);

      // Assert
      expect(agentMemoriesRepository.save).toHaveBeenCalledWith([
        {
          agentId: 'agent-uuid-123',
          structuredContent: {
            title: 'Test Memory 1',
            reason: 'For testing',
            content: 'This is a test memory',
          },
          createdAt: expect.any(Number),
        },
        {
          agentId: 'agent-uuid-123',
          structuredContent: {
            title: 'Test Memory 2',
            reason: 'For validation',
            content: 'This is another test memory',
          },
          createdAt: expect.any(Number),
        },
      ]);
      expect(result.id).toBe('agent-uuid-123');
    });

    it('should not save memories when none provided', async () => {
      // Arrange
      const createDto: CreateAgentTemplateDto = {
        name: 'Test Agent',
        typeId: 1,
        modelConfig: { temperature: 0.7 },
        // No memories provided
      };

      const savedAgent = { id: 'agent-uuid-123' };
      const typeAgent = { id: 1, name: 'Test Type' };

      typeAgentRepository.findById.mockResolvedValue(typeAgent);
      agentRepository.save.mockResolvedValue(savedAgent);
      agentTemplateRepository.save.mockResolvedValue({});

      // Act
      await service.create(createDto, 1);

      // Assert
      expect(agentMemoriesRepository.save).not.toHaveBeenCalled();
    });
  });

  describe('findById', () => {
    it('should include memories in detail response', async () => {
      // Arrange
      const templateId = 'template-uuid-123';
      const template = {
        id: templateId,
        typeId: 1,
        status: AgentTemplateStatus.DRAFT,
        createdBy: 1,
        updatedBy: 1,
        agent: {
          id: templateId,
          name: 'Test Agent',
          modelConfig: { temperature: 0.7 },
        },
        typeAgent: {
          name: 'Test Type',
        },
      };

      const memories = [
        {
          id: 'memory-1',
          agentId: templateId,
          structuredContent: {
            title: 'Memory 1',
            reason: 'Test reason',
            content: 'Test content 1',
          },
        },
        {
          id: 'memory-2',
          agentId: templateId,
          structuredContent: {
            title: 'Memory 2',
            reason: 'Test reason 2',
            content: 'Test content 2',
          },
        },
      ];

      agentTemplateRepository.findByIdWithDetails.mockResolvedValue(template);
      agentMemoriesRepository.findByAgentId.mockResolvedValue(memories);

      // Act
      const result = await service.findById(templateId);

      // Assert
      expect(agentMemoriesRepository.findByAgentId).toHaveBeenCalledWith(templateId);
      expect(result.memories).toEqual([
        {
          title: 'Memory 1',
          reason: 'Test reason',
          content: 'Test content 1',
        },
        {
          title: 'Memory 2',
          reason: 'Test reason 2',
          content: 'Test content 2',
        },
      ]);
    });
  });
});
