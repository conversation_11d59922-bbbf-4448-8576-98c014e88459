import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { UserRagApiKey } from '../entities/user-rag-api-key.entity';

/**
 * Repository cho UserRagApiKey entity
 * Xử lý các thao tác database liên quan đến RAG API key của user
 */
@Injectable()
export class UserRagApiKeyRepository extends Repository<UserRagApiKey> {
  constructor(private dataSource: DataSource) {
    super(UserRagApiKey, dataSource.createEntityManager());
  }

  /**
   * Lấy RAG API key của user
   * @param userId ID của user
   * @returns RAG API key của user hoặc null nếu không có
   */
  async findByUserId(userId: number): Promise<UserRagApiKey | null> {
    return this.findOne({
      where: { userId }
    });
  }

  /**
   * Tạo hoặc cập nhật RAG API key cho user
   * @param userId ID của user
   * @param ragApiKey RAG API key mới
   * @returns UserRagApiK<PERSON> đã lưu
   */
  async upsertUserApiKey(userId: number, ragApiKey: string): Promise<UserRagApiKey> {
    // Xóa API key cũ của user (nếu có)
    await this.delete({ userId });
    
    // Tạo API key mới
    const userRagApiKey = this.create({
      userId,
      ragApiKey,
    });
    
    return this.save(userRagApiKey);
  }

  /**
   * Xóa RAG API key của user
   * @param userId ID của user
   */
  async deleteByUserId(userId: number): Promise<void> {
    await this.delete({ userId });
  }

  /**
   * Kiểm tra xem API key có tồn tại không
   * @param ragApiKey RAG API key cần kiểm tra
   * @returns true nếu API key tồn tại
   */
  async existsByApiKey(ragApiKey: string): Promise<boolean> {
    const count = await this.count({
      where: { ragApiKey }
    });
    return count > 0;
  }

  /**
   * Lấy user ID từ RAG API key
   * @param ragApiKey RAG API key
   * @returns UserRagApiKey hoặc null nếu không tìm thấy
   */
  async findByApiKey(ragApiKey: string): Promise<UserRagApiKey | null> {
    return this.findOne({
      where: { ragApiKey }
    });
  }
}
