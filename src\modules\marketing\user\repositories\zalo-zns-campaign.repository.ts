import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { ZaloZnsCampaign, ZaloZnsCampaignStatus } from '../entities/zalo-zns-campaign.entity';
import { PaginatedResult } from '@/common/response';

/**
 * Repository cho ZNS Campaign
 */
@Injectable()
export class ZaloZnsCampaignRepository {
  constructor(
    @InjectRepository(ZaloZnsCampaign)
    private readonly repository: Repository<ZaloZnsCampaign>,
  ) {}

  /**
   * Tạo chiến dịch ZNS mới
   */
  async create(data: Partial<ZaloZnsCampaign>): Promise<ZaloZnsCampaign> {
    const campaign = this.repository.create(data);
    return this.repository.save(campaign);
  }

  /**
   * Tìm chiến dịch theo ID
   */
  async findById(id: number): Promise<ZaloZnsCampaign | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm chiến dịch theo user ID và campaign ID
   */
  async findByUserIdAndId(userId: number, id: number): Promise<ZaloZnsCampaign | null> {
    return this.repository.findOne({ where: { userId, id } });
  }

  /**
   * Lấy danh sách chiến dịch với phân trang
   */
  async findWithPagination(
    userId: number,
    oaId: string,
    page: number = 1,
    limit: number = 10,
    search?: string,
    status?: ZaloZnsCampaignStatus,
  ): Promise<PaginatedResult<ZaloZnsCampaign>> {
    const queryBuilder = this.repository.createQueryBuilder('campaign');
    
    queryBuilder.where('campaign.userId = :userId', { userId });
    queryBuilder.andWhere('campaign.oaId = :oaId', { oaId });

    if (search) {
      queryBuilder.andWhere(
        '(campaign.name ILIKE :search OR campaign.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    if (status) {
      queryBuilder.andWhere('campaign.status = :status', { status });
    }

    queryBuilder.orderBy('campaign.createdAt', 'DESC');

    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Cập nhật chiến dịch
   */
  async update(id: number, data: Partial<ZaloZnsCampaign>): Promise<ZaloZnsCampaign | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa chiến dịch
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return (result.affected ?? 0) > 0;
  }

  /**
   * Lấy danh sách chiến dịch theo trạng thái
   */
  async findByStatus(status: ZaloZnsCampaignStatus): Promise<ZaloZnsCampaign[]> {
    return this.repository.find({ where: { status } });
  }

  /**
   * Lấy danh sách chiến dịch cần chạy (scheduled và thời gian đã đến)
   */
  async findScheduledCampaigns(currentTime: number): Promise<ZaloZnsCampaign[]> {
    return this.repository
      .createQueryBuilder('campaign')
      .where('campaign.status = :status', { status: ZaloZnsCampaignStatus.SCHEDULED })
      .andWhere('campaign.scheduledAt <= :currentTime', { currentTime })
      .getMany();
  }

  /**
   * Cập nhật thống kê chiến dịch
   */
  async updateStats(
    id: number,
    sentMessages: number,
    failedMessages: number,
  ): Promise<void> {
    await this.repository.update(id, {
      sentMessages,
      failedMessages,
      updatedAt: Date.now(),
    });
  }

  /**
   * Đánh dấu chiến dịch hoàn thành
   */
  async markAsCompleted(id: number): Promise<void> {
    const now = Date.now();
    await this.repository.update(id, {
      status: ZaloZnsCampaignStatus.COMPLETED,
      completedAt: now,
      updatedAt: now,
    });
  }

  /**
   * Đánh dấu chiến dịch thất bại
   */
  async markAsFailed(id: number, errorMessage: string): Promise<void> {
    const now = Date.now();
    await this.repository.update(id, {
      status: ZaloZnsCampaignStatus.FAILED,
      errorMessage,
      updatedAt: now,
    });
  }
}
