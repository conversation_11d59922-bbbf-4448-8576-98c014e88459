import { AgentTemplateStatus } from '@modules/agent/constants';
import { ConvertConfig } from '@modules/agent/interfaces/convert-config.interface';
import { ProfileAgent } from '@modules/agent/interfaces/profile-agent.interface';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested
} from 'class-validator';
import { ModelConfigDto } from '../agent-system';
import { AgentMemoryDto } from './agent-memory.dto';
import { ConversionConfigDto } from './conversion-config.dto';

/**
 * DTO cho việc cập nhật agent template
 */
export class UpdateAgentTemplateDto {
  /**
   * Tên hiển thị của agent
   */
  @ApiPropertyOptional({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  @IsString()
  @IsOptional()
  name?: string;

  /**
   * MIME type của avatar
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * Cấu hình model AI
   */
  @ApiPropertyOptional({
    description: 'Cấu hình model AI',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  @IsObject()
  @IsOptional()
  modelConfig?: ModelConfigDto;

  /**
   * Thông tin hồ sơ mẫu
   */
  @ApiPropertyOptional({
    description: 'Thông tin hồ sơ mẫu',
    type: Object,
  })
  @IsObject()
  @IsOptional()
  profile?: ProfileAgent;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example: 'Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  @IsOptional()
  instruction?: string;

  /**
   * ID của vector store
   */
  @ApiPropertyOptional({
    description: 'ID của vector store',
    example: 'vector-store-1',
  })
  @IsString()
  @IsOptional()
  vectorStoreId?: string;

  /**
   * Trạng thái của agent template
   */
  @ApiPropertyOptional({
    description: 'Trạng thái của agent template',
    enum: AgentTemplateStatus,
    example: AgentTemplateStatus.ACTIVE,
  })
  @IsEnum(AgentTemplateStatus)
  @IsOptional()
  status?: AgentTemplateStatus;

  /**
   * Cấu hình chuyển đổi (legacy)
   */
  @ApiPropertyOptional({
    description: 'Cấu hình chuyển đổi (legacy)',
    type: Object,
  })
  @IsObject()
  @IsOptional()
  convertConfig?: ConvertConfig;

  /**
   * Cấu hình conversion mới
   */
  @ApiPropertyOptional({
    description: 'Cấu hình conversion mới',
    type: [ConversionConfigDto],
    example: [
      {
        name: 'email',
        type: 'string',
        description: 'Địa chỉ email người dùng',
        required: true,
        active: true
      }
    ]
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ConversionConfigDto)
  conversion?: ConversionConfigDto[];

  /**
   * ID của loại agent
   */
  @ApiPropertyOptional({
    description: 'ID của loại agent',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  typeId?: number;

  /**
   * ID của base model (legacy)
   */
  @ApiPropertyOptional({
    description: 'ID của base model (legacy)',
    example: 'base-model-uuid',
  })
  @IsString()
  @IsOptional()
  modelBaseId?: string;

  /**
   * ID của system model
   */
  @ApiPropertyOptional({
    description: 'ID của system model',
    example: 'model-system-uuid',
  })
  @IsString()
  @IsOptional()
  modelSystemId?: string;

  /**
   * Trạng thái có thể bán
   */
  @ApiPropertyOptional({
    description: 'Trạng thái có thể bán',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  isForSale?: boolean;

  /**
   * Memories của agent
   */
  @ApiPropertyOptional({
    description: 'Memories của agent',
    type: [AgentMemoryDto],
    example: [
      {
        title: 'memories',
        reason: 'memories',
        content: 'memories'
      }
    ]
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => AgentMemoryDto)
  memories?: AgentMemoryDto[];
}
