import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho template ZNS từ Zalo API
 */
export class ZaloZnsTemplateApiDto {
  @ApiProperty({
    description: 'ID template',
    example: '900025',
  })
  templateId: string;

  @ApiProperty({
    description: 'Tên template',
    example: 'Template_1',
  })
  templateName: string;

  @ApiProperty({
    description: 'Thời gian tạo template (Unix timestamp)',
    example: 1640995200,
  })
  createdTime: number;

  @ApiProperty({
    description: 'Trạng thái template',
    enum: ['ENABLE', 'PENDING_REVIEW', 'DELETE', 'REJECT', 'DISABLE'],
    example: 'ENABLE',
  })
  status: string;

  @ApiProperty({
    description: 'Chất lượng template',
    enum: ['HIGH', 'MEDIUM', 'LOW', 'UNDEFINED'],
    example: 'HIGH',
  })
  templateQuality: string;

  @ApiPropertyOptional({
    description: 'Lý do template có trạng thái hiện tại',
    example: 'Template đã được phê duyệt',
  })
  reason?: string;

  @ApiPropertyOptional({
    description: 'Danh sách các tham số của template',
    type: 'array',
    items: { type: 'object' },
  })
  listParams?: any[];

  @ApiPropertyOptional({
    description: 'Danh sách các button của template',
    type: 'array',
    items: { type: 'object' },
  })
  listButtons?: any[];

  @ApiPropertyOptional({
    description: 'Tag template',
    example: 'TRANSACTION',
  })
  templateTag?: string;

  @ApiPropertyOptional({
    description: 'Giá template',
    example: 500,
  })
  price?: number;
}

/**
 * DTO cho metadata của danh sách template từ Zalo API
 */
export class ZaloZnsTemplateApiMetaDto {
  @ApiProperty({
    description: 'Tổng số template',
    example: 300,
  })
  total: number;
}

/**
 * DTO cho kết quả trả về từ Zalo API
 */
export class ZaloZnsTemplateApiResultDto {
  @ApiProperty({
    description: 'Danh sách template',
    type: [ZaloZnsTemplateApiDto],
  })
  data: ZaloZnsTemplateApiDto[];

  @ApiProperty({
    description: 'Metadata chứa thông tin phân trang',
    type: ZaloZnsTemplateApiMetaDto,
  })
  metadata: ZaloZnsTemplateApiMetaDto;
}

/**
 * DTO cho kết quả phân trang template từ Zalo API
 */
export class ZaloZnsTemplateApiPageResultDto {
  @ApiProperty({
    description: 'Danh sách template',
    type: [ZaloZnsTemplateApiDto],
  })
  items: ZaloZnsTemplateApiDto[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    type: 'object',
    properties: {
      totalItems: { type: 'number', description: 'Tổng số template' },
      itemCount: { type: 'number', description: 'Số lượng template trên trang hiện tại' },
      itemsPerPage: { type: 'number', description: 'Số lượng template trên mỗi trang' },
      totalPages: { type: 'number', description: 'Tổng số trang' },
      currentPage: { type: 'number', description: 'Trang hiện tại' },
    },
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

/**
 * DTO cho tham số template ZNS
 */
export class ZaloZnsTemplateParamDto {
  @ApiProperty({
    description: 'Tên thuộc tính',
    example: 'customer_name',
  })
  name: string;

  @ApiProperty({
    description: 'Tính bắt buộc của thuộc tính',
    example: true,
  })
  require: boolean;

  @ApiProperty({
    description: 'Định dạng validate của thuộc tính',
    example: 'string',
  })
  type: string;

  @ApiProperty({
    description: 'Số ký tự tối đa',
    example: 100,
  })
  maxLength: number;

  @ApiProperty({
    description: 'Số ký tự tối thiểu',
    example: 1,
  })
  minLength: number;

  @ApiProperty({
    description: 'Có thể nhận giá trị rỗng hay không',
    example: false,
  })
  acceptNull: boolean;
}

/**
 * DTO cho button template ZNS
 */
export class ZaloZnsTemplateButtonDto {
  @ApiProperty({
    description: 'Loại button',
    example: 'oa.open.url',
  })
  type: string;

  @ApiProperty({
    description: 'Tiêu đề button',
    example: 'Xem chi tiết',
  })
  title: string;

  @ApiProperty({
    description: 'Payload của button',
    example: 'https://example.com/order/{{order_id}}',
  })
  payload: string;

  @ApiPropertyOptional({
    description: 'URL hình ảnh icon',
    example: 'https://example.com/icon.png',
  })
  image_icon?: string;
}

/**
 * DTO cho chi tiết template ZNS từ Zalo API
 */
export class ZaloZnsTemplateDetailApiDto {
  @ApiProperty({
    description: 'ID template',
    example: '900025',
  })
  templateId: string;

  @ApiProperty({
    description: 'Tên template',
    example: 'Template_1',
  })
  templateName: string;

  @ApiProperty({
    description: 'Trạng thái template',
    enum: ['ENABLE', 'PENDING_REVIEW', 'DELETE', 'REJECT', 'DISABLE'],
    example: 'ENABLE',
  })
  status: string;

  @ApiPropertyOptional({
    description: 'Lý do template có trạng thái hiện tại',
    example: 'Template đã được phê duyệt',
  })
  reason?: string;

  @ApiPropertyOptional({
    description: 'Danh sách các tham số của template',
    type: [ZaloZnsTemplateParamDto],
  })
  listParams?: ZaloZnsTemplateParamDto[];

  @ApiPropertyOptional({
    description: 'Danh sách các button của template',
    type: [ZaloZnsTemplateButtonDto],
  })
  listButtons?: ZaloZnsTemplateButtonDto[];

  @ApiPropertyOptional({
    description: 'Thời gian timeout của template (milliseconds)',
    example: 86400000,
  })
  timeout?: number;

  @ApiPropertyOptional({
    description: 'URL hình ảnh preview',
    example: 'https://example.com/preview.jpg',
  })
  previewUrl?: string;

  @ApiPropertyOptional({
    description: 'Thời gian tạo template (Unix timestamp)',
    example: 1640995200,
  })
  createdTime?: number;

  @ApiPropertyOptional({
    description: 'Thời gian cập nhật template (Unix timestamp)',
    example: 1640995200,
  })
  updatedTime?: number;

  @ApiPropertyOptional({
    description: 'Chất lượng template',
    enum: ['HIGH', 'MEDIUM', 'LOW', 'UNDEFINED'],
    example: 'HIGH',
  })
  templateQuality?: string;

  @ApiPropertyOptional({
    description: 'Tag template',
    example: 'TRANSACTION',
  })
  templateTag?: string;

  @ApiPropertyOptional({
    description: 'Giá template',
    example: 500,
  })
  price?: number;
}
