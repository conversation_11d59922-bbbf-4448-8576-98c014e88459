/**
 * Test script để kiểm tra avatar CDN URL trong API findOne
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3003/v1';

// Test token đơn giản (trong thực tế cần token thật)
const TEST_TOKEN = 'Bearer test-token';

async function testAvatarCDN() {
  console.log('🚀 Bắt đầu test Avatar CDN URL...\n');

  try {
    // Test 1: Tạo audience test
    console.log('📝 Test 1: Tạo audience test...');
    const createResponse = await axios.post(
      `${BASE_URL}/admin/marketing/audiences`,
      {
        name: 'Test User Avatar',
        email: '<EMAIL>',
        phone: '+84912345678'
      },
      {
        headers: {
          'Authorization': TEST_TOKEN,
          'Content-Type': 'application/json'
        },
        validateStatus: () => true // Không throw error cho status codes
      }
    );

    console.log('Create Response Status:', createResponse.status);
    console.log('Create Response:', JSON.stringify(createResponse.data, null, 2));

    if (createResponse.status === 201) {
      const audienceId = createResponse.data.data.id;
      console.log(`✅ Audience created with ID: ${audienceId}\n`);

      // Test 2: Test API findOne
      console.log('📖 Test 2: Test API findOne...');
      const findOneResponse = await axios.get(
        `${BASE_URL}/admin/marketing/audiences/${audienceId}`,
        {
          headers: {
            'Authorization': TEST_TOKEN
          },
          validateStatus: () => true
        }
      );

      console.log('FindOne Response Status:', findOneResponse.status);
      console.log('FindOne Response:', JSON.stringify(findOneResponse.data, null, 2));

      if (findOneResponse.status === 200) {
        const avatar = findOneResponse.data.data.avatar;
        console.log(`\n🔍 Avatar field: ${avatar}`);
        
        if (avatar === null) {
          console.log('✅ Avatar is null (expected for new audience)');
        } else if (avatar && avatar.startsWith('http')) {
          console.log('✅ Avatar is CDN URL:', avatar);
        } else {
          console.log('❌ Avatar is S3 key (not CDN URL):', avatar);
        }
      }

      // Test 3: Cập nhật với avatar
      console.log('\n📝 Test 3: Cập nhật audience với avatar...');
      const updateResponse = await axios.put(
        `${BASE_URL}/admin/marketing/audiences/${audienceId}`,
        {
          name: 'Test User Avatar Updated',
          email: '<EMAIL>',
          avatarMediaType: 'image/jpeg'
        },
        {
          headers: {
            'Authorization': TEST_TOKEN,
            'Content-Type': 'application/json'
          },
          validateStatus: () => true
        }
      );

      console.log('Update Response Status:', updateResponse.status);
      if (updateResponse.status === 200) {
        const updatedAvatar = updateResponse.data.data.avatar;
        console.log(`🔍 Updated Avatar field: ${updatedAvatar}`);
        
        if (updatedAvatar && updatedAvatar.startsWith('http')) {
          console.log('✅ Updated Avatar is CDN URL:', updatedAvatar);
        } else {
          console.log('❌ Updated Avatar is S3 key (not CDN URL):', updatedAvatar);
        }

        // Test 4: Test lại API findOne sau khi có avatar
        console.log('\n📖 Test 4: Test API findOne sau khi có avatar...');
        const finalFindResponse = await axios.get(
          `${BASE_URL}/admin/marketing/audiences/${audienceId}`,
          {
            headers: {
              'Authorization': TEST_TOKEN
            },
            validateStatus: () => true
          }
        );

        if (finalFindResponse.status === 200) {
          const finalAvatar = finalFindResponse.data.data.avatar;
          console.log(`🔍 Final Avatar field: ${finalAvatar}`);
          
          if (finalAvatar && finalAvatar.startsWith('http')) {
            console.log('✅ Final Avatar is CDN URL - TEST PASSED!');
          } else {
            console.log('❌ Final Avatar is S3 key - TEST FAILED!');
          }
        }
      }
    }

  } catch (error) {
    console.error('❌ Test error:', error.message);
    if (error.response) {
      console.error('Error Response:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Chạy test
testAvatarCDN();
