import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsNumber,
  IsObject,
  IsArray,
  ValidateNested,
  ValidateIf,
  MaxLength,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ShipmentConfigType } from '@modules/business/interfaces';
import { ProductImageOperationDto } from '../image-operations/image-operation.dto';

/**
 * Enum cho các loại thao tác biến thể
 */
export enum VariantOperationType {
  ADD = 'add',
  UPDATE = 'update',
  DELETE = 'delete',
}

/**
 * DTO cho dữ liệu biến thể trong thao tác
 */
export class VariantOperationDataDto {
  @ApiProperty({
    description: 'Tên biến thể',
    example: 'Áo thun đỏ size M',
    maxLength: 500,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(500)
  name: string;

  @ApiProperty({
    description: 'Mô tả biến thể',
    example: '<PERSON><PERSON> thun màu đỏ, size M, chất liệu cotton',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Mã SKU riêng của biến thể',
    example: 'SHIRT-RED-M-001',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  sku?: string;

  @ApiProperty({
    description: 'Mã vạch riêng của biến thể',
    example: '1234567890123',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  barcode?: string;

  @ApiProperty({
    description: 'Các thuộc tính biến thể (JSON)',
    example: {
      color: 'Đỏ',
      size: 'M',
      material: 'Cotton'
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  attributes?: any;

  @ApiProperty({
    description: 'Giá riêng cho biến thể',
    example: {
      listPrice: 500000,
      salePrice: 450000,
      currency: 'VND'
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  price?: any;

  @ApiProperty({
    description: 'Cấu hình vận chuyển riêng cho biến thể',
    example: {
      widthCm: 25,
      heightCm: 5,
      lengthCm: 30,
      weightGram: 200
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  shipmentConfig?: ShipmentConfigType;

  @ApiProperty({
    description: 'Thao tác với ảnh riêng cho biến thể này',
    type: [ProductImageOperationDto],
    example: [
      {
        operation: 'add',
        mediaId: '123e4567-e89b-12d3-a456-426614174000'
      },
      {
        operation: 'delete',
        key: 'variant-image.jpg'
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductImageOperationDto)
  imageOperations?: ProductImageOperationDto[];
}

/**
 * DTO cho thao tác biến thể sản phẩm vật lý
 */
export class PhysicalProductVariantOperationDto {
  @ApiProperty({
    description: 'Loại thao tác',
    enum: VariantOperationType,
    example: VariantOperationType.ADD,
  })
  @IsEnum(VariantOperationType)
  @IsNotEmpty()
  operation: VariantOperationType;

  @ApiProperty({
    description: 'ID biến thể (bắt buộc cho UPDATE và DELETE)',
    example: 123,
    required: false,
  })
  @ValidateIf((o) => o.operation === VariantOperationType.UPDATE || o.operation === VariantOperationType.DELETE)
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  id?: number;

  @ApiProperty({
    description: 'Dữ liệu biến thể (bắt buộc cho ADD và UPDATE)',
    type: VariantOperationDataDto,
    required: false,
  })
  @ValidateIf((o) => o.operation === VariantOperationType.ADD || o.operation === VariantOperationType.UPDATE)
  @ValidateNested()
  @Type(() => VariantOperationDataDto)
  data?: VariantOperationDataDto;
}
