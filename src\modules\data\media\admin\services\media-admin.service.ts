import {Injectable, Logger} from '@nestjs/common';
import { Media } from '../../entities/media.entity';
import {ApiResponseDto, PaginatedResult} from "@common/response";
import {MediaQueryDto} from "@/modules/data/media/dto/media-query.dto";
import {MediaRepository} from "@modules/data/media/repositories";
import {S3Service} from "@shared/services/s3.service";
import {AppException, ErrorCode} from "@/common";
import {AdminMediaResponseDto} from "@/modules/data/media/dto/media-admin.dto";
import {DataSource, In, Repository, DeepPartial} from "typeorm";
import {InjectRepository} from "@nestjs/typeorm";
import {User} from "@modules/user/entities";
import { AgentMediaRepository } from '@modules/agent/repositories/agent-media.repository';
import { OwnerTypeEnum } from '../../enums/owner-type.enum';
import { MediaMapper } from '../../mappers/media-admin.mapper';
import { UserRepository } from '@/modules/user/repositories';
import { CdnService } from '@/shared/services/cdn.service';
import { MediaValidationHelper } from '../../helpers/validation.helper';
import { MEDIA_ERROR_CODES } from '../../exception';
import { AgentMedia } from '@/modules/agent/entities/agents-media.entity';
import { chunk } from 'lodash';
import { MediaResponseDto, MediaDto } from '../../dto/media-user.dto';
import { MediaStatusEnum } from '../../enums/media-status.enum';
import { MediaTypeEnum } from '../../enums/media-type.enum';
import { Transactional } from 'typeorm-transactional';
import { DeleteAgentMediaDto } from '../../dto/delete-agent-media.dto';
import { UpdateMediaStatusDto, UpdateMultipleMediaStatusDto } from '../../dto/update-media-status.dto';
import {
  CategoryFolderEnum,
  FileSizeEnum,
  generateS3Key,
  ImageType,
  MediaType,
  MediaTypeUtil,
  TimeIntervalEnum,
  VideoType,
  AudioType,
} from '@/shared/utils';
import { RagFileProcessingService } from '@shared/services/ai/rag-file-processing.service';

/**
 * Interface định nghĩa cấu trúc trả về của phương thức resolveMediaConfig
 */
interface MediaConfig {
  mimeType: MediaType;
  expirationTime: TimeIntervalEnum;
  maxSize: FileSizeEnum;
  CategoryFolderEnum: CategoryFolderEnum;
}

@Injectable()
export class MediaAdminService {
  private log = new Logger(MediaAdminService.name);

  constructor(
      private readonly mediaRepository: MediaRepository,
      private readonly s3Service: S3Service,
      private readonly agentMediaRepository: AgentMediaRepository,
      private readonly userRepository: UserRepository,
      private readonly cdnService: CdnService,
      private readonly mediaValidationHelper: MediaValidationHelper,
      private readonly dataSource: DataSource,
      private readonly ragFileProcessingService: RagFileProcessingService,
  ) {}

  /**
   * Lấy danh sách media của người dùng
   * @param query Tham số truy vấn
   * @param isAdmin
   * @returns Danh sách media với phân trang
   */
  async findAllForAdmin(
      query: MediaQueryDto,
      isAdmin: boolean|undefined,
  ): Promise<PaginatedResult<AdminMediaResponseDto>> {

    this.mediaValidationHelper.isAdmin(isAdmin);
     // Lấy dữ liệu phân trang từ repository
    const paginatedResult = await this.mediaRepository.findAllForAdmin(query);
    const updatedMediaList = await MediaMapper.toAdminList(paginatedResult.items, this.cdnService);


    // Trả về kết quả phân trang đã xử lý
    return{
      items: updatedMediaList,
      meta: paginatedResult.meta,
  };
  }

  /**
   * Tìm media theo ID
   * @param id ID của media
   * @param isAdmin để check quyền admin
   * @returns Media nếu tìm thấy và là của người dùng
   */
  async findByIdForAdmin(
      id: string,
      isAdmin: boolean|undefined,
  ): Promise<AdminMediaResponseDto> {
    this.mediaValidationHelper.isAdmin(isAdmin);
    const media = await this.mediaRepository.findByIdConfig(id) as Media;

    this.mediaValidationHelper.validateMediaExists(media, id);


    return (await MediaMapper.toAdminDetail(media, this.s3Service)) ;
  }

  /**
   * Xóa liên kết nhiều agent với một media
   * @param dto DTO chứa thông tin xóa liên kết (mediaId và danh sách agentIds)
   * @param isAdmin để check quyền admin
   */
  @Transactional()
  async deleteAgentMediaByAdmin(
    dto: DeleteAgentMediaDto,
    isAdmin: boolean | undefined
  ): Promise<{ deletedIds: string[]; skippedIds: string[]; failedIds: { id: string; reason: string }[] }> {
    this.mediaValidationHelper.isAdmin(isAdmin);

    // Kiểm tra mediaId có tồn tại không
    if (!dto.mediaId) {
      throw new AppException(
        MEDIA_ERROR_CODES.BAD_REQUEST,
        'mediaId không được để trống'
      );
    }

    // Nếu không có agentIds, trả về kết quả trống
    if (!dto.agentIds || dto.agentIds.length === 0) {
      return {
        deletedIds: [],
        skippedIds: [],
        failedIds: []
      };
    }

    // Xóa nhiều agent khỏi một media
    return this.deleteAgentsFromMedia(dto.mediaId, dto.agentIds);
  }

  /**
   * Xóa nhiều agent khỏi một media
   * @param mediaId ID của media
   * @param agentIds Danh sách ID của các agent cần xóa liên kết
   */
  private async deleteAgentsFromMedia(
    mediaId: string,
    agentIds: string[]
  ): Promise<{ deletedIds: string[]; skippedIds: string[]; failedIds: { id: string; reason: string }[] }> {
    const deletedIds: string[] = [];
    const skippedIds: string[] = [];
    const failedIds: { id: string; reason: string }[] = [];

    // Kiểm tra media có tồn tại không
    const media = await this.mediaRepository.findOne({
      where: { id: mediaId },
      select: ['id'],
    });

    if (!media) {
      return {
        deletedIds: [],
        skippedIds: [],
        failedIds: [{ id: mediaId, reason: 'Media không tồn tại' }]
      };
    }

    // Xóa từng liên kết agent-media
    for (const agentId of agentIds) {
      try {
        const result = await this.agentMediaRepository.delete({
          mediaId,
          agentId
        });

        if (result.affected && result.affected > 0) {
          deletedIds.push(agentId);
        } else {
          skippedIds.push(agentId);
        }
      } catch (error) {
        this.log.error(`Lỗi khi xóa liên kết agent ${agentId} với media ${mediaId}: ${error.message}`, error.stack);
        failedIds.push({ id: agentId, reason: error?.message ?? 'Unknown error' });
      }
    }

    return { deletedIds, skippedIds, failedIds };
  }

  /**
   * Xóa tất cả liên kết của một media với các agent
   * @param mediaId ID của media
   * @param isAdmin để check quyền admin
   */
  @Transactional()
  async deleteAllAgentMediaByAdmin(
    mediaId: string,
    isAdmin: boolean | undefined
  ): Promise<{ deletedCount: number; mediaId: string }> {
    this.mediaValidationHelper.isAdmin(isAdmin);

    // Kiểm tra media có tồn tại không
    const media = await this.mediaRepository.findOne({
      where: { id: mediaId },
      select: ['id'],
    });

    if (!media) {
      throw new AppException(
        MEDIA_ERROR_CODES.NOT_FOUND,
        `Media với id ${mediaId} không tồn tại`
      );
    }

    try {
      // Xóa tất cả liên kết của media này với các agent
      const result = await this.agentMediaRepository.delete({ mediaId });

      return {
        deletedCount: result.affected || 0,
        mediaId
      };
    } catch (error) {
      this.log.error(`Lỗi khi xóa tất cả liên kết của media ${mediaId}: ${error.message}`, error.stack);
      throw new AppException(
        MEDIA_ERROR_CODES.BAD_REQUEST,
        `Lỗi khi xóa liên kết: ${error.message}`
      );
    }
  }

  /**
   * Xóa mềm nhiều media theo danh sách ID (cập nhật trạng thái thành DELETED)
   * @param mediaIds Danh sách ID của media
   * @param isAdmin để check quyền admin
   * @returns Danh sách các ID đã xóa thành công và các lỗi nếu có
   */
  @Transactional()
  async deleteManyMediaByAdmin(
    mediaIds: string[],
    isAdmin: boolean | undefined,
  ): Promise<{
    deletedIds: string[],
    skippedIds: string[],
    failedIds: { id: string; reason: string }[]
  }> {
    this.mediaValidationHelper.isAdmin(isAdmin);
    this.mediaValidationHelper
    .validateMediaArray(
      mediaIds,
      'Danh sách id không được rỗng',
    );
    const deletedIds: string[] = [];
    const skippedIds: string[] = [];
    const failedIds: { id: string; reason: string }[] = [];

    const CHUNK_SIZE = 1000;
    const mediaIdChunks = chunk(mediaIds, CHUNK_SIZE);

    for (const batch of mediaIdChunks) {
      const medias = await this.mediaRepository.find({
        where: { id: In(batch) }
      });

      const mediaMap = new Map(medias.map(m => [m.id, m]));

      for (const mediaId of batch) {
        const media = mediaMap.get(mediaId);
        if (!media) {
          skippedIds.push(mediaId);
          continue;
        }

        try {
          // Cập nhật trạng thái thành DELETED thay vì xóa hoàn toàn
          await this.mediaRepository.update(
            { id: mediaId },
            {
              status: MediaStatusEnum.DELETED,
              updatedAt: Date.now()
            }
          );

          deletedIds.push(mediaId);
        } catch (error) {
          this.log.error(`Lỗi khi xóa mềm media ${mediaId}: ${error.message}`, error.stack);
          failedIds.push({ id: mediaId, reason: error.message });
        }
      }
    }

    return { deletedIds, skippedIds, failedIds };
  }

  /**
   * Tạo presigned URLs cho danh sách media (Admin)
   * @param mediaList Danh sách media cần tạo presigned URLs
   * @param employeeId ID của employee (admin)
   * @returns Danh sách presigned URLs
   */
  async createPresignedUrlsFromMediaList(
    mediaList: MediaDto[],
    employeeId: number,
  ): Promise<string[]> {
    const presignedUrls: string[] = [];
    const mediaEntities: DeepPartial<Media>[] = [];
    const mediaWithMimeTypes: Array<{ entity: DeepPartial<Media>; mimeType: string }> = [];

    for (const media of mediaList) {
      // Validate MIME type - name là tên mô tả, không phải file name
      this.mediaValidationHelper.validateAllowedMimeType(media.type);

      const { mimeType, expirationTime, maxSize, CategoryFolderEnum } =
        this.resolveMediaConfig(media.type);

      // Tạo file name từ name và type
      const extension = this.getFileExtensionFromMimeType(media.type);
      const fileName = `${media.name.replace(/[^a-zA-Z0-9]/g, '_')}.${extension}`;

      const storageKey = generateS3Key({
        baseFolder: 'media',
        categoryFolder: CategoryFolderEnum,
        fileName: fileName,
        prefix: `admin_${employeeId}`,
        useTimeFolder: true,
      });

      // Kiểm tra kích thước file
      this.mediaValidationHelper.validateMediaSize(
        media.size,
        maxSize,
      );

      const presignedUrl = await this.s3Service.createPresignedWithID(
        storageKey,
        expirationTime,
        mimeType,
        maxSize,
      );

      presignedUrls.push(presignedUrl);

      const mediaEntity: DeepPartial<Media> = {
        name: media.name,
        description: media.description, // Có thể null/undefined, entity đã hỗ trợ nullable
        size: media.size,
        tags: media.tags,
        ownedBy: employeeId,
        ownerType: OwnerTypeEnum.ADMIN,
        status: MediaStatusEnum.PENDING,
        storageKey,
        mediaType: this.getMediaTypeFromMimeType(media.type), // Xác định loại media từ MIME type
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      mediaEntities.push(mediaEntity);
      mediaWithMimeTypes.push({ entity: mediaEntity, mimeType: media.type });
    }

    await this.mediaRepository.save(mediaEntities);

    // Gửi thông tin media sang RAG API để xử lý embedding (background, không chờ kết quả)
    this.processMediaWithRAG(mediaWithMimeTypes);

    return presignedUrls;
  }

  /**
   * Xác định cấu hình media dựa trên loại media
   * Chỉ hỗ trợ Image, Video và Audio - không hỗ trợ Document files
   * @param type Loại media
   * @returns Cấu hình media bao gồm mimeType, expirationTime, maxSize, và CategoryFolderEnum
   * @throws AppException nếu loại media không được hỗ trợ
   */
  private resolveMediaConfig(type: string): MediaConfig {
    // Thử xử lý như hình ảnh
    try {
      const typeImage: MediaType = ImageType.getType(type);
      return {
        mimeType: typeImage,
        expirationTime: TimeIntervalEnum.FIVE_MINUTES,
        maxSize: FileSizeEnum.FIVE_MB,
        CategoryFolderEnum: CategoryFolderEnum.IMAGE,
      };
    } catch (error) {
      // Thử xử lý như video
      try {
        const typeVideo: MediaType = VideoType.getType(type);
        return {
          mimeType: typeVideo,
          expirationTime: TimeIntervalEnum.FIFTEEN_MINUTES,
          maxSize: FileSizeEnum.FIFTY_MB,
          CategoryFolderEnum: CategoryFolderEnum.VIDEO,
        };
      } catch (error) {
        // Thử xử lý như audio
        try {
          const typeAudio: MediaType = AudioType.getType(type);
          return {
            mimeType: typeAudio,
            expirationTime: TimeIntervalEnum.FIFTEEN_MINUTES,
            maxSize: FileSizeEnum.TWENTY_MB,
            CategoryFolderEnum: CategoryFolderEnum.AUDIO,
          };
        } catch (innerError) {
          // Nếu tất cả các trường hợp đều thất bại, ném lỗi
          throw new AppException(
            MEDIA_ERROR_CODES.FILE_TYPE_NOT_FOUND,
            `Loại media không được hỗ trợ. Chỉ hỗ trợ hình ảnh, video và audio: ${type}`
          );
        }
      }
    }
  }

  /**
   * Xác định MediaTypeEnum từ MIME type sử dụng shared utils
   * @param mimeType MIME type string (ví dụ: 'image/jpeg', 'video/mp4')
   * @returns MediaTypeEnum tương ứng
   * @throws Error if MIME type is not supported in media module
   */
  private getMediaTypeFromMimeType(mimeType: string): MediaTypeEnum {
    // Sử dụng MediaTypeUtil để kiểm tra loại media được phép
    if (!MediaTypeUtil.isAllowedInMediaModule(mimeType)) {
      throw new Error(`MIME type ${mimeType} is not allowed in media module`);
    }

    if (mimeType.startsWith('image/')) {
      return MediaTypeEnum.IMAGE;
    } else if (mimeType.startsWith('video/')) {
      return MediaTypeEnum.VIDEO;
    } else if (mimeType.startsWith('audio/')) {
      return MediaTypeEnum.AUDIO;
    }

    // Không bao giờ đến đây vì MediaTypeUtil.isAllowedInMediaModule đã kiểm tra
    throw new Error(`Unsupported MIME type: ${mimeType}`);
  }

  /**
   * Lấy file extension từ MIME type
   * @param mimeType MIME type
   * @returns File extension
   */
  private getFileExtensionFromMimeType(mimeType: string): string {
    const mimeToExtension: Record<string, string> = {
      // Image types
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/gif': 'gif',
      'image/webp': 'webp',

      // Video types
      'video/mp4': 'mp4',
      'video/webm': 'webm',
      'video/quicktime': 'mov',
      'video/x-msvideo': 'avi',
      'video/x-matroska': 'mkv',
      'video/x-flv': 'flv',
      'video/x-ms-wmv': 'wmv',
      'video/mpeg': 'mpg',
      'video/3gpp': '3gp',

      // Audio types
      'audio/mpeg': 'mp3',
      'audio/wav': 'wav',
      'audio/ogg': 'ogg',
      'audio/aac': 'aac',
      'audio/flac': 'flac',
      'audio/mp4': 'm4a',
      'audio/x-ms-wma': 'wma',
      'audio/webm': 'webm',
    };

    return mimeToExtension[mimeType] || 'bin';
  }

  /**
   * Cập nhật trạng thái của một media
   * @param id ID của media
   * @param dto DTO chứa thông tin cập nhật trạng thái
   * @param isAdmin để check quyền admin
   * @returns Thông tin media đã cập nhật
   */
  @Transactional()
  async updateMediaStatus(
    id: string,
    dto: UpdateMediaStatusDto,
    isAdmin: boolean | undefined,
  ): Promise<{ id: string; status: MediaStatusEnum; updatedAt: number }> {
    this.mediaValidationHelper.isAdmin(isAdmin);

    // Kiểm tra media có tồn tại không
    const media = await this.mediaRepository.findOne({
      where: { id },
      select: ['id', 'status'],
    });

    if (!media) {
      throw new AppException(
        MEDIA_ERROR_CODES.NOT_FOUND,
        `Media với id ${id} không tồn tại`
      );
    }

    // Kiểm tra trạng thái hiện tại có thể chuyển đổi không
    this.validateStatusTransition(media.status, dto.status);

    const now = Date.now();

    try {
      // Cập nhật trạng thái media
      await this.mediaRepository.update(
        { id },
        {
          status: dto.status,
          updatedAt: now,
        }
      );

      this.log.log(`Media ${id} status updated to ${dto.status} by admin`);

      return {
        id,
        status: dto.status,
        updatedAt: now,
      };
    } catch (error) {
      this.log.error(`Lỗi khi cập nhật trạng thái media ${id}: ${error.message}`, error.stack);
      throw new AppException(
        MEDIA_ERROR_CODES.GENERAL_ERROR,
        `Không thể cập nhật trạng thái media: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật trạng thái của nhiều media
   * @param dto DTO chứa danh sách media IDs và trạng thái mới
   * @param isAdmin để check quyền admin
   * @returns Kết quả cập nhật
   */
  @Transactional()
  async updateMultipleMediaStatus(
    dto: UpdateMultipleMediaStatusDto,
    isAdmin: boolean | undefined,
  ): Promise<{
    updatedIds: string[];
    skippedIds: string[];
    failedIds: { id: string; reason: string }[];
  }> {
    this.mediaValidationHelper.isAdmin(isAdmin);

    const updatedIds: string[] = [];
    const skippedIds: string[] = [];
    const failedIds: { id: string; reason: string }[] = [];

    const CHUNK_SIZE = 1000;
    const mediaIdChunks = chunk(dto.mediaIds, CHUNK_SIZE);

    for (const batch of mediaIdChunks) {
      const medias = await this.mediaRepository.find({
        where: { id: In(batch) },
        select: ['id', 'status'],
      });

      const mediaMap = new Map(medias.map(m => [m.id, m]));

      for (const mediaId of batch) {
        const media = mediaMap.get(mediaId);
        if (!media) {
          skippedIds.push(mediaId);
          continue;
        }

        try {
          // Kiểm tra trạng thái có thể chuyển đổi không
          this.validateStatusTransition(media.status, dto.status);

          // Cập nhật trạng thái
          await this.mediaRepository.update(
            { id: mediaId },
            {
              status: dto.status,
              updatedAt: Date.now(),
            }
          );

          updatedIds.push(mediaId);
        } catch (error) {
          this.log.error(`Lỗi khi cập nhật trạng thái media ${mediaId}: ${error.message}`, error.stack);
          failedIds.push({ id: mediaId, reason: error.message });
        }
      }
    }

    this.log.log(`Bulk status update completed: ${updatedIds.length} updated, ${skippedIds.length} skipped, ${failedIds.length} failed`);

    return { updatedIds, skippedIds, failedIds };
  }

  /**
   * Validate status transition rules
   * @param currentStatus Current media status
   * @param newStatus New media status
   */
  private validateStatusTransition(currentStatus: MediaStatusEnum, newStatus: MediaStatusEnum): void {
    // Không cho phép cập nhật media đã bị xóa
    if (currentStatus === MediaStatusEnum.DELETED) {
      throw new AppException(
        MEDIA_ERROR_CODES.BAD_REQUEST,
        'Không thể cập nhật trạng thái của media đã bị xóa'
      );
    }

    // Không cho phép chuyển sang trạng thái DELETED thông qua API này
    if (newStatus === MediaStatusEnum.DELETED) {
      throw new AppException(
        MEDIA_ERROR_CODES.BAD_REQUEST,
        'Không thể chuyển trạng thái sang DELETED thông qua API này. Vui lòng sử dụng API xóa media.'
      );
    }

    // Không cho phép chuyển sang trạng thái DRAFT
    if (newStatus === MediaStatusEnum.DRAFT) {
      throw new AppException(
        MEDIA_ERROR_CODES.BAD_REQUEST,
        'Không thể chuyển trạng thái sang DRAFT thông qua API này.'
      );
    }
  }

  /**
   * Kiểm tra xem media type có nên được gửi sang RAG API không
   * Chỉ image files được gửi sang RAG API
   * @param mimeType MIME type của media
   * @returns true nếu nên gửi sang RAG API
   */
  private shouldProcessWithRAG(mimeType: string): boolean {
    return mimeType.startsWith('image/');
  }

  /**
   * Gửi thông tin media sang RAG API để xử lý embedding (background processing)
   * @param mediaWithMimeTypes Danh sách media entities với MIME types
   */
  private processMediaWithRAG(mediaWithMimeTypes: Array<{ entity: DeepPartial<Media>; mimeType: string }>): void {
    // Xử lý từng media trong background
    for (const { entity: mediaEntity, mimeType } of mediaWithMimeTypes) {
      // Chỉ xử lý image files
      if (!this.shouldProcessWithRAG(mimeType)) {
        this.log.log(`Bỏ qua media "${mediaEntity.name}" (${mimeType}) - không phải image file`);
        continue;
      }

      // Fire-and-forget: chạy trong background không chờ kết quả
      setImmediate(async () => {
        try {
          // Gọi RAG API để xử lý embedding
          const mediaId = await this.ragFileProcessingService.processMediaMixed(
            mediaEntity.name || '',
            mediaEntity.description || null,
            mediaEntity.tags || [],
            mediaEntity.storageKey || '',
            2000, // chunk_size
            100   // chunk_overlap
          );

          // Nếu có media_id từ RAG API, cập nhật vào database
          if (mediaId && mediaEntity.id) {
            try {
              await this.mediaRepository.update(
                { id: mediaEntity.id },
                {
                  mediaId: mediaId,
                  updatedAt: Date.now()
                }
              );
              this.log.log(`Đã cập nhật media_id "${mediaId}" cho media "${mediaEntity.name}"`);
            } catch (updateError) {
              this.log.error(`Lỗi khi cập nhật media_id cho media "${mediaEntity.name}": ${updateError.message}`, updateError.stack);
            }
          }

          this.log.log(`Đã gửi media "${mediaEntity.name}" sang RAG API để xử lý embedding`);
        } catch (error) {
          // Log lỗi nhưng không throw để không ảnh hưởng flow chính
          this.log.error(`Lỗi khi gửi media "${mediaEntity.name}" sang RAG API: ${error.message}`, error.stack);
        }
      });
    }
  }
}
