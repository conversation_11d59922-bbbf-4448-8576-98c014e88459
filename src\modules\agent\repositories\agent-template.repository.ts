import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AgentTemplate } from '@modules/agent/entities';
import { PaginatedResult } from '@/common/response';
import { AgentTemplateStatus } from '@modules/agent/constants';

/**
 * Repository cho AgentTemplate
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến mẫu agent
 */
@Injectable()
export class AgentTemplateRepository extends Repository<AgentTemplate> {
  private readonly logger = new Logger(AgentTemplateRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentTemplate, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AgentTemplate
   * @returns SelectQueryBuilder cho AgentTemplate
   */
  private createBaseQuery(): SelectQueryBuilder<AgentTemplate> {
    return this.createQueryBuilder('agentTemplate');
  }

  /**
   * Tìm mẫu agent theo ID
   * @param id ID của mẫu agent
   * @returns AgentTemplate nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: string): Promise<AgentTemplate | null> {
    return this.createBaseQuery()
      .where('agentTemplate.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm mẫu agent theo ID với thông tin chi tiết (JOIN với agent và type_agent)
   * @param id ID của mẫu agent
   * @returns AgentTemplate với thông tin chi tiết nếu tìm thấy, null nếu không tìm thấy
   */
  async findByIdWithDetails(id: string): Promise<AgentTemplate & { agent: any; typeAgent: any } | null> {
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'agentTemplate.id AS template_id',
        'agentTemplate.type_id AS template_type_id',
        'agentTemplate.profile AS template_profile',
        'agentTemplate.convert_config AS template_convert_config',
        'agentTemplate.status AS template_status',
        'agentTemplate.model_base_id AS template_model_base_id',
        'agentTemplate.model_finetuning_id AS template_model_finetuning_id',
        'agentTemplate.is_for_sale AS template_is_for_sale',
        'agentTemplate.created_by AS template_created_by',
        'agentTemplate.updated_by AS template_updated_by',
        'agentTemplate.deleted_by AS template_deleted_by',
        'agent.id AS agent_id',
        'agent.name AS agent_name',
        'agent.avatar AS agent_avatar',
        'agent.model_config AS agent_model_config',
        'agent.instruction AS agent_instruction',
        'agent.status AS agent_status',
        'agent.created_at AS agent_created_at',
        'agent.updated_at AS agent_updated_at',
        'agent.deleted_at AS agent_deleted_at',
        'typeAgent.id AS type_agent_id',
        'typeAgent.name AS type_agent_name',
        'typeAgent.description AS type_agent_description',
      ])
      .from('agents_template', 'agentTemplate')
      .innerJoin('agents', 'agent', 'agent.id = agentTemplate.id')
      .leftJoin('type_agents', 'typeAgent', 'typeAgent.id = agentTemplate.type_id')
      .where('agentTemplate.id = :id', { id })
      .andWhere('agentTemplate.deleted_by IS NULL');

    const row = await qb.getRawOne();

    if (!row) {
      return null;
    }

    // Map kết quả về entity format
    const template = new AgentTemplate();
    template.id = row.template_id;
    template.typeId = row.template_type_id;
    template.profile = row.template_profile;
    template.convertConfig = row.template_convert_config;
    template.status = row.template_status;
    template.modelBaseId = row.template_model_base_id;
    template.modelFinetuningId = row.template_model_finetuning_id;
    template.isForSale = row.template_is_for_sale;
    template.createdBy = row.template_created_by;
    template.updatedBy = row.template_updated_by;
    template.deletedBy = row.template_deleted_by;

    // Thêm thông tin agent và type agent
    (template as any).agent = {
      id: row.agent_id,
      name: row.agent_name,
      avatar: row.agent_avatar,
      modelConfig: row.agent_model_config,
      instruction: row.agent_instruction,
      status: row.agent_status,
      createdAt: row.agent_created_at,
      updatedAt: row.agent_updated_at,
      deletedAt: row.agent_deleted_at,
    };

    (template as any).typeAgent = row.type_agent_id ? {
      id: row.type_agent_id,
      name: row.type_agent_name,
      description: row.type_agent_description,
    } : null;

    return template as any;
  }

  /**
   * Tìm mẫu agent theo ID loại agent
   * @param typeId ID của loại agent
   * @returns Danh sách mẫu agent
   */
  async findByTypeId(typeId: number): Promise<AgentTemplate[]> {
    return this.createBaseQuery()
      .where('agentTemplate.typeId = :typeId', { typeId })
      .getMany();
  }

  /**
   * Lấy danh sách mẫu agent với phân trang (tối ưu với JOIN)
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tìm theo tên agent)
   * @param status Trạng thái của mẫu agent
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách mẫu agent với phân trang
   */
  async findPaginated(
    page: number,
    limit: number,
    search?: string,
    status?: AgentTemplateStatus,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<AgentTemplate & { agent: any; typeAgent: any }>> {
    // Tạo query builder với JOIN để tối ưu performance
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'agentTemplate.id AS template_id',
        'agentTemplate.type_id AS template_type_id',
        'agentTemplate.profile AS template_profile',
        'agentTemplate.convert_config AS template_convert_config',
        'agentTemplate.status AS template_status',
        'agentTemplate.model_base_id AS template_model_base_id',
        'agentTemplate.model_finetuning_id AS template_model_finetuning_id',
        'agentTemplate.is_for_sale AS template_is_for_sale',
        'agentTemplate.created_by AS template_created_by',
        'agentTemplate.updated_by AS template_updated_by',
        'agentTemplate.deleted_by AS template_deleted_by',
        'agent.id AS agent_id',
        'agent.name AS agent_name',
        'agent.avatar AS agent_avatar',
        'agent.model_config AS agent_model_config',
        'agent.instruction AS agent_instruction',
        'agent.status AS agent_status',
        'agent.created_at AS agent_created_at',
        'agent.updated_at AS agent_updated_at',
        'agent.deleted_at AS agent_deleted_at',
        'typeAgent.id AS type_agent_id',
        'typeAgent.name AS type_agent_name',
        'typeAgent.description AS type_agent_description',
      ])
      .from('agents_template', 'agentTemplate')
      .innerJoin('agents', 'agent', 'agent.id = agentTemplate.id')
      .leftJoin('type_agents', 'typeAgent', 'typeAgent.id = agentTemplate.type_id')
      .where('agentTemplate.deleted_by IS NULL')
      .andWhere('agent.deleted_at IS NULL');

    // Thêm điều kiện lọc theo trạng thái nếu có
    if (status) {
      qb.andWhere('agentTemplate.status = :status', { status });
    }

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('agent.name ILIKE :search', { search: `%${search}%` });
    }

    // Đếm tổng số lượng
    const countQb = qb.clone();
    const total = await countQb.getCount();

    // Thêm phân trang và sắp xếp
    const sortField = sortBy === 'name' ? 'agent.name' :
                     sortBy === 'status' ? 'agentTemplate.status' :
                     sortBy === 'typeId' ? 'agentTemplate.type_id' :
                     sortBy === 'updatedAt' ? 'agent.updated_at' : 'agent.created_at';

    qb.orderBy(sortField, sortDirection)
      .offset((page - 1) * limit)
      .limit(limit);

    // Lấy dữ liệu
    const rawResults = await qb.getRawMany();

    // Map kết quả về entity format
    const items = rawResults.map(row => {
      const template = new AgentTemplate();
      template.id = row.template_id;
      template.typeId = row.template_type_id;
      template.profile = row.template_profile;
      template.convertConfig = row.template_convert_config;
      template.status = row.template_status;
      template.modelBaseId = row.template_model_base_id;
      template.modelFinetuningId = row.template_model_finetuning_id;
      template.isForSale = row.template_is_for_sale;
      template.createdBy = row.template_created_by;
      template.updatedBy = row.template_updated_by;
      template.deletedBy = row.template_deleted_by;

      // Thêm thông tin agent và type agent
      (template as any).agent = {
        id: row.agent_id,
        name: row.agent_name,
        avatar: row.agent_avatar,
        modelConfig: row.agent_model_config,
        instruction: row.agent_instruction,
        status: row.agent_status,
        createdAt: row.agent_created_at,
        updatedAt: row.agent_updated_at,
        deletedAt: row.agent_deleted_at,
      };

      (template as any).typeAgent = row.type_agent_id ? {
        id: row.type_agent_id,
        name: row.type_agent_name,
        description: row.type_agent_description,
      } : null;

      return template;
    });

    return {
      items: items as any,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Cập nhật trạng thái của mẫu agent
   * @param id ID của mẫu agent
   * @param status Trạng thái mới
   * @param updatedBy ID của nhân viên cập nhật
   * @returns Số lượng bản ghi đã cập nhật
   */
  async updateStatus(id: string, status: AgentTemplateStatus, updatedBy?: number): Promise<number> {
    const updateData: Partial<AgentTemplate> = { status };

    if (updatedBy) {
      updateData.updatedBy = updatedBy;
    }

    const result = await this.update(id, updateData);
    return result.affected || 0;
  }

  /**
   * Tìm mẫu agent đã xóa theo ID
   * @param id ID của mẫu agent
   * @returns AgentTemplate nếu tìm thấy, null nếu không tìm thấy
   */
  async findDeletedById(id: string): Promise<AgentTemplate | null> {
    return this.createBaseQuery()
      .where('agentTemplate.id = :id', { id })
      .andWhere('agentTemplate.deleted_by IS NOT NULL')
      .getOne();
  }

  /**
   * Tìm mẫu agent đã xóa theo ID với thông tin chi tiết (JOIN với agent và type_agent)
   * @param id ID của mẫu agent
   * @returns AgentTemplate với thông tin chi tiết nếu tìm thấy, null nếu không tìm thấy
   */
  async findDeletedByIdWithDetails(id: string): Promise<AgentTemplate & { agent: any; typeAgent: any } | null> {
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'agentTemplate.id AS template_id',
        'agentTemplate.type_id AS template_type_id',
        'agentTemplate.profile AS template_profile',
        'agentTemplate.convert_config AS template_convert_config',
        'agentTemplate.status AS template_status',
        'agentTemplate.model_base_id AS template_model_base_id',
        'agentTemplate.model_finetuning_id AS template_model_finetuning_id',
        'agentTemplate.is_for_sale AS template_is_for_sale',
        'agentTemplate.created_by AS template_created_by',
        'agentTemplate.updated_by AS template_updated_by',
        'agentTemplate.deleted_by AS template_deleted_by',
        'agent.id AS agent_id',
        'agent.name AS agent_name',
        'agent.avatar AS agent_avatar',
        'agent.model_config AS agent_model_config',
        'agent.instruction AS agent_instruction',
        'agent.status AS agent_status',
        'agent.created_at AS agent_created_at',
        'agent.updated_at AS agent_updated_at',
        'agent.deleted_at AS agent_deleted_at',
        'typeAgent.id AS type_agent_id',
        'typeAgent.name AS type_agent_name',
        'typeAgent.description AS type_agent_description',
      ])
      .from('agents_template', 'agentTemplate')
      .innerJoin('agents', 'agent', 'agent.id = agentTemplate.id')
      .leftJoin('type_agents', 'typeAgent', 'typeAgent.id = agentTemplate.type_id')
      .where('agentTemplate.id = :id', { id })
      .andWhere('agentTemplate.deleted_by IS NOT NULL')
      .andWhere('agent.deleted_at IS NOT NULL');

    const row = await qb.getRawOne();

    if (!row) {
      return null;
    }

    // Map kết quả về entity format
    const template = new AgentTemplate();
    template.id = row.template_id;
    template.typeId = row.template_type_id;
    template.profile = row.template_profile;
    template.convertConfig = row.template_convert_config;
    template.status = row.template_status;
    template.modelBaseId = row.template_model_base_id;
    template.modelFinetuningId = row.template_model_finetuning_id;
    template.isForSale = row.template_is_for_sale;
    template.createdBy = row.template_created_by;
    template.updatedBy = row.template_updated_by;
    template.deletedBy = row.template_deleted_by;

    // Thêm thông tin agent và type agent
    (template as any).agent = {
      id: row.agent_id,
      name: row.agent_name,
      avatar: row.agent_avatar,
      modelConfig: row.agent_model_config,
      instruction: row.agent_instruction,
      status: row.agent_status,
      createdAt: row.agent_created_at,
      updatedAt: row.agent_updated_at,
      deletedAt: row.agent_deleted_at,
    };

    (template as any).typeAgent = row.type_agent_id ? {
      id: row.type_agent_id,
      name: row.type_agent_name,
      description: row.type_agent_description,
    } : null;

    return template as any;
  }

  /**
   * Khôi phục mẫu agent đã xóa
   * @param id ID của mẫu agent
   * @param updatedBy ID của nhân viên khôi phục
   * @returns Số lượng bản ghi đã được khôi phục
   */
  async restoreTemplate(id: string, updatedBy: number): Promise<number> {
    const updateData: Partial<AgentTemplate> = {
      deletedBy: null,
      updatedBy,
    };

    const result = await this.update(id, updateData);
    return result.affected || 0;
  }

  /**
   * Khôi phục nhiều mẫu agent đã xóa
   * @param ids Danh sách ID của các mẫu agent
   * @param updatedBy ID của nhân viên khôi phục
   * @returns Số lượng bản ghi đã được khôi phục
   */
  async restoreMany(ids: string[], updatedBy: number): Promise<number> {
    if (!ids || ids.length === 0) {
      return 0;
    }

    const result = await this.createQueryBuilder()
      .update(AgentTemplate)
      .set({
        deletedBy: null,
        updatedBy,
      })
      .where('id IN (:...ids)', { ids })
      .andWhere('deleted_by IS NOT NULL')
      .execute();

    return result.affected || 0;
  }

  /**
   * Lấy danh sách mẫu agent đã xóa với phân trang (tối ưu với JOIN)
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tìm theo tên agent)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách mẫu agent đã xóa với phân trang
   */
  async findDeletedPaginated(
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<AgentTemplate & { agent: any; typeAgent: any }>> {
    // Tạo query builder với JOIN để tối ưu performance
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'agentTemplate.id AS template_id',
        'agentTemplate.type_id AS template_type_id',
        'agentTemplate.profile AS template_profile',
        'agentTemplate.convert_config AS template_convert_config',
        'agentTemplate.status AS template_status',
        'agentTemplate.model_base_id AS template_model_base_id',
        'agentTemplate.model_finetuning_id AS template_model_finetuning_id',
        'agentTemplate.is_for_sale AS template_is_for_sale',
        'agentTemplate.created_by AS template_created_by',
        'agentTemplate.updated_by AS template_updated_by',
        'agentTemplate.deleted_by AS template_deleted_by',
        'agent.id AS agent_id',
        'agent.name AS agent_name',
        'agent.avatar AS agent_avatar',
        'agent.model_config AS agent_model_config',
        'agent.instruction AS agent_instruction',
        'agent.status AS agent_status',
        'agent.created_at AS agent_created_at',
        'agent.updated_at AS agent_updated_at',
        'agent.deleted_at AS agent_deleted_at',
        'typeAgent.id AS type_agent_id',
        'typeAgent.name AS type_agent_name',
        'typeAgent.description AS type_agent_description',
      ])
      .from('agents_template', 'agentTemplate')
      .innerJoin('agents', 'agent', 'agent.id = agentTemplate.id')
      .leftJoin('type_agents', 'typeAgent', 'typeAgent.id = agentTemplate.type_id')
      .where('agentTemplate.deleted_by IS NOT NULL')
      .andWhere('agent.deleted_at IS NOT NULL');

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('agent.name ILIKE :search', { search: `%${search}%` });
    }

    // Đếm tổng số lượng
    const countQb = qb.clone();
    const total = await countQb.getCount();

    // Thêm phân trang và sắp xếp
    const sortField = sortBy === 'name' ? 'agent.name' :
                     sortBy === 'status' ? 'agentTemplate.status' :
                     sortBy === 'typeId' ? 'agentTemplate.type_id' :
                     sortBy === 'updatedAt' ? 'agent.updated_at' : 'agent.created_at';

    qb.orderBy(sortField, sortDirection)
      .offset((page - 1) * limit)
      .limit(limit);

    // Lấy dữ liệu
    const rawResults = await qb.getRawMany();

    // Map kết quả về entity format
    const items = rawResults.map(row => {
      const template = new AgentTemplate();
      template.id = row.template_id;
      template.typeId = row.template_type_id;
      template.profile = row.template_profile;
      template.convertConfig = row.template_convert_config;
      template.status = row.template_status;
      template.modelBaseId = row.template_model_base_id;
      template.modelFinetuningId = row.template_model_finetuning_id;
      template.isForSale = row.template_is_for_sale;
      template.createdBy = row.template_created_by;
      template.updatedBy = row.template_updated_by;
      template.deletedBy = row.template_deleted_by;

      // Thêm thông tin agent và type agent
      (template as any).agent = {
        id: row.agent_id,
        name: row.agent_name,
        avatar: row.agent_avatar,
        modelConfig: row.agent_model_config,
        instruction: row.agent_instruction,
        status: row.agent_status,
        createdAt: row.agent_created_at,
        updatedAt: row.agent_updated_at,
        deletedAt: row.agent_deleted_at,
      };

      (template as any).typeAgent = row.type_agent_id ? {
        id: row.type_agent_id,
        name: row.type_agent_name,
        description: row.type_agent_description,
      } : null;

      return template;
    });

    return {
      items: items as any,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }
}
