/**
 * Interface cho job gửi ZNS đơn lẻ
 */
export interface SendZnsJobData {
  /**
   * ID Official Account
   */
  oaId: string;

  /**
   * Số điện thoại nhận
   */
  phone: string;

  /**
   * ID template ZNS
   */
  templateId: string;

  /**
   * Dữ liệu cho template
   */
  templateData: Record<string, any>;

  /**
   * ID chiến dịch (nếu có)
   */
  campaignId?: number;

  /**
   * Tracking ID (tùy chọn)
   */
  trackingId?: string;

  /**
   * Số lần retry
   */
  retryCount?: number;
}

/**
 * Interface cho job gửi ZNS theo chiến dịch
 */
export interface SendZnsCampaignJobData {
  /**
   * ID chiến dịch
   */
  campaignId: number;

  /**
   * ID Official Account
   */
  oaId: string;

  /**
   * ID template ZNS
   */
  templateId: string;

  /**
   * Dữ liệu cho template
   */
  templateData: Record<string, any>;

  /**
   * Danh sách số điện thoại
   */
  phoneList: string[];

  /**
   * Batch size (số tin nhắn gửi cùng lúc)
   */
  batchSize?: number;

  /**
   * Delay giữa các batch (milliseconds)
   */
  batchDelay?: number;
}

/**
 * Interface cho job gửi batch ZNS
 */
export interface SendBatchZnsJobData {
  /**
   * ID Official Account
   */
  oaId: string;

  /**
   * Danh sách tin nhắn ZNS
   */
  messages: {
    phone: string;
    templateId: string;
    templateData: Record<string, any>;
    trackingId?: string;
  }[];

  /**
   * ID chiến dịch (nếu có)
   */
  campaignId?: number;

  /**
   * Batch index (thứ tự batch)
   */
  batchIndex: number;

  /**
   * Tổng số batch
   */
  totalBatches: number;
}

/**
 * Interface cho kết quả gửi ZNS
 */
export interface ZnsJobResult {
  /**
   * Trạng thái thành công
   */
  success: boolean;

  /**
   * Số điện thoại
   */
  phone: string;

  /**
   * Message ID từ Zalo (nếu thành công)
   */
  messageId?: string;

  /**
   * Mã lỗi (nếu thất bại)
   */
  errorCode?: string;

  /**
   * Thông báo lỗi (nếu thất bại)
   */
  errorMessage?: string;

  /**
   * Thời gian gửi
   */
  sentAt: number;
}

/**
 * Interface cho kết quả batch ZNS
 */
export interface BatchZnsJobResult {
  /**
   * ID chiến dịch
   */
  campaignId?: number;

  /**
   * Batch index
   */
  batchIndex: number;

  /**
   * Tổng số batch
   */
  totalBatches: number;

  /**
   * Danh sách kết quả
   */
  results: ZnsJobResult[];

  /**
   * Số tin nhắn thành công
   */
  successCount: number;

  /**
   * Số tin nhắn thất bại
   */
  failedCount: number;

  /**
   * Thời gian hoàn thành
   */
  completedAt: number;
}
