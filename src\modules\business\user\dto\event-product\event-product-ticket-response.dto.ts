import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

/**
 * Response DTO cho vé sự kiện
 */
export class EventProductTicketResponseDto {
  @ApiProperty({
    description: 'ID vé sự kiện',
    example: 123,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'ID sản phẩm sự kiện',
    example: 456,
  })
  @Expose()
  eventProductId: number;

  @ApiProperty({
    description: 'Tên loại vé',
    example: 'Vé VIP',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Giá vé (VND)',
    example: 500000,
  })
  @Expose()
  price: number;

  @ApiProperty({
    description: 'Tổng số lượng vé được phát hành',
    example: 100,
  })
  @Expose()
  totalQuantity: number;

  @ApiProperty({
    description: '<PERSON><PERSON> tả chi tiết cho loại vé',
    example: '<PERSON><PERSON> <PERSON> bao gồm chỗ ngồi hạng nhất và buffet',
    required: false,
  })
  @Expose()
  description?: string;

  @ApiProperty({
    description: 'Thời gian bắt đầu mở bán (epoch milliseconds)',
    example: 1704067200000,
  })
  @Expose()
  saleStart: number;

  @ApiProperty({
    description: 'Thời gian kết thúc bán vé (epoch milliseconds)',
    example: 1704153600000,
  })
  @Expose()
  saleEnd: number;

  @ApiProperty({
    description: 'Múi giờ áp dụng cho thời gian mở bán',
    example: 'Asia/Ho_Chi_Minh',
  })
  @Expose()
  timeZone: string;

  @ApiProperty({
    description: 'Mã định danh SKU cho vé',
    example: 'TICKET-VIP-001',
    required: false,
  })
  @Expose()
  sku?: string;

  @ApiProperty({
    description: 'Số lượng tối thiểu phải mua trong 1 lần đặt vé',
    example: 1,
    required: false,
  })
  @Expose()
  minQuantityPerOrder?: number;

  @ApiProperty({
    description: 'Số lượng tối đa được phép mua trong 1 lần đặt vé',
    example: 10,
    required: false,
  })
  @Expose()
  maxQuantityPerOrder?: number;

  @ApiProperty({
    description: 'Danh sách hình ảnh của vé',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'dcc8f803-04fc-49b3-953a-e660505ac353' },
        url: { type: 'string', example: 'https://cdn.example.com/ticket-image.jpg' },
        name: { type: 'string', example: 'ticket-vip.jpg' },
        size: { type: 'number', example: 1024000 },
        mimeType: { type: 'string', example: 'image/jpeg' }
      }
    },
    required: false,
  })
  @Expose()
  images?: Record<string, unknown>[];
}
