import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateEntityHasMediaMediaIdToUuid1750100000000 implements MigrationInterface {
  name = 'UpdateEntityHasMediaMediaIdToUuid1750100000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1. <PERSON><PERSON>a tất cả dữ liệu hiện tại trong entity_has_media vì không thể convert bigint sang uuid
    await queryRunner.query(`DELETE FROM "entity_has_media"`);
    
    // 2. Thay đổi kiểu dữ liệu của cột media_id từ bigint sang uuid
    await queryRunner.query(`ALTER TABLE "entity_has_media" ALTER COLUMN "media_id" TYPE uuid USING NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Rollback: Xóa dữ liệu và chuyển về bigint
    await queryRunner.query(`DELETE FROM "entity_has_media"`);
    await queryRunner.query(`ALTER TABLE "entity_has_media" ALTER COLUMN "media_id" TYPE bigint USING NULL`);
  }
}
