import { Body, Controller, Get, HttpStatus, Logger, Param, Post, Query, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response';
import { PaymentInfoResponseDto, PurchasePointDto, PurchasePointResponseDto } from '../dto';
import { PointUserService } from '../services/point-user.service';
import { CouponUserService } from '../services/coupon-user.service';
import { SystemConfigurationService } from '@modules/system-configuration/admin/service/system-configuration.service';
import { PointPurchaseTransactionRepository, InvoiceRepository } from '@modules/r-point/repositories';
import { TransactionStatus, InvoiceStatus } from '@modules/r-point/enums';
import { TransactionUserService } from '../services/transaction-user.service';
import { TransactionRedisService } from '../services/transaction-redis.service';
import { BankInfoService } from '../services/bank-info.service';
import { UserTransactionResponseDto } from '../dto/transaction-response.dto';
import { UserTransactionQueryDto } from '../dto/transaction-request.dto';
import { AppException, ErrorCode } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { BankRepository } from '@modules/user/repositories/bank.repository';
import { CdnService } from '@/shared/services/cdn.service';
import { TimeIntervalEnum } from '@/shared/utils';

@ApiTags(SWAGGER_API_TAGS.PAYMENT_R_POINT_USER)
@Controller('r-point/payment')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class PaymentUserController {
  private readonly logger = new Logger(PaymentUserController.name);
  constructor(
    private readonly pointUserService: PointUserService,
    private readonly couponUserService: CouponUserService,
    private readonly systemConfigurationService: SystemConfigurationService,
    private readonly transactionRepository: PointPurchaseTransactionRepository,
    private readonly transactionUserService: TransactionUserService,
    private readonly transactionRedisService: TransactionRedisService,
    private readonly bankInfoService: BankInfoService,
    private readonly bankRepository: BankRepository,
    private readonly cdnService: CdnService,
    private readonly invoiceRepository: InvoiceRepository,
  ) {}

  /**
   * API mua R-Point
   * @param user Thông tin người dùng
   * @param purchasePointDto Thông tin mua R-Point
   * @returns Thông tin giao dịch và QR code
   */
  @Post('purchase')
  @ApiOperation({
    summary: 'Mua R-Point',
    description: 'API mua R-Point với tùy chọn thông tin hóa đơn. Hỗ trợ hóa đơn doanh nghiệp và cá nhân.'
  })
  @ApiBody({
    type: PurchasePointDto,
    examples: {
      'business-invoice': {
        summary: 'Mua R-Point với hóa đơn doanh nghiệp',
        description: 'Ví dụ mua R-Point với thông tin hóa đơn doanh nghiệp đầy đủ',
        value: {
          pointId: 1,
          pointAmount: 100,
          couponCode: 'SUMMER2023',
          invoiceInfo: {
            type: 'business',
            representativeName: 'Nguyễn Văn A',
            representativePosition: 'Giám đốc',
            companyName: 'Công ty TNHH ABC',
            companyAddress: '123 Đường ABC, Quận 1, TP.HCM',
            taxCode: '0123456789',
            email: '<EMAIL>'
          }
        }
      },
      'personal-invoice': {
        summary: 'Mua R-Point với hóa đơn cá nhân',
        description: 'Ví dụ mua R-Point với thông tin hóa đơn cá nhân',
        value: {
          pointId: 1,
          pointAmount: 100,
          invoiceInfo: {
            type: 'personal',
            email: '<EMAIL>'
          }
        }
      },
      'no-invoice': {
        summary: 'Mua R-Point không cần hóa đơn',
        description: 'Ví dụ mua R-Point không yêu cầu hóa đơn',
        value: {
          pointId: 1,
          pointAmount: 100,
          couponCode: 'SUMMER2023'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo giao dịch mua R-Point thành công',
    type: PurchasePointResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ - Kiểm tra thông tin hóa đơn cho hóa đơn doanh nghiệp',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy gói R-Point',
  })
  async purchasePoint(
    @CurrentUser() user: JwtPayload,
    @Body() purchasePointDto: PurchasePointDto,
  ): Promise<ApiResponseDto<PurchasePointResponseDto>> {
    // 1. Tìm kiếm gói R-point theo id
    const point = await this.pointUserService.getPointById(
      purchasePointDto.pointId,
    );
    if (!point) {
      throw new AppException(
        ErrorCode.RESOURCE_NOT_FOUND,
        `Không tìm thấy gói R-Point với ID ${purchasePointDto.pointId}`,
      );
    }

    // Tính toán số tiền cần thanh toán
    let amount = 0;
    let finalAmount = 0;
    let couponId: number | undefined = undefined;

    // Nếu là gói customize, tính toán số tiền dựa trên số point và tỷ lệ
    if (point.isCustomize) {
      // Kiểm tra số point có nằm trong khoảng min-max không
      if (point.min && purchasePointDto.pointAmount < point.min) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Số lượng R-Point tối thiểu là ${point.min}`,
        );
      }

      if (point.max && purchasePointDto.pointAmount > point.max) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Số lượng R-Point tối đa là ${point.max}`,
        );
      }

      // Tính toán số tiền dựa trên số point và tỷ lệ
      amount = purchasePointDto.pointAmount * point.rate;
    } else {
      // Nếu là gói cố định, lấy giá tiền và số point từ gói
      amount = point.cash;
      // Ghi đè số point từ DTO bằng số point của gói
      purchasePointDto.pointAmount = point.point;
    }

    // 2. Kiểm tra coupon nếu có
    if (purchasePointDto.couponCode) {
      const validationResult = await this.couponUserService.validateCoupon(
        {
          couponCode: purchasePointDto.couponCode,
          pointId: purchasePointDto.pointId,
          pointAmount: purchasePointDto.pointAmount,
        },
        user.id,
      );

      if (validationResult.isValid) {
        finalAmount = validationResult.finalPrice;
        // Chuyển đổi coupon.id từ string sang number
        couponId = parseInt(validationResult.coupon.id);
      } else {
        // Nếu coupon không hợp lệ, sử dụng giá gốc
        finalAmount = amount;
      }
    } else {
      // Nếu không có coupon, sử dụng giá gốc
      finalAmount = amount;
    }

    // 3. Tạo đối tượng đơn hàng với trạng thái đang chờ
    const now = Math.floor(Date.now() / 1000);
    const transaction = await this.transactionRepository.create({
      userId: user.id,
      amount: finalAmount,
      pointsAmount: purchasePointDto.pointAmount,
      pointName: point.name,
      pointId: point.id,
      currency: 'VND',
      status: TransactionStatus.PENDING,
      paymentMethod: 'BANK_TRANSFER',
      description: `Mua ${purchasePointDto.pointAmount} R-Point`,
      createdAt: now,
      updatedAt: now,
      balanceBefore: 0, // Sẽ cập nhật khi thanh toán thành công
      balanceAfter: 0, // Sẽ cập nhật khi thanh toán thành công
      couponId: couponId,
    });

    // 4. Tạo url qr code
    const qrCodeUrl =
      await this.systemConfigurationService.generateQRPaymentUrl(
        finalAmount,
        transaction.id.toString(),
      );

    // 5. Lấy des
    const des = this.systemConfigurationService.generateDescription(
      transaction.id.toString(),
    );

    // 6. Lưu thông tin hóa đơn nếu có
    if (purchasePointDto.invoiceInfo) {
      try {
        await this.invoiceRepository.save({
          orderId: transaction.id,
          buyerFullName: purchasePointDto.invoiceInfo.representativeName || '',
          companyName: purchasePointDto.invoiceInfo.companyName || '',
          taxCode: purchasePointDto.invoiceInfo.taxCode || '',
          address: purchasePointDto.invoiceInfo.companyAddress || '',
          paymentMethod: 'BANK_TRANSFER',
          currency: 'VND',
          status: InvoiceStatus.PENDING,
          itemName: `Mua ${purchasePointDto.pointAmount} R-Point`,
          quantity: purchasePointDto.pointAmount,
          unitPrice: finalAmount / purchasePointDto.pointAmount,
          amount: finalAmount,
          vatRate: 0, // Có thể cấu hình sau
          vatAmount: 0, // Có thể cấu hình sau
          createdAt: now,
          updatedAt: now,
        });
        this.logger.log(`Đã lưu thông tin hóa đơn cho giao dịch ${transaction.id}`);
      } catch (error) {
        this.logger.error(`Lỗi khi lưu thông tin hóa đơn: ${error.message}`, error.stack);
        // Không throw lỗi để không ảnh hưởng đến quá trình tạo giao dịch
      }
    }

    // Tạo đối tượng response
    const responseData: PurchasePointResponseDto = {
      id: transaction.id,
      userId: transaction.userId,
      amount: transaction.amount,
      pointsAmount: transaction.pointsAmount,
      pointName: transaction.pointName,
      description: des,
      pointId: transaction.pointId,
      status: transaction.status,
      qrCodeUrl: qrCodeUrl,
      createdAt: transaction.createdAt,
      originalAmount: amount,
      updatedAt: transaction.updatedAt,
    };

    // Lưu trạng thái giao dịch vào Redis với thời gian sống 30 phút
    await this.transactionRedisService.saveTransactionStatus(
      transaction.id,
      responseData
    );

    // Trả về thông tin giao dịch và QR code
    return ApiResponseDto.success(
      responseData,
      'Tạo giao dịch mua R-Point thành công',
    );
  }

  /**
   * Lấy danh sách lịch sử giao dịch mua R-Point của người dùng
   * @param user Thông tin người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách giao dịch và thông tin phân trang
   */
  @Get('history')
  @ApiOperation({ summary: 'Lấy lịch sử giao dịch mua R-Point' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách lịch sử giao dịch mua R-Point',
    schema: ApiResponseDto.getPaginatedSchema(UserTransactionResponseDto)
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Không có quyền truy cập',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang (bắt đầu từ 1)', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng item trên mỗi trang', type: Number })
  @ApiQuery({ name: 'status', required: false, description: 'Trạng thái giao dịch', enum: TransactionStatus })
  @ApiQuery({ name: 'search', required: false, description: 'Từ khóa tìm kiếm (tìm theo mã tham chiếu)' })
  @ApiQuery({ name: 'startTime', required: false, description: 'Thời gian bắt đầu (Unix timestamp)', type: Number })
  @ApiQuery({ name: 'endTime', required: false, description: 'Thời gian kết thúc (Unix timestamp)', type: Number })
  async getTransactionHistory(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: UserTransactionQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<UserTransactionResponseDto>>> {
    try {
      const transactions = await this.transactionUserService.getUserTransactions(user.id, queryDto);
      return ApiResponseDto.paginated(transactions);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Lỗi khi lấy lịch sử giao dịch');
    }
  }

  /**
   * Kiểm tra trạng thái giao dịch từ Redis
   * @param transactionId ID của giao dịch
   * @returns Thông tin giao dịch hoặc null nếu không tìm thấy
   */
  @Get('status/:transactionId')
  @ApiOperation({ summary: 'Kiểm tra trạng thái giao dịch từ Redis' })
  @ApiParam({ name: 'transactionId', description: 'ID của giao dịch', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thông tin trạng thái giao dịch',
    type: PurchasePointResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy thông tin giao dịch trong Redis',
  })
  async checkTransactionStatus(
    @Param('transactionId') transactionId: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PurchasePointResponseDto | null>> {
    try {
      // Kiểm tra trạng thái từ Redis
      let transactionStatus = await this.transactionRedisService.getTransactionStatus(transactionId);

      // Nếu không tìm thấy trong Redis, tìm trong database
      if (!transactionStatus) {
        this.logger.debug(`Không tìm thấy giao dịch ${transactionId} trong Redis, tìm trong database`);

        // Tìm giao dịch trong database
        const transaction = await this.transactionRepository.findById(transactionId);

        // Nếu không tìm thấy trong database, trả về null
        if (!transaction) {
          return ApiResponseDto.success(null, 'Không tìm thấy thông tin giao dịch');
        }

        // Kiểm tra quyền truy cập (chỉ người dùng tạo giao dịch mới có thể xem)
        if (transaction.userId !== user.id) {
          throw new AppException(
            ErrorCode.FORBIDDEN,
            'Bạn không có quyền truy cập thông tin giao dịch này',
          );
        }

        // Tạo mô tả giao dịch
        const description = this.systemConfigurationService.generateDescription(transaction.id.toString());

        // Tạo URL QR thanh toán
        const qrCodeUrl = await this.systemConfigurationService.generateQRPaymentUrl(
          transaction.amount,
          transaction.id.toString(),
        );

        // Tạo đối tượng response
        transactionStatus = {
          id: transaction.id,
          userId: transaction.userId,
          amount: transaction.amount,
          pointsAmount: transaction.pointsAmount,
          pointName: transaction.pointName,
          description: description,
          pointId: transaction.pointId,
          status: transaction.status,
          qrCodeUrl: qrCodeUrl,
          createdAt: transaction.createdAt,
          updatedAt: transaction.updatedAt,
        };

        // Lưu vào Redis với thời gian sống 30 phút
        await this.transactionRedisService.saveTransactionStatus(
          transaction.id,
          transactionStatus
        );

        this.logger.debug(`Đã lưu giao dịch ${transactionId} vào Redis`);
      } else {
        // Kiểm tra quyền truy cập (chỉ người dùng tạo giao dịch mới có thể xem)
        if (transactionStatus.userId !== user.id) {
          throw new AppException(
            ErrorCode.FORBIDDEN,
            'Bạn không có quyền truy cập thông tin giao dịch này',
          );
        }
      }

      return ApiResponseDto.success(transactionStatus, 'Thông tin trạng thái giao dịch');
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi kiểm tra trạng thái giao dịch: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi kiểm tra trạng thái giao dịch: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết thanh toán của giao dịch
   * @param transactionId ID của giao dịch
   * @param user Thông tin người dùng
   * @returns Thông tin chi tiết thanh toán
   */
  @Get('info/:transactionId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết thanh toán' })
  @ApiParam({ name: 'transactionId', description: 'ID của giao dịch', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thông tin chi tiết thanh toán',
    type: PaymentInfoResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy giao dịch',
  })
  async getPaymentInfo(
    @Param('transactionId') transactionId: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PaymentInfoResponseDto>> {
    try {
      // Lấy thông tin giao dịch từ database
      const transaction = await this.transactionRepository.findById(transactionId);

      if (!transaction) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy giao dịch với ID ${transactionId}`,
        );
      }

      // Kiểm tra quyền truy cập (chỉ người dùng tạo giao dịch mới có thể xem)
      if (transaction.userId !== user.id) {
        throw new AppException(
          ErrorCode.FORBIDDEN,
          'Bạn không có quyền truy cập thông tin giao dịch này',
        );
      }

      // Lấy thông tin cấu hình hệ thống
      const systemConfig = await this.systemConfigurationService.getActiveConfiguration();

      // Tạo mô tả giao dịch
      const description = this.systemConfigurationService.generateDescription(transaction.id.toString());

      // Tạo URL QR thanh toán
      const qrCodeUrl = await this.systemConfigurationService.generateQRPaymentUrl(
        transaction.amount,
        transaction.id.toString(),
      );

      // Lấy thông tin ngân hàng từ mã ngân hàng
      const bankName = this.bankInfoService.getBankName(systemConfig.bankCode);

      // Lấy thông tin logo ngân hàng từ repository
      let logoPath = '';
      try {
        const bankInfo = await this.bankRepository.findByCode(systemConfig.bankCode);
        if (bankInfo && bankInfo.logoPath) {
          // Tạo URL tạm thời cho logo ngân hàng
          const url = this.cdnService.generateUrlView(bankInfo.logoPath, TimeIntervalEnum.ONE_HOUR);
          if (url) {
            logoPath = url;
          }
        }
      } catch (error) {
        this.logger.error(`Lỗi khi lấy thông tin ngân hàng: ${error.message}`, error.stack);
        // Không throw lỗi, chỉ log và tiếp tục
      }

      // Tạo đối tượng response
      const paymentInfo: PaymentInfoResponseDto = {
        transactionId: transaction.id,
        bankCode: systemConfig.bankCode,
        bankName: bankName,
        logoPath: logoPath,
        accountNumber: systemConfig.accountNumber,
        accountHolder: systemConfig.accountName,
        amount: transaction.amount,
        pointsAmount: transaction.pointsAmount,
        pointName: transaction.pointName,
        description: description,
        qrCodeUrl: qrCodeUrl,
        status: transaction.status,
        createdAt: transaction.createdAt,
        updatedAt: transaction.updatedAt,
      };

      return ApiResponseDto.success(paymentInfo, 'Thông tin chi tiết thanh toán');
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi lấy thông tin chi tiết thanh toán: ${error.message}`,
      );
    }
  }
}
