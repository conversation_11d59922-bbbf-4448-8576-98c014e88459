import { Injectable, Logger } from '@nestjs/common';
import { UserService } from '@/modules/user/user/service/user.service';
import {
  FacebookAuthDto,
  GoogleAuthDto,
  LoginDto,
  RegisterDto,
  ResendOtpDto,
  SelectTwoFactorAuthMethodDto,
  SelectTwoFactorAuthMethodResponseDto,
  TwoFaLoginResponseDto,
  VerifyOtpDto,
  VerifyTwoFactorAuthDto,
  VerifyTwoFactorAuthResponseDto,
  ZaloAuthDto,
} from '../dto';
import { LoginResponse } from '../dto/login.dto';
import * as bcrypt from 'bcrypt';
import { User } from '@modules/user/entities';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RecaptchaService } from '@shared/services/recaptcha.service';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { RagApiKeyProvisioningService } from '@/modules/user/user/service/rag-api-key-provisioning.service';
import {
  JwtPayload,
  JwtUtilService,
  TokenType,
} from '@modules/auth/guards/jwt.util';
import { PermissionService } from '@/modules/user/user/service/permission.service';
import { RedisService } from '@shared/services/redis.service';
import { ConfigService } from '@nestjs/config';
import { SendWithTemplateService } from '@/modules/email/services/send-with-template.service';
import { EmailPlaceholderService } from '@/modules/email/services/email-placeholder.service';
import { UserRoleService } from '@/modules/user/user/service/user-role.service';
import { Transactional } from 'typeorm-transactional';
import { PasswordService } from '@/modules/user/share/password.share.service';
import { ForgotPasswordDto } from '../dto/forgot-password.dto';
import { VerifyForgotPasswordDto } from '../dto/verify-forgot-password.dto';
import { ResetPasswordDto } from '../dto/reset-password.dto';
import { AppException, ErrorCode } from '@/common';
import { AUTH_ERROR_CODE } from '../errors';
import { TwoFactorAuthService } from '@/modules/user/user/service';
import { OAuth2Service } from './oauth2.service';
import {
  AuthVerificationLogRepository,
  DeviceInfoRepository,
} from '@/modules/user/repositories';
import { AuthMethodEnum } from '@/modules/user/enums/auth-method.enum';
import { UAParser } from 'ua-parser-js';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly refreshTokenPrefix = 'refresh_token:';
  private readonly registrationPrefix = 'registration:';
  private readonly forgotPasswordPrefix = 'forgot_password:';
  private readonly changePasswordPrefix = 'change_password:';
  private readonly rememberMePrefix = 'remember_me:';
  private readonly twoFaPrefix = 'two_fa:';
  constructor(
    private readonly userService: UserService,
    private readonly jwtService: JwtUtilService,
    private readonly recaptchaService: RecaptchaService, // Used for reCAPTCHA verification (currently commented out in code)
    private readonly permissionService: PermissionService,
    private readonly redisService: RedisService,
    private readonly configService: ConfigService, // Used indirectly through jwtService
    private readonly sendWithTemplateService: SendWithTemplateService,
    private readonly emailPlaceholderService: EmailPlaceholderService,
    private readonly userRoleService: UserRoleService,
    private readonly passwordService: PasswordService,
    private readonly twoFaService: TwoFactorAuthService,
    private readonly oauth2Service: OAuth2Service,
    private readonly authVerificationLogRepository: AuthVerificationLogRepository,
    private readonly deviceInfoRepository: DeviceInfoRepository,
    private readonly ragApiKeyProvisioningService: RagApiKeyProvisioningService,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {
    // Các dependency được inject để sử dụng trong các phương thức khác nhau của service
  }

  /**
   * Xác thực người dùng và tạo token JWT
   * @param loginDto Thông tin đăng nhập
   * @param requestInfo Thông tin yêu cầu (IP, User Agent, Fingerprint)
   * @returns Token JWT, refresh token và thông tin người dùng
   */
  @Transactional()
  async login(
    loginDto: LoginDto,
    requestInfo?: {
      ipAddress?: string;
      userAgent?: string;
      fingerprint?: string;
    },
  ): Promise<{
    result: ApiResponseDto<LoginResponse | TwoFaLoginResponseDto>;
    refreshToken: string;
  }> {
    // Xác thực reCAPTCHA token
    try {
      if (loginDto.recaptchaToken) {
        // Bỏ comment dòng dưới đây khi triển khai thực tế
        // const recaptchaResponse = await this.recaptchaService.verifyRecaptcha(loginDto.recaptchaToken);
        //
        // if (!recaptchaResponse.success) {
        //   throw new AppException(ErrorCode.RECAPTCHA_VERIFICATION_FAILED);
        // }
      }
    } catch (error) {
      throw new AppException(
        AUTH_ERROR_CODE.TOO_MANY_REQUESTS,
        'Xác thực reCAPTCHA thất bại',
      );
    }

    try {
      // Tìm người dùng theo email và verify
      const user = await this.validateUser(loginDto.email, loginDto.password);

      // Kiểm tra xem đã verify email hoặc phone hoặc có tài khoản Facebook chưa
      // Nếu chưa, gửi OTP qua email để xác thực
      if (!user.isVerifyEmail && !user.isVerifyPhone && !user.facebookId) {
        // Tạo OTP 6 số
        const otp = this.generateOTP();

        // Tạo token OTP
        const { token: otpToken, expiresInSeconds } =
          this.jwtService.generateUserVerifyToken({
            sub: user.id,
            username: user.email || user.phoneNumber,
          });

        // Lưu thông tin OTP vào Redis để sử dụng sau này
        const verificationData = {
          userId: user.id,
          email: user.email,
          phoneNumber: user.phoneNumber,
          otp,
          createdAt: Date.now(),
        };

        const redisKey = `${this.registrationPrefix}${otpToken}`;
        try {
          await this.redisService.setWithExpiry(
            redisKey,
            JSON.stringify(verificationData),
            expiresInSeconds,
          );
          this.logger.debug(
            `Verification data stored in Redis with key: ${redisKey}`,
          );
        } catch (error) {
          this.logger.error(
            `Failed to store verification data in Redis: ${error.message}`,
            error,
          );
          throw new AppException(
            AUTH_ERROR_CODE.REGISTRATION_FAILED,
            'Không thể lưu thông tin xác thực',
          );
        }

        // Che một phần email
        const maskedEmail = this.maskEmail(user.email);

        // Gửi email chứa OTP cho người dùng
        try {
          await this.emailPlaceholderService.sendEmailVerification({
            EMAIL: user.email,
            USER_ID: user.id.toString(),
            NAME: user.fullName || user.email,
            TWO_FA_CODE: otp,
          });
          this.logger.debug(
            `Sent verification email with OTP to ${user.email}`,
          );
        } catch (error) {
          this.logger.error(
            `Failed to send verification email to ${user.email}: ${error.message}`,
            error,
          );
          // Không throw lỗi ở đây để không ảnh hưởng đến luồng đăng nhập
        }

        // Tính toán thời điểm hết hạn thực tế (timestamp)
        const expiresAt = Date.now() + expiresInSeconds * 1000;

        // Chuẩn bị kết quả trả về
        const verifyResult = {
          verifyToken: otpToken,
          expiresAt,
          info: [{ platform: 'EMAIL', value: maskedEmail }],
        };

        // Trong môi trường phát triển, trả về cả OTP để tiện test
        if (this.configService.get<string>('NODE_ENV') !== 'production') {
          verifyResult['otp'] = otp;
        }

        return {
          result: {
            code: 202,
            message: 'Mã OTP đã được gửi đến email',
            result: verifyResult,
          },
          refreshToken: '',
        };
      }

      // Kiểm tra xác thực 2FA
      const twoFa = await this.twoFaService.getTwoFactorAuthStatus(user.id);

      if (twoFa.is2FAEnabled) {
        // Tạo token tạm thời cho quá trình xác thực 2FA
        const twoFaToken = this.jwtService.generateUserTokenTwoFA({
          sub: user.id,
          username: user.email || user.phoneNumber,
        });

        // Chuẩn bị thông tin về các phương thức 2FA đã được kích hoạt
        const enabledMethods: Array<{ type: string; value: string }> = [];

        // Nếu xác thực qua email được bật
        if (twoFa.otpEmailEnabled) {
          // Ẩn một phần email để bảo mật (ví dụ: a***@gmail.com)
          const maskedEmail = this.maskEmail(user.email);
          enabledMethods.push({ type: 'EMAIL', value: maskedEmail });
        }

        // Nếu xác thực qua Google Authenticator được bật
        if (twoFa.googleAuthenticatorEnabled) {
          enabledMethods.push({
            type: 'GOOGLE_AUTHENTICATOR',
            value: 'Google Authenticator',
          });
        }

        // Nếu xác thực qua SMS được bật
        if (twoFa.otpSmsEnabled) {
          // Ẩn một phần số điện thoại để bảo mật (ví dụ: ****1234)
          const maskedPhone = this.maskPhoneNumber(user.phoneNumber);
          enabledMethods.push({ type: 'SMS', value: maskedPhone });
        }

        // Tính toán thời điểm hết hạn thực tế (timestamp)
        const expiresAt = Date.now() + twoFaToken.expiresInSeconds * 1000;

        // Trả về token tạm thời và danh sách phương thức xác thực
        return {
          result: {
            code: 203,
            message: 'Yêu cầu xác thực hai lớp',
            result: {
              verifyToken: twoFaToken.token,
              expiresAt,
              enabledMethods: enabledMethods,
            },
          },
          refreshToken: '',
        };
      }

      // Lấy ra permission
      const permissions =
        (await this.permissionService.getUserPermissions(user.id)) || [];

      // Tạo JWT token
      const accessToken = await this.generateToken(user, permissions);

      // Nếu có rememberMe, tạo token với thời gian dài hơn
      const { token: refreshToken, expiresInSeconds } = loginDto.rememberMe
        ? await this.generateLongLivedRefreshToken(user, permissions)
        : await this.generateRefreshToken(user, permissions);

      // Lưu refresh token vào Redis với key là user ID
      const redisKey = `${this.refreshTokenPrefix}${user.id}`;
      try {
        await this.redisService.setWithExpiry(
          redisKey,
          refreshToken,
          expiresInSeconds,
        );
        this.logger.debug(
          `Refresh token stored in Redis for user ID: ${user.id}`,
        );

        // Nếu có rememberMe, lưu thêm thông tin vào Redis để sử dụng sau này
        if (loginDto.rememberMe) {
          const rememberMeKey = `${this.rememberMePrefix}${user.id}`;
          const rememberMeData = {
            userId: user.id,
            email: user.email,
            refreshToken: refreshToken,
            createdAt: Date.now(),
          };

          // Lưu thông tin rememberMe với thời hạn dài hơn (30 ngày)
          await this.redisService.setWithExpiry(
            rememberMeKey,
            JSON.stringify(rememberMeData),
            30 * 24 * 60 * 60, // 30 ngày
          );

          this.logger.debug(
            `RememberMe data stored in Redis for user ID: ${user.id}`,
          );
        }
      } catch (error) {
        this.logger.error(
          `Failed to store token in Redis for user ID: ${user.id}`,
          error,
        );
        // Không throw lỗi ở đây để không ảnh hưởng đến luồng đăng nhập
      }

      // Tạo log xác thực thành công
      if (requestInfo) {
        try {
          // Tạo log xác thực
          await this.authVerificationLogRepository.createLoginSuccessLog(
            user.id,
            AuthMethodEnum.EMAIL,
            requestInfo.ipAddress,
            requestInfo.userAgent,
          );

          // Tạo hoặc cập nhật thông tin thiết bị
          if (requestInfo.fingerprint) {
            // Parse user agent để lấy thông tin trình duyệt
            const parser = new UAParser();
            parser.setUA(requestInfo.userAgent || '');
            const browserInfo = parser.getResult();

            await this.deviceInfoRepository.findOrCreate(
              requestInfo.fingerprint,
              user.id,
              requestInfo.ipAddress,
              requestInfo.userAgent,
              {
                browser: browserInfo.browser.name,
                os: browserInfo.os.name,
              },
            );
          }
        } catch (error) {
          // Log lỗi nhưng không ảnh hưởng đến luồng đăng nhập
          this.logger.error(
            `Failed to create login logs: ${error.message}`,
            error.stack,
          );
        }
      }

      // Tính toán thời điểm hết hạn thực tế (timestamp)
      const expiresAt = Date.now() + expiresInSeconds * 1000;

      const result: ApiResponseDto<LoginResponse> = {
        code: 200,
        message: 'Đăng nhập thành công',
        result: {
          accessToken,
          expiresAt,
          info: [],
          user: {
            id: user.id,
            email: user.email,
            username: '',
            permissions: permissions,
            status: 'active',
          },
        },
      };
      return { result, refreshToken };
    } catch (error) {
      // Nếu đăng nhập thất bại, tạo log xác thực thất bại
      if (
        requestInfo &&
        error instanceof AppException &&
        error.getResponse() &&
        (error.getResponse() as any).code ===
          AUTH_ERROR_CODE.INVALID_CREDENTIALS.code
      ) {
        try {
          // Tìm người dùng theo email (nếu có)
          const user = await this.userService.findByEmail(loginDto.email);

          // Tạo log xác thực thất bại
          await this.authVerificationLogRepository.createLoginFailedLog(
            user?.id || null,
            AuthMethodEnum.EMAIL,
            requestInfo.ipAddress,
            requestInfo.userAgent,
          );
        } catch (logError) {
          // Log lỗi nhưng không ảnh hưởng đến việc ném lỗi đăng nhập
          this.logger.error(
            `Failed to create login failure log: ${logError.message}`,
            logError.stack,
          );
        }
      }

      // Ném lại lỗi ban đầu
      throw error;
    }
  }

  /**
   * Đăng ký người dùng mới
   * @param registerDto Thông tin đăng ký
   * @returns Thông tin OTP và token OTP
   */
  @Transactional()
  async register(
    registerDto: RegisterDto,
  ): Promise<{ code: number; message: string; result: any }> {
    // Xác thực reCAPTCHA token
    try {
      if (registerDto.recaptchaToken) {
        if (this.configService.get<string>('NODE_ENV') == 'production') {
          const recaptchaResponse = await this.recaptchaService.verifyRecaptcha(registerDto.recaptchaToken);
        
          if (!recaptchaResponse.success) {
            throw new AppException(ErrorCode.RECAPTCHA_VERIFICATION_FAILED);
          }
        }
      }
    } catch (error) {
      throw new AppException(
        AUTH_ERROR_CODE.TOO_MANY_REQUESTS,
        'Xác thực reCAPTCHA thất bại',
      );
    }

    // Kiểm tra email đã tồn tại chưa
    const existingUser = await this.userService.findByEmail(registerDto.email);
    if (existingUser) {
      throw new AppException(AUTH_ERROR_CODE.EMAIL_ALREADY_EXISTS);
    }

    // Kiểm tra số điện thoại đã tồn tại chưa
    // TODO: Thêm phương thức findByPhoneNumber vào UserService
    const existingPhoneUser = await this.userService.findByPhone(
      registerDto.phoneNumber,
    );
    if (existingPhoneUser) {
      throw new AppException(AUTH_ERROR_CODE.PHONE_ALREADY_EXISTS);
    }

    // Mã hóa mật khẩu
    const hashedPassword = await this.hashPassword(registerDto.password);

    // Tạo người dùng mới với trạng thái chưa xác thực
    const newUser = await this.userService.create({
      email: registerDto.email,
      password: hashedPassword,
      fullName: registerDto.fullName,
      phoneNumber: registerDto.phoneNumber,
      isActive: true,
      isVerifyEmail: false, // Chưa xác thực email
      isVerifyPhone: false, // Chưa xác thực số điện thoại - chưa có trường này trong entity
      affiliateAccountId: registerDto.ref, // Ánh xạ ref thành affiliateAccountId
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Gán quyền cho người dùng
    await this.userRoleService.addUserRole(newUser.id);

    // Tự động tạo RAG API key cho user mới
    await this.ragApiKeyProvisioningService.provisionApiKeyForNewUser(newUser.id);

    // Tạo OTP 6 số
    const otp = this.generateOTP();

    // Tạo token OTP
    const { token: otpToken, expiresInSeconds } =
      this.jwtService.generateUserVerifyToken({
        sub: newUser.id, // Sử dụng ID của người dùng đã tạo
        username: registerDto.email,
        permissions: [],
      });

    // Lưu thông tin OTP vào Redis để sử dụng sau này
    const verificationData = {
      userId: newUser.id,
      email: registerDto.email,
      phoneNumber: registerDto.phoneNumber,
      otp,
      createdAt: Date.now(),
    };

    const redisKey = `${this.registrationPrefix}${otpToken}`;
    try {
      await this.redisService.setWithExpiry(
        redisKey,
        JSON.stringify(verificationData),
        expiresInSeconds,
      );
      this.logger.debug(
        `Verification data stored in Redis with key: ${redisKey}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to store verification data in Redis: ${error.message}`,
        error,
      );
      throw new AppException(
        AUTH_ERROR_CODE.REGISTRATION_FAILED,
        'Không thể lưu thông tin xác thực',
      );
    }

    // Che một phần số điện thoại và email
    // const maskedPhoneNumber = this.maskPhoneNumber(registerDto.phoneNumber);
    const maskedEmail = this.maskEmail(registerDto.email);

    // Gửi email chứa OTP cho người dùng
    try {
      await this.emailPlaceholderService.sendEmailVerification({
        EMAIL: registerDto.email,
        USER_ID: newUser.id.toString(),
        NAME: registerDto.fullName || registerDto.email,
        TWO_FA_CODE: otp,
      });
      this.logger.debug(
        `Sent verification email with OTP to ${registerDto.email}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send verification email to ${registerDto.email}: ${error.message}`,
        error,
      );
      // Không throw lỗi ở đây để không ảnh hưởng đến luồng đăng ký
    }

    // Tính toán thời điểm hết hạn thực tế (timestamp)
    const expiresAt = Date.now() + expiresInSeconds * 1000;

    // Trả về thông tin OTP và token
    const result = {
      otpToken,
      expiresAt,
      maskedEmail,
    };

    // Trong môi trường phát triển, trả về cả OTP để tiện test
    if (this.configService.get<string>('NODE_ENV') !== 'production') {
      result['otp'] = otp;
    }

    return {
      code: 200,
      message: 'Mã OTP đã được gửi đến email',
      result,
    };
  }

  /**
   * Xác thực người dùng bằng email và mật khẩu
   * @param email Email người dùng
   * @param password Mật khẩu người dùng
   * @returns Thông tin người dùng nếu xác thực thành công
   */
  private async validateUser(email: string, password: string): Promise<User> {
    // Tìm người dùng theo email
    const user = await this.userService.findByEmail(email);
    if (!user) {
      throw new AppException(AUTH_ERROR_CODE.INVALID_CREDENTIALS);
    }

    // Kiểm tra mật khẩu
    const isPasswordValid = await this.comparePasswords(
      password,
      user.password,
    );

    if (!isPasswordValid) {
      throw new AppException(AUTH_ERROR_CODE.INVALID_CREDENTIALS);
    }

    // Kiểm tra trạng thái tài khoản
    if (!user.isActive) {
      throw new AppException(AUTH_ERROR_CODE.ACCOUNT_LOCKED);
    }

    return user;
  }

  /**
   * Tạo JWT token
   * @param user Thông tin người dùng
   * @param permissions
   * @returns JWT token
   */
  private async generateToken(
    user: User,
    permissions: string[],
  ): Promise<string> {
    const payload: JwtPayload = {
      sub: user.id,
      typeToken: TokenType.ACCESS,
      username: user.email,
      id: user.id,
      isAdmin: false,
      permissions: permissions,
    };

    const { token } = this.jwtService.generateUserAccessToken(payload);
    return token;
  }

  /**
   * Tạo JWT refresh token thông thường
   * @param user Thông tin người dùng
   * @param permissions Quyền của người dùng
   * @returns JWT token và thời gian hết hạn
   */
  private async generateRefreshToken(
    user: User,
    permissions: string[],
  ): Promise<{ token: string; expiresInSeconds: number }> {
    const payload: JwtPayload = {
      sub: user.id,
      username: user.email,
      id: user.id,
      isAdmin: false,
      permissions: permissions,
    };

    return this.jwtService.generateRefreshToken(payload);
  }

  /**
   * Tạo JWT refresh token với thời gian sống dài hơn cho chức năng rememberMe
   * @param user Thông tin người dùng
   * @param permissions Quyền của người dùng
   * @returns JWT token và thời gian hết hạn
   */
  private async generateLongLivedRefreshToken(
    user: User,
    permissions: string[],
  ): Promise<{ token: string; expiresInSeconds: number }> {
    const payload: JwtPayload = {
      sub: user.id,
      username: user.email,
      id: user.id,
      isAdmin: false,
      permissions: permissions,
    };

    // Sử dụng thời gian hết hạn dài hơn (30 ngày)
    const expiryTime = '30d';

    return this.jwtService.generateCustomToken(
      payload,
      TokenType.REFRESH,
      expiryTime,
    );
  }

  /**
   * Mã hóa mật khẩu
   * @param password Mật khẩu cần mã hóa
   * @returns Mật khẩu đã mã hóa
   */
  private async hashPassword(password: string): Promise<string> {
    const salt = await bcrypt.genSalt();
    return bcrypt.hash(password, salt);
  }

  /**
   * So sánh mật khẩu
   * @param plainPassword Mật khẩu gốc
   * @param hashedPassword Mật khẩu đã mã hóa
   * @returns true nếu mật khẩu khớp, ngược lại false
   */
  private async comparePasswords(
    plainPassword: string,
    hashedPassword: string,
  ): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  /**
   * Phương thức này đã được thay thế bằng logic gửi OTP qua email
   * @deprecated Sử dụng logic gửi OTP qua email thay thế
   */
  private getOptionVerify(user: User): { platform: string; value: string }[] {
    const response: { platform: string; value: string }[] = [];
    if (user.phoneNumber) {
      response.push({ platform: 'SMS', value: this.maskPhoneNumber(user.phoneNumber) });
    }
    if (user.email) {
      response.push({ platform: 'EMAIL', value: this.maskEmail(user.email) });
    }
    return response;
  }

  /**
   * Tạo mã OTP 6 số
   * @returns Mã OTP 6 số
   */
  private generateOTP(): string {
    // Tạo OTP 6 số ngẫu nhiên
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Chọn phương thức xác thực hai lớp
   * @param selectTwoFactorAuthMethodDto DTO chứa thông tin phương thức xác thực
   * @returns Thông tin về OTP đã gửi
   */
  async selectTwoFactorAuthMethod(
    selectTwoFactorAuthMethodDto: SelectTwoFactorAuthMethodDto,
  ): Promise<ApiResponseDto<SelectTwoFactorAuthMethodResponseDto>> {
    try {
      // Xác thực token
      const payload = this.jwtService.verifyToken(
        selectTwoFactorAuthMethodDto.token,
        TokenType.VERIFY,
      );

      if (!payload || !payload.sub) {
        throw new AppException(AUTH_ERROR_CODE.INVALID_TOKEN);
      }

      // Tìm người dùng
      const user = await this.userService.findOne(payload.sub);
      if (!user) {
        throw new AppException(AUTH_ERROR_CODE.USER_NOT_FOUND);
      }

      // Tìm cấu hình 2FA của người dùng
      const twoFa = await this.twoFaService.getTwoFactorAuthDetail(user.id);
      if (!twoFa) {
        throw new AppException(AUTH_ERROR_CODE.TWO_FACTOR_AUTH_NOT_FOUND);
      }

      // Kiểm tra xem phương thức xác thực có được bật không
      switch (selectTwoFactorAuthMethodDto.platform) {
        case AuthMethodEnum.EMAIL:
          if (!twoFa.otpEmailEnabled) {
            throw new AppException(
              AUTH_ERROR_CODE.TWO_FACTOR_AUTH_METHOD_NOT_ENABLED,
              'Phương thức xác thực qua email chưa được bật',
            );
          }
          return this.sendEmailOtp(user, selectTwoFactorAuthMethodDto.token);

        case AuthMethodEnum.SMS:
          if (!twoFa.otpSmsEnabled) {
            throw new AppException(
              AUTH_ERROR_CODE.TWO_FACTOR_AUTH_METHOD_NOT_ENABLED,
              'Phương thức xác thực qua SMS chưa được bật',
            );
          }
          return this.sendSmsOtp(user, selectTwoFactorAuthMethodDto.token);

        case AuthMethodEnum.GOOGLE_AUTHENTICATOR:
          if (!twoFa.googleAuthenticatorEnabled) {
            throw new AppException(
              AUTH_ERROR_CODE.TWO_FACTOR_AUTH_METHOD_NOT_ENABLED,
              'Phương thức xác thực qua Google Authenticator chưa được bật',
            );
          }
          // Không cần gửi OTP cho Google Authenticator
          return ApiResponseDto.success(
            {
              message: 'Vui lòng nhập mã từ ứng dụng Google Authenticator',
              expiresAt: Date.now() + 5 * 60 * 1000, // 5 phút
            },
            'Vui lòng nhập mã từ ứng dụng Google Authenticator',
          );

        default:
          throw new AppException(
            AUTH_ERROR_CODE.INVALID_TWO_FACTOR_AUTH_METHOD,
            'Phương thức xác thực không hợp lệ',
          );
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error in selectTwoFactorAuthMethod: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AUTH_ERROR_CODE.INTERNAL_SERVER_ERROR,
        'Lỗi khi chọn phương thức xác thực hai lớp',
      );
    }
  }

  /**
   * Gửi OTP qua email
   * @param user Thông tin người dùng
   * @param token Token xác thực
   * @returns Thông tin về OTP đã gửi
   */
  private async sendEmailOtp(
    user: User,
    token: string,
  ): Promise<ApiResponseDto<SelectTwoFactorAuthMethodResponseDto>> {
    // Tạo OTP 6 số
    const otp = this.generateOTP();

    // Lưu thông tin OTP vào Redis
    const twoFaData = {
      userId: user.id,
      email: user.email,
      otp,
      platform: AuthMethodEnum.EMAIL,
      createdAt: Date.now(),
    };

    const redisKey = `${this.twoFaPrefix}${token}`;
    const expiryTime = 5 * 60; // 5 phút

    try {
      await this.redisService.setWithExpiry(
        redisKey,
        JSON.stringify(twoFaData),
        expiryTime,
      );
      this.logger.debug(`2FA data stored in Redis with key: ${redisKey}`);
    } catch (error) {
      this.logger.error(
        `Failed to store 2FA data in Redis: ${error.message}`,
        error,
      );
      throw new AppException(
        AUTH_ERROR_CODE.INTERNAL_SERVER_ERROR,
        'Không thể lưu thông tin xác thực',
      );
    }

    // Gửi email chứa OTP
    try {
      await this.sendWithTemplateService.sendTwoFactorAuthEmail(
        user.email,
        user.id,
        otp,
      );
      this.logger.debug(`Sent 2FA email with OTP to ${user.email}`);
    } catch (error) {
      this.logger.error(
        `Failed to send 2FA email to ${user.email}: ${error.message}`,
        error,
      );
      throw new AppException(
        AUTH_ERROR_CODE.EMAIL_SENDING_FAILED,
        'Không thể gửi email xác thực',
      );
    }

    // Tính toán thời điểm hết hạn
    const expiresAt = Date.now() + expiryTime * 1000;

    // Che một phần email
    const maskedEmail = this.maskEmail(user.email);

    return ApiResponseDto.success(
      {
        message: `Đã gửi mã OTP đến email ${maskedEmail}`,
        expiresAt,
      },
      `Đã gửi mã OTP đến email ${maskedEmail}`,
    );
  }

  /**
   * Gửi OTP qua SMS
   * @param user Thông tin người dùng
   * @param token Token xác thực
   * @returns Thông tin về OTP đã gửi
   */
  private async sendSmsOtp(
    user: User,
    token: string,
  ): Promise<ApiResponseDto<SelectTwoFactorAuthMethodResponseDto>> {
    // Tạo OTP 6 số
    const otp = this.generateOTP();

    // Lưu thông tin OTP vào Redis
    const twoFaData = {
      userId: user.id,
      phoneNumber: user.phoneNumber,
      otp,
      platform: AuthMethodEnum.SMS,
      createdAt: Date.now(),
    };

    const redisKey = `${this.twoFaPrefix}${token}`;
    const expiryTime = 5 * 60; // 5 phút

    try {
      await this.redisService.setWithExpiry(
        redisKey,
        JSON.stringify(twoFaData),
        expiryTime,
      );
      this.logger.debug(`2FA data stored in Redis with key: ${redisKey}`);
    } catch (error) {
      this.logger.error(
        `Failed to store 2FA data in Redis: ${error.message}`,
        error,
      );
      throw new AppException(
        AUTH_ERROR_CODE.INTERNAL_SERVER_ERROR,
        'Không thể lưu thông tin xác thực',
      );
    }

    // TODO: Gửi SMS chứa OTP - Phần này sẽ được triển khai sau
    // Hiện tại chỉ log ra console
    this.logger.log(`[MOCK] Sending SMS with OTP ${otp} to ${user.phoneNumber}`);

    // Tính toán thời điểm hết hạn
    const expiresAt = Date.now() + expiryTime * 1000;

    // Che một phần số điện thoại
    const maskedPhone = this.maskPhoneNumber(user.phoneNumber);

    return ApiResponseDto.success(
      {
        message: `Đã gửi mã OTP đến số điện thoại ${maskedPhone}`,
        expiresAt,
      },
      `Đã gửi mã OTP đến số điện thoại ${maskedPhone}`,
    );
  }

  /**
   * Xác thực hai lớp
   * @param verifyTwoFactorAuthDto DTO chứa thông tin xác thực
   * @returns Thông tin đăng nhập nếu xác thực thành công
   */
  async verifyTwoFactorAuth(
    verifyTwoFactorAuthDto: VerifyTwoFactorAuthDto,
  ): Promise<{
    result: ApiResponseDto<VerifyTwoFactorAuthResponseDto>;
    refreshToken: string;
  }> {
    try {
      // Xác thực token
      const payload = this.jwtService.verifyToken(
        verifyTwoFactorAuthDto.token,
        TokenType.VERIFY,
      );

      if (!payload || !payload.sub) {
        throw new AppException(AUTH_ERROR_CODE.INVALID_TOKEN);
      }

      // Tìm người dùng
      const user = await this.userService.findOne(payload.sub);
      if (!user) {
        throw new AppException(AUTH_ERROR_CODE.USER_NOT_FOUND);
      }

      // Tìm cấu hình 2FA của người dùng
      const twoFa = await this.twoFaService.getTwoFactorAuthDetail(user.id);
      if (!twoFa) {
        throw new AppException(AUTH_ERROR_CODE.TWO_FACTOR_AUTH_NOT_FOUND);
      }

      // Xác thực OTP dựa trên phương thức
      switch (verifyTwoFactorAuthDto.platform) {
        case AuthMethodEnum.EMAIL:
        case AuthMethodEnum.SMS:
          // Lấy thông tin OTP từ Redis
          const redisKey = `${this.twoFaPrefix}${verifyTwoFactorAuthDto.token}`;
          const twoFaDataStr = await this.redisService.get(redisKey);

          if (!twoFaDataStr) {
            throw new AppException(
              AUTH_ERROR_CODE.INVALID_TOKEN,
              'Token không hợp lệ hoặc đã hết hạn',
            );
          }

          const twoFaData = JSON.parse(twoFaDataStr);

          // Kiểm tra OTP
          if (twoFaData.otp !== verifyTwoFactorAuthDto.otp) {
            throw new AppException(
              AUTH_ERROR_CODE.INVALID_OTP,
              'Mã OTP không chính xác',
            );
          }

          // Kiểm tra phương thức xác thực
          if (twoFaData.platform !== verifyTwoFactorAuthDto.platform) {
            throw new AppException(
              AUTH_ERROR_CODE.INVALID_TWO_FACTOR_AUTH_METHOD,
              'Phương thức xác thực không khớp',
            );
          }

          // Xóa thông tin OTP khỏi Redis
          await this.redisService.del(redisKey);
          break;

        case AuthMethodEnum.GOOGLE_AUTHENTICATOR:
          // Xác thực mã từ Google Authenticator
          const isValid = this.twoFaService.verifyGoogleAuthenticator(
            user.id,
            verifyTwoFactorAuthDto.otp,
          );

          if (!isValid) {
            throw new AppException(
              AUTH_ERROR_CODE.INVALID_OTP,
              'Mã xác thực không chính xác',
            );
          }
          break;

        default:
          throw new AppException(
            AUTH_ERROR_CODE.INVALID_TWO_FACTOR_AUTH_METHOD,
            'Phương thức xác thực không hợp lệ',
          );
      }

      // Lấy ra permission
      const permissions =
        (await this.permissionService.getUserPermissions(user.id)) || [];

      // Tạo JWT token
      const accessToken = await this.generateToken(user, permissions);

      // Tạo refresh token
      const { token: refreshToken, expiresInSeconds } =
        await this.generateRefreshToken(user, permissions);

      // Lưu refresh token vào Redis với key là user ID
      const refreshTokenKey = `${this.refreshTokenPrefix}${user.id}`;
      try {
        await this.redisService.setWithExpiry(
          refreshTokenKey,
          refreshToken,
          expiresInSeconds,
        );
        this.logger.debug(
          `Refresh token stored in Redis for user ID: ${user.id}`,
        );
      } catch (error) {
        this.logger.error(
          `Failed to store refresh token in Redis for user ID: ${user.id}`,
          error,
        );
        // Không throw lỗi ở đây để không ảnh hưởng đến luồng đăng nhập
      }

      // Tính toán thời điểm hết hạn thực tế (timestamp)
      const expiresAt = Date.now() + expiresInSeconds * 1000;

      return {
        result: ApiResponseDto.success(
          {
            accessToken,
            expiresAt,
            user: {
              id: user.id,
              email: user.email,
              fullName: user.fullName,
              username: '',
              permissions: permissions,
              status: 'active',
            },
          },
          'Xác thực hai lớp thành công',
        ),
        refreshToken,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error in verifyTwoFactorAuth: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AUTH_ERROR_CODE.INTERNAL_SERVER_ERROR,
        'Lỗi khi xác thực hai lớp',
      );
    }
  }

  /**
   * Che một phần số điện thoại
   * @param phoneNumber Số điện thoại cần che
   * @returns Số điện thoại đã được che
   */
  private maskPhoneNumber(phoneNumber: string): string {
    if (!phoneNumber || phoneNumber.length < 6) {
      return phoneNumber;
    }

    const start = phoneNumber.substring(0, 3);
    const end = phoneNumber.substring(phoneNumber.length - 3);
    const masked = '*'.repeat(phoneNumber.length - 6);

    return `${start}${masked}${end}`;
  }

  /**
   * Che một phần email
   * @param email Email cần che
   * @returns Email đã được che
   */
  private maskEmail(email: string): string {
    if (!email || !email.includes('@')) {
      return email;
    }

    const [username, domain] = email.split('@');

    let maskedUsername = username;
    if (username.length > 3) {
      maskedUsername =
        username.substring(0, 1) +
        '*'.repeat(username.length - 2) +
        username.substring(username.length - 1);
    } else if (username.length > 1) {
      maskedUsername =
        username.substring(0, 1) + '*'.repeat(username.length - 1);
    }

    return `${maskedUsername}@${domain}`;
  }

  /**
   * Xác thực OTP và hoàn tất đăng ký
   * @param verifyOtpDto Thông tin OTP và token
   * @returns Thông tin người dùng đã đăng ký và refresh token
   */
  @Transactional()
  async verifyOtp(
    verifyOtpDto: VerifyOtpDto,
  ): Promise<{ result: any; refreshToken: string }> {
    const { otp, otpToken } = verifyOtpDto;

    // Lấy thông tin đăng ký từ Redis
    const redisKey = `${this.registrationPrefix}${otpToken}`;
    let registrationData: {
      userId: number;
      email: string;
      phoneNumber: string;
      otp: string;
      createdAt: number;
    };

    try {
      const data = await this.redisService.get(redisKey);
      if (!data) {
        throw new AppException(AUTH_ERROR_CODE.INVALID_TOKEN);
      }

      registrationData = JSON.parse(data);
    } catch (error) {
      this.logger.error(
        `Failed to get registration data from Redis: ${error.message}`,
        error,
      );
      throw new AppException(AUTH_ERROR_CODE.INVALID_TOKEN);
    }

    // Kiểm tra OTP
    if (registrationData.otp !== otp) {
      throw new AppException(AUTH_ERROR_CODE.VERIFICATION_CODE_INVALID);
    }

    // Không cần mã hóa mật khẩu vì đã mã hóa khi tạo người dùng

    // Tìm người dùng đã tạo trước đó
    const user = await this.userService.findOne(registrationData.userId);

    // Cập nhật trạng thái xác thực - sử dụng repository trực tiếp vì UserService chưa có phương thức update
    user.isVerifyEmail = true; // Đã xác thực email qua OTP
    // user.isVerifyPhone = true; // Đã xác thực số điện thoại qua OTP - chưa có trường này trong entity
    user.updatedAt = Date.now();

    // Lưu lại thay đổi
    const updatedUser = await this.userRepository.save(user);

    // Tạo quyền cho người dùng - xử lý trường hợp người dùng đã có vai trò USER
    try {
      await this.userRoleService.addUserRole(updatedUser.id);
    } catch (error) {
      // Bỏ qua lỗi nếu người dùng đã có vai trò USER
      if (
        error.message &&
        error.message.includes('Người dùng đã có vai trò USER')
      ) {
        this.logger.log(
          `User ${updatedUser.id} already has USER role, skipping role assignment`,
        );
      } else {
        // Nếu là lỗi khác, ném lại lỗi
        throw error;
      }
    }

    // Xóa thông tin đăng ký khỏi Redis
    try {
      await this.redisService.del(redisKey);
    } catch (error) {
      this.logger.error(
        `Failed to delete registration data from Redis: ${error.message}`,
        error,
      );
      // Không throw lỗi ở đây vì người dùng đã được tạo thành công
    }

    // Lấy ra permission
    const permissions =
      (await this.permissionService.getUserPermissions(updatedUser.id)) || [];

    // Tạo JWT token
    const accessToken = await this.generateToken(updatedUser, permissions);
    const { token: refreshToken, expiresInSeconds } =
      await this.generateRefreshToken(updatedUser, permissions);

    // Lưu refresh token vào Redis với key là user ID
    const refreshTokenKey = `${this.refreshTokenPrefix}${updatedUser.id}`;
    try {
      await this.redisService.setWithExpiry(
        refreshTokenKey,
        refreshToken,
        expiresInSeconds,
      );
      this.logger.debug(
        `Refresh token stored in Redis for user ID: ${updatedUser.id}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to store refresh token in Redis for user ID: ${updatedUser.id}`,
        error,
      );
      // Không throw lỗi ở đây để không ảnh hưởng đến luồng đăng ký
    }

    const result = {
      code: 200,
      message: 'Đăng ký thành công',
      result: {
        accessToken,
        expiresIn: expiresInSeconds,
        info: [],
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          fullName: updatedUser.fullName,
          username: '',
          permissions: permissions,
          status: 'active',
        },
      },
    };

    return { result, refreshToken };
  }

  @Transactional()
  async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<any> {
    // Tìm user theo email
    const user = await this.userService.findByEmail(forgotPasswordDto.email);
    if (!user) {
      throw new AppException(AUTH_ERROR_CODE.USER_NOT_FOUND);
    }

    // Tạo OTP 6 số
    const otp = this.generateOTP();

    // Tạo token OTP
    const { token: otpToken, expiresInSeconds } =
      this.jwtService.generateUserForgotPasswordToken({
        sub: user.id,
        username: user.email,
      });

    // Lưu thông tin OTP vào Redis
    const verificationData = {
      userId: user.id,
      email: user.email,
      otp,
      createdAt: Date.now(),
    };

    const redisKey = `${this.forgotPasswordPrefix}${otpToken}`;
    await this.redisService.setWithExpiry(
      redisKey,
      JSON.stringify(verificationData),
      expiresInSeconds,
    );

    // Gửi email chứa OTP
    await this.sendWithTemplateService.sendAccountForgotPassword(
      user.email,
      user.id,
      otp,
    );

    // Che một phần email
    const maskedEmail = this.maskEmail(user.email);

    // Tính toán thời điểm hết hạn thực tế (timestamp)
    const expiresAt = Date.now() + expiresInSeconds * 1000;

    const result = {
      otpToken,
      expiresAt,
      maskedEmail,
    };

    // Trong môi trường phát triển, trả về cả OTP
    if (this.configService.get<string>('NODE_ENV') !== 'production') {
      result['otp'] = otp;
    }

    return {
      code: 200,
      message: 'Mã OTP đã được gửi đến email của bạn',
      result,
    };
  }

  @Transactional()
  async verifyForgotPassword(
    verifyForgotPasswordDto: VerifyForgotPasswordDto,
  ): Promise<any> {
    const { otp, otpToken } = verifyForgotPasswordDto;

    // Lấy thông tin từ Redis
    const redisKey = `${this.forgotPasswordPrefix}${otpToken}`;
    const verificationDataStr = await this.redisService.get(redisKey);

    if (!verificationDataStr) {
      throw new AppException(AUTH_ERROR_CODE.VERIFICATION_CODE_INVALID);
    }

    const verificationData = JSON.parse(verificationDataStr);

    // Kiểm tra OTP
    if (verificationData.otp !== otp) {
      throw new AppException(AUTH_ERROR_CODE.VERIFICATION_CODE_INVALID);
    }

    // Tạo token đổi mật khẩu
    const { token: changePasswordToken, expiresInSeconds } =
      this.jwtService.generateUserChangePasswordToken({
        sub: verificationData.userId,
        username: verificationData.email,
      });

    // Lưu thông tin token vào Redis
    const changePasswordKey = `${this.changePasswordPrefix}${changePasswordToken}`;
    await this.redisService.setWithExpiry(
      changePasswordKey,
      JSON.stringify({ userId: verificationData.userId }),
      expiresInSeconds,
    );

    // Xóa thông tin OTP cũ
    await this.redisService.del(redisKey);

    // Tính toán thời điểm hết hạn thực tế (timestamp)
    const expiresAt = Date.now() + expiresInSeconds * 1000;

    return {
      code: 200,
      message: 'Xác thực OTP thành công',
      result: {
        changePasswordToken,
        expiresAt,
      },
    };
  }

  @Transactional()
  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<any> {
    const { newPassword, changePasswordToken } = resetPasswordDto;

    // Lấy thông tin từ Redis
    const redisKey = `${this.changePasswordPrefix}${changePasswordToken}`;
    const changePasswordDataStr = await this.redisService.get(redisKey);

    if (!changePasswordDataStr) {
      throw new AppException(AUTH_ERROR_CODE.PASSWORD_RESET_CODE_INVALID);
    }

    const changePasswordData = JSON.parse(changePasswordDataStr);

    // Đổi mật khẩu
    await this.passwordService.setPassword(
      changePasswordData.userId,
      newPassword,
    );

    // Xóa token đổi mật khẩu
    await this.redisService.del(redisKey);

    return {
      code: 200,
      message: 'Đổi mật khẩu thành công',
    };
  }

  /**
   * Gửi lại mã OTP cho việc xác thực email khi đăng ký
   * @param resendOtpDto Thông tin token OTP cũ
   * @returns Thông tin token OTP mới và thời gian hết hạn
   */
  @Transactional()
  async resendOtp(
    resendOtpDto: ResendOtpDto,
  ): Promise<{ code: number; message: string; result: any }> {
    const { otpToken } = resendOtpDto;

    // Lấy thông tin đăng ký từ Redis
    const redisKey = `${this.registrationPrefix}${otpToken}`;
    let registrationData: {
      userId: number;
      email: string;
      phoneNumber: string;
      otp: string;
      createdAt: number;
    };

    try {
      const data = await this.redisService.get(redisKey);
      if (!data) {
        throw new AppException(
          AUTH_ERROR_CODE.INVALID_TOKEN,
          'Token OTP không hợp lệ hoặc đã hết hạn',
        );
      }

      registrationData = JSON.parse(data);
    } catch (error) {
      this.logger.error(
        `Failed to get registration data from Redis: ${error.message}`,
        error,
      );
      throw new AppException(
        AUTH_ERROR_CODE.INVALID_TOKEN,
        'Token OTP không hợp lệ hoặc đã hết hạn',
      );
    }

    // Xóa thông tin đăng ký cũ khỏi Redis
    try {
      await this.redisService.del(redisKey);
    } catch (error) {
      this.logger.error(
        `Failed to delete old registration data from Redis: ${error.message}`,
        error,
      );
      // Không throw lỗi ở đây vì chúng ta vẫn có thể tiếp tục tạo OTP mới
    }

    // Tạo OTP mới 6 số
    const newOtp = this.generateOTP();

    // Tạo token OTP mới
    const { token: newOtpToken, expiresInSeconds } =
      this.jwtService.generateUserVerifyToken({
        sub: registrationData.userId,
        username: registrationData.email,
        permissions: [],
      });

    // Lưu thông tin OTP mới vào Redis
    const newVerificationData = {
      userId: registrationData.userId,
      email: registrationData.email,
      phoneNumber: registrationData.phoneNumber,
      otp: newOtp,
      createdAt: Date.now(),
    };

    const newRedisKey = `${this.registrationPrefix}${newOtpToken}`;
    try {
      await this.redisService.setWithExpiry(
        newRedisKey,
        JSON.stringify(newVerificationData),
        expiresInSeconds,
      );
      this.logger.debug(
        `New verification data stored in Redis with key: ${newRedisKey}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to store new verification data in Redis: ${error.message}`,
        error,
      );
      throw new AppException(
        AUTH_ERROR_CODE.REGISTRATION_FAILED,
        'Không thể lưu thông tin xác thực mới',
      );
    }

    // Gửi email chứa OTP mới cho người dùng
    try {
      // Lấy thông tin user để có tên
      const user = await this.userService.findOne(registrationData.userId);
      await this.emailPlaceholderService.sendEmailVerification({
        EMAIL: registrationData.email,
        USER_ID: registrationData.userId.toString(),
        NAME: user?.fullName || registrationData.email,
        TWO_FA_CODE: newOtp,
      });
      this.logger.debug(
        `Sent verification email with new OTP to ${registrationData.email}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send verification email to ${registrationData.email}: ${error.message}`,
        error,
      );
      // Không throw lỗi ở đây để không ảnh hưởng đến luồng gửi lại OTP
    }

    // Che một phần email
    const maskedEmail = this.maskEmail(registrationData.email);

    // Tính toán thời điểm hết hạn thực tế (timestamp)
    const expiresAt = Date.now() + expiresInSeconds * 1000;

    // Trả về thông tin OTP mới và token
    const result = {
      otpToken: newOtpToken,
      expiresAt,
      maskedEmail,
    };

    // Trong môi trường phát triển, trả về cả OTP để tiện test
    if (this.configService.get<string>('NODE_ENV') !== 'production') {
      result['otp'] = newOtp;
    }

    return {
      code: 200,
      message: 'Mã OTP mới đã được gửi đến email',
      result,
    };
  }

  /**
   * Đăng xuất người dùng
   * @param userId ID của người dùng
   * @returns Thông báo đăng xuất thành công
   */
  async logout(userId: number): Promise<ApiResponseDto<null>> {
    try {
      // Xóa refresh token khỏi Redis
      const redisKey = `${this.refreshTokenPrefix}${userId}`;
      await this.redisService.del(redisKey);

      // Xóa thông tin rememberMe khỏi Redis
      const rememberMeKey = `${this.rememberMePrefix}${userId}`;
      await this.redisService.del(rememberMeKey);

      this.logger.debug(`Logged out user ID: ${userId}`);

      return {
        code: 200,
        message: 'Đăng xuất thành công',
        result: null,
      };
    } catch (error) {
      this.logger.error(`Failed to logout user ID: ${userId}`, error);
      // Vẫn trả về thành công cho người dùng ngay cả khi có lỗi xảy ra với Redis
      return {
        code: 200,
        message: 'Đăng xuất thành công',
        result: null,
      };
    }
  }

  /**
   * Lấy thông tin đăng nhập từ cookie rememberMe
   * @param rememberMeToken Token rememberMe từ cookie
   * @returns Thông tin đăng nhập
   */
  async getRememberMe(
    rememberMeToken: string,
  ): Promise<{
    accessToken: string;
    expiresIn?: number;
    expiresAt: number;
    user: any;
  }> {
    try {
      // Xác thực token
      const payload = this.jwtService.verifyToken(
        rememberMeToken,
        TokenType.REFRESH,
      );

      if (!payload || !payload.sub) {
        throw new AppException(AUTH_ERROR_CODE.INVALID_TOKEN);
      }

      // Kiểm tra xem có thông tin rememberMe trong Redis không
      const rememberMeKey = `${this.rememberMePrefix}${payload.sub}`;
      const rememberMeData = await this.redisService.get(rememberMeKey);

      if (!rememberMeData) {
        throw new AppException(AUTH_ERROR_CODE.INVALID_TOKEN);
      }

      // Parse dữ liệu rememberMe
      JSON.parse(rememberMeData); // Chỉ kiểm tra xem dữ liệu có thể parse được không

      // Tìm người dùng
      const user = await this.userService.findOne(payload.sub);

      if (!user) {
        throw new AppException(AUTH_ERROR_CODE.USER_NOT_FOUND);
      }

      if (!user.isActive) {
        throw new AppException(AUTH_ERROR_CODE.ACCOUNT_LOCKED);
      }

      // Lấy ra permission
      const permissions =
        (await this.permissionService.getUserPermissions(user.id)) || [];

      // Tạo access token mới
      const accessToken = await this.generateToken(user, permissions);

      // Lấy thời gian hết hạn từ cấu hình JWT
      const { expiresInSeconds } = this.jwtService.generateUserAccessToken({
        sub: user.id,
        username: user.email,
        permissions: permissions,
      });

      // Tính toán thời điểm hết hạn thực tế (timestamp)
      const expiresAt = Date.now() + expiresInSeconds * 1000;

      return {
        accessToken,
        expiresAt,
        expiresIn: expiresInSeconds, // Giữ lại để tương thích ngược
        user: {
          id: user.id,
          email: user.email,
          fullName: user.fullName,
        },
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Failed to get rememberMe info: ${error.message}`,
        error,
      );
      throw new AppException(AUTH_ERROR_CODE.INVALID_TOKEN);
    }
  }

  /**
   * Làm mới token
   * @param refreshToken Refresh token
   * @returns Access token mới và thời gian hết hạn
   */
  async refreshToken(
    refreshToken: string,
  ): Promise<{ accessToken: string; expiresIn?: number; expiresAt: number }> {
    try {
      // Xác thực refresh token
      const payload = this.jwtService.verifyToken(
        refreshToken,
        TokenType.REFRESH,
      );

      if (!payload || !payload.sub) {
        throw new AppException(AUTH_ERROR_CODE.INVALID_TOKEN);
      }

      // Kiểm tra xem refresh token có trong Redis không
      const redisKey = `${this.refreshTokenPrefix}${payload.sub}`;
      const storedToken = await this.redisService.get(redisKey);

      if (!storedToken || storedToken !== refreshToken) {
        throw new AppException(AUTH_ERROR_CODE.INVALID_TOKEN);
      }

      // Tìm người dùng
      const user = await this.userService.findOne(payload.sub);

      if (!user) {
        throw new AppException(AUTH_ERROR_CODE.USER_NOT_FOUND);
      }

      if (!user.isActive) {
        throw new AppException(AUTH_ERROR_CODE.ACCOUNT_LOCKED);
      }

      // Lấy ra permission
      const permissions =
        (await this.permissionService.getUserPermissions(user.id)) || [];

      // Tạo access token mới
      const accessToken = await this.generateToken(user, permissions);

      // Lấy thời gian hết hạn từ cấu hình JWT
      const { expiresInSeconds } = this.jwtService.generateUserAccessToken({
        sub: user.id,
        username: user.email,
        permissions: permissions,
      });

      // Tính toán thời điểm hết hạn thực tế (timestamp)
      const expiresAt = Date.now() + expiresInSeconds * 1000;

      return {
        accessToken,
        expiresAt,
        expiresIn: expiresInSeconds, // Giữ lại để tương thích ngược
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Failed to refresh token: ${error.message}`, error);
      throw new AppException(AUTH_ERROR_CODE.INVALID_TOKEN);
    }
  }

  /**
   * Tạo URL xác thực Google OAuth2
   * @returns URL xác thực Google
   */
  generateGoogleAuthUrl(redirectUri?: string): string {
    return this.oauth2Service.generateGoogleAuthUrl(redirectUri);
  }

  /**
   * Tạo URL xác thực Facebook OAuth2
   * @returns Object chứa URL, scope và state
   */
  generateFacebookAuthUrl(redirectUri?: string): { url: string; scope: string; state: string } {
    return this.oauth2Service.generateFacebookAuthUrl(redirectUri);
  }

  /**
   * Tạo URL xác thực Zalo OAuth2
   * @returns URL xác thực Zalo
   */
  generateZaloAuthUrl(redirectUri?: string): string {
    return this.oauth2Service.generateZaloAuthUrl(redirectUri);
  }

  /**
   * Xử lý đăng nhập Google OAuth2
   * @param googleAuthDto Thông tin xác thực Google
   * @param requestInfo Thông tin yêu cầu (IP, User Agent, Fingerprint)
   * @returns Thông tin người dùng và token
   */
  @Transactional()
  async loginWithGoogle(
    googleAuthDto: GoogleAuthDto,
    requestInfo?: {
      ipAddress?: string;
      userAgent?: string;
      fingerprint?: string;
    },
  ): Promise<{ result: ApiResponseDto<LoginResponse>; refreshToken: string }> {
    try {
      // Xử lý đăng nhập Google
      const user = await this.oauth2Service.handleGoogleLogin(
        googleAuthDto.code,
        googleAuthDto.redirectUri,
        googleAuthDto.ref,
      );

      // Lấy ra permission
      const permissions =
        (await this.permissionService.getUserPermissions(user.id)) || [];

      // Tạo JWT token
      const accessToken = await this.generateToken(user, permissions);
      const { token: refreshToken, expiresInSeconds } =
        await this.generateRefreshToken(user, permissions);

      // Lưu refresh token vào Redis với key là user ID
      const redisKey = `${this.refreshTokenPrefix}${user.id}`;
      try {
        await this.redisService.setWithExpiry(
          redisKey,
          refreshToken,
          expiresInSeconds,
        );
        this.logger.debug(
          `Refresh token stored in Redis for user ID: ${user.id}`,
        );
      } catch (error) {
        this.logger.error(
          `Failed to store refresh token in Redis for user ID: ${user.id}`,
          error,
        );
        // Không throw lỗi ở đây để không ảnh hưởng đến luồng đăng nhập
      }

      // Tạo log xác thực thành công
      if (requestInfo) {
        try {
          // Tạo log xác thực
          await this.authVerificationLogRepository.createLoginSuccessLog(
            user.id,
            AuthMethodEnum.GOOGLE_AUTHENTICATOR,
            requestInfo.ipAddress,
            requestInfo.userAgent,
          );

          // Tạo hoặc cập nhật thông tin thiết bị
          if (requestInfo.fingerprint) {
            // Parse user agent để lấy thông tin trình duyệt
            const parser = new UAParser();
            parser.setUA(requestInfo.userAgent || '');
            const browserInfo = parser.getResult();

            await this.deviceInfoRepository.findOrCreate(
              requestInfo.fingerprint,
              user.id,
              requestInfo.ipAddress,
              requestInfo.userAgent,
              {
                browser: browserInfo.browser.name,
                os: browserInfo.os.name,
              },
            );
          }
        } catch (error) {
          // Log lỗi nhưng không ảnh hưởng đến luồng đăng nhập
          this.logger.error(
            `Failed to create login logs: ${error.message}`,
            error.stack,
          );
        }
      }

      // Tính toán thời điểm hết hạn thực tế (timestamp)
      const expiresAt = Date.now() + expiresInSeconds * 1000;

      const result: ApiResponseDto<LoginResponse> = {
        code: 200,
        message: 'Đăng nhập Google thành công',
        result: {
          accessToken,
          expiresAt,
          info: [],
          user: {
            id: user.id,
            email: user.email,
            username: '',
            permissions: permissions,
            status: 'active',
          },
        },
      };

      return { result, refreshToken };
    } catch (error) {
      this.logger.error(`Google login failed: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
        'Đăng nhập Google thất bại',
      );
    }
  }

  /**
   * Xử lý đăng nhập Facebook OAuth2
   * @param facebookAuthDto Thông tin xác thực Facebook
   * @param requestInfo Thông tin yêu cầu (IP, User Agent, Fingerprint)
   * @returns Thông tin người dùng và token
   */
  @Transactional()
  async loginWithFacebook(
    facebookAuthDto: FacebookAuthDto,
    requestInfo?: {
      ipAddress?: string;
      userAgent?: string;
      fingerprint?: string;
    },
  ): Promise<{ result: ApiResponseDto<LoginResponse>; refreshToken: string }> {
    try {
      // Xử lý đăng nhập Facebook
      const user = await this.oauth2Service.handleFacebookLogin(
        facebookAuthDto.code,
        facebookAuthDto.redirectUri,
        facebookAuthDto.ref,
      );

      // Lấy ra permission
      const permissions =
        (await this.permissionService.getUserPermissions(user.id)) || [];

      // Tạo JWT token
      const accessToken = await this.generateToken(user, permissions);
      const { token: refreshToken, expiresInSeconds } =
        await this.generateRefreshToken(user, permissions);

      // Lưu refresh token vào Redis với key là user ID
      const redisKey = `${this.refreshTokenPrefix}${user.id}`;
      try {
        await this.redisService.setWithExpiry(
          redisKey,
          refreshToken,
          expiresInSeconds,
        );
        this.logger.debug(
          `Refresh token stored in Redis for user ID: ${user.id}`,
        );
      } catch (error) {
        this.logger.error(
          `Failed to store refresh token in Redis for user ID: ${user.id}`,
          error,
        );
        // Không throw lỗi ở đây để không ảnh hưởng đến luồng đăng nhập
      }

      // Tạo log xác thực thành công
      if (requestInfo) {
        try {
          // Tạo log xác thực
          await this.authVerificationLogRepository.createLoginSuccessLog(
            user.id,
            AuthMethodEnum.EMAIL, // Sử dụng EMAIL vì không có enum riêng cho Facebook
            requestInfo.ipAddress,
            requestInfo.userAgent,
          );

          // Tạo hoặc cập nhật thông tin thiết bị
          if (requestInfo.fingerprint) {
            // Parse user agent để lấy thông tin trình duyệt
            const parser = new UAParser();
            parser.setUA(requestInfo.userAgent || '');
            const browserInfo = parser.getResult();

            await this.deviceInfoRepository.findOrCreate(
              requestInfo.fingerprint,
              user.id,
              requestInfo.ipAddress,
              requestInfo.userAgent,
              {
                browser: browserInfo.browser.name,
                os: browserInfo.os.name,
              },
            );
          }
        } catch (error) {
          // Log lỗi nhưng không ảnh hưởng đến luồng đăng nhập
          this.logger.error(
            `Failed to create login logs: ${error.message}`,
            error.stack,
          );
        }
      }

      // Tính toán thời điểm hết hạn thực tế (timestamp)
      const expiresAt = Date.now() + expiresInSeconds * 1000;

      const result: ApiResponseDto<LoginResponse> = {
        code: 200,
        message: 'Đăng nhập Facebook thành công',
        result: {
          accessToken,
          expiresAt,
          info: [],
          user: {
            id: user.id,
            email: user.email,
            username: '',
            permissions: permissions,
            status: 'active',
          },
        },
      };

      return { result, refreshToken };
    } catch (error) {
      this.logger.error(`Facebook login failed: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
        'Đăng nhập Facebook thất bại',
      );
    }
  }

  /**
   * Xử lý đăng nhập Zalo OAuth2
   * @param zaloAuthDto Thông tin xác thực Zalo
   * @param requestInfo Thông tin yêu cầu (IP, User Agent, Fingerprint)
   * @returns Thông tin người dùng và token
   */
  @Transactional()
  async loginWithZalo(
    zaloAuthDto: ZaloAuthDto,
    requestInfo?: {
      ipAddress?: string;
      userAgent?: string;
      fingerprint?: string;
    },
  ): Promise<{ result: ApiResponseDto<LoginResponse>; refreshToken: string }> {
    try {
      // Xử lý đăng nhập Zalo
      const user = await this.oauth2Service.handleZaloLogin(
        zaloAuthDto.code,
        zaloAuthDto.redirectUri,
        zaloAuthDto.ref,
        zaloAuthDto.state,
      );

      // Lấy ra permission
      const permissions =
        (await this.permissionService.getUserPermissions(user.id)) || [];

      // Tạo JWT token
      const accessToken = await this.generateToken(user, permissions);
      const { token: refreshToken, expiresInSeconds } =
        await this.generateRefreshToken(user, permissions);

      // Lưu thông tin đăng nhập (có thể implement sau nếu cần)
      // if (requestInfo) {
      //   await this.saveLoginInfo(user.id, requestInfo);
      // }

      // Tính toán thời điểm hết hạn thực tế (timestamp)
      const expiresAt = Date.now() + expiresInSeconds * 1000;

      const result = ApiResponseDto.success(
        {
          accessToken,
          expiresIn: expiresInSeconds,
          expiresAt,
          info: [],
          user: {
            id: user.id,
            email: user.email || '',
            username: '',
            permissions: permissions,
            status: user.isActive ? 'active' : 'inactive',
          },
        },
        'Đăng nhập Zalo thành công',
      );

      return { result, refreshToken };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Zalo login failed: ${error.message}`, error.stack);
      throw new AppException(
        AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
        'Đăng nhập Zalo thất bại',
      );
    }
  }
}
